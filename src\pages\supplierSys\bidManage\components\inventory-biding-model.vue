<!-- 清单竞价 -->
<template>
    <div>
        <el-dialog v-loading="bidingFormLoading" v-dialogDrag id="inventoryBidingDialog" title="生成清单竞价"
            :visible.sync="inventoryBidFormVisible" width="90%" style="margin-left: 10%" :close-on-click-modal="false">
            <el-divider content-position="left">竞价信息</el-divider>
            <el-form :model="inventoryBidForm" :rules="bidingFormRules" label-width="200px" ref="inventoryBidingFormRef"
                :disabled="false" class="demo-ruleForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品类型：" prop="productType">
                            <el-radio-group v-has-permi="{ platform: 'oneselfShopManage', auth: 'created-lxBid', }"
                                v-model="inventoryBidForm.productType">
                                <el-radio :label="1">大宗临购清单</el-radio>
                                <el-radio :label="2">周转材料清单</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-show="inventoryBidForm.productType === 1 ||
                        inventoryBidForm.productType === 2
                        ">
                        <el-form-item label="价格类型：" prop="billType">
                            <el-radio-group v-has-permi="{ platform: 'oneselfShopManage', auth: 'created-lxBid', }"
                                v-model="inventoryBidForm.billType">
                                <el-radio :label="1">浮动价格</el-radio>
                                <el-radio :label="2">固定价格</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="标题：" prop="title">
                            <el-input maxlength="200" placeholder="请输入标题" clearable
                                v-model="inventoryBidForm.title"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="截止时间：" prop="endTime">
                            <el-date-picker style="width: 100%;" v-model="inventoryBidForm.endTime" type="datetime"
                                placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系人：" prop="linkName">
                            <el-input maxlength="10" placeholder="请输入联系人" clearable
                                v-model="inventoryBidForm.linkName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话：" prop="linkPhone">
                            <el-input type="number" clearable v-model="inventoryBidForm.linkPhone"
                                placeholder="请输入11位手机号码" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="竞价类型：" prop="type">
                            <el-radio-group v-model="inventoryBidForm.type">
                                <el-radio :label="1">公开竞价</el-radio>
                                <span> <el-radio :label="2">邀请竞价</el-radio></span>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="inventoryBidForm.type === 2">
                        <el-form-item label="选择供应商：" prop="">
                            <el-button class="btn10" type="primary" @click="showSupplierDialog">选择供应商</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="说明：" prop="biddingExplain">
                            <editor v-model="inventoryBidForm.biddingExplain"></editor>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="24">
                        <el-form-item label="竞价函说明：" prop="biddingNotice">
                            <el-input type="textarea" :rows="6" v-model="inventoryBidForm.biddingNotice"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-divider content-position="left">清单商品</el-divider>
            <div class="e-table" style="background-color: #fff" v-loading="biddingOrderItemLoading">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <div class="search_box" style="margin-left: 10px">
                            <el-button class="btn10" type="primary" @click="selectInventoryProduct">选择清单商品</el-button>
                        </div>
                    </div>
                </div>
                <el-table ref="bidingOrderItemRef" border style="width: 100%"
                    :data="inventoryBidForm.synthesizeTemporaryDtlList" class="table" max-height="450px">
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" label-width="">
                        <template v-slot="scope">
                            <span class="action" @click="deleteInventoryRow(scope.row)">删除</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品编号" width="230" prop="productSn"></el-table-column>
                    <el-table-column prop="productImg" label="商品图片" width="130">
                        <template v-slot="scope">
                            <el-image style="width: 90px; height: 60px"
                                :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="productName" label="商品名称" width="、"></el-table-column>
                    <el-table-column prop="texture" label="规格" width="200"></el-table-column>
                    <el-table-column prop="texture" label="材质" width="200"></el-table-column>
                    <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                    <el-table-column prop="qty" label="数量" width="100"></el-table-column>
                    <el-table-column v-if="inventoryBidForm.billType === 1" prop="bidNetPrice" label="网价" width="180">
                        <template v-slot="scope">
                            <el-input-number :controls="false" :precision="2"
                                v-model="scope.row.bidNetPrice"></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column prop="maxPrice" label="最高单价" width="180">
                        <template v-slot="scope">
                            <el-input-number :controls="false" :precision="2"
                                v-model="scope.row.maxPrice"></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <el-button class="btn-blue btn10" @click="createInventoryBid" style="margin-right: 40px">生成清单竞价</el-button>
                <el-button class="btn10" @click="inventoryBidFormVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <!--  清单选择      -->
        <el-dialog v-dialogDrag custom-class="dlg" width="90%" title="清单商品选择" :visible.sync="showInventoryList">
            <div class="box-left">
                <div class="e-table">
                    <div class="top">
                        <div style="width: 100%">
                            <el-input type="text" @blur="getInventoryList" v-model="inventoryKey" placeholder="输入清单编号">
                                <img src="@/assets/search.png" slot="suffix" @click="getInventoryList" alt="" />
                            </el-input>
                        </div>
                    </div>
                    <el-table max-height="340px" @row-click="inventoryListClick" v-loading="biddingOrderListLoading"
                        class="table" :height="rightTableHeight" :data="inventoryList" border>
                        <el-table-column label="清单编号" width="250" prop="synthesizeTemporarySn" />
                        <el-table-column label="清单类型" width="100" prop="清单类型">
                            <template v-slot="scope">
                                <el-tag v-if="scope.row.billType == 1">浮动价格</el-tag>
                                <el-tag v-if="scope.row.billType == 2">固定价格</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建时间" width="150" prop="gmtCreate">
                            <template v-slot="scope">
                                <span>{{ scope.row.gmtCreate | dateStr }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <myPagination :total="inventoryListPagination.total"
                        :current-page="inventoryListPagination.currentPage"
                        :page-size.sync="inventoryListPagination.pageSize" :page-sizes="[20, 40, 60, 80]"
                        @currentChange="getInventoryList" @sizeChange="getInventoryList" />
                </div>
            </div>
            <div class="box-right">
                <div class="e-table">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <span style="color: #ff0000; margin-left: 20px">点击选择！</span>
                    </div>
                    <el-table max-height="340px" @row-click="handleCurrentInventoryProductClick" ref="eltableCurrentRow"
                        v-loading="biddingorderItemSelectLoading" class="table" :height="rightTableHeight"
                        :data="inventoryProductList" border>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="productName" label="商品名称" width="200" />
                        <el-table-column prop="spec" label="规格" width="100" />
                        <el-table-column prop="unit" label="单位" width="80" />
                        <el-table-column prop="qty" label="数量" width="100" />
                        <el-table-column prop="classNamePath" label="分类路径" width="200" />
                    </el-table>
                </div>
            </div>
            <div class="box-right">
                <div class="e-table">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <span style="color: #ff0000">双击移除！</span>
                    </div>
                    <el-table max-height="340px" class="table" @row-dblclick="removeSelectProductList"
                        :height="rightTableHeight" :data="inventorySelectedProductList" border>
                        <el-table-column label="序号" type="index" width="50"></el-table-column>
                        <el-table-column prop="productSn" label="商品编号" width="100" />
                        <el-table-column prop="productName" label="商品名称" width="200" />
                        <el-table-column prop="spec" label="规格" width="" />
                        <el-table-column prop="unit" label="计量单位" width="100" />
                        <el-table-column prop="qty" label="数量" width="100" />
                        <el-table-column prop="classNamePath" label="分类路径" width="200" />
                    </el-table>
                </div>
            </div>
            <span slot="footer">
                <el-button type="primary" class="btn10" @click="selectInventoryProductAffirm">选择物资</el-button>
                <el-button class="btn10" @click="showInventoryList = false">取消</el-button>
            </span>
        </el-dialog>

        <!-- 供应商弹窗 -->
        <SupplierModel ref="supplierModelRef" @getSupplierList="getSupplierList" />
    </div>
</template>

<script>

//工具
import { hideLoading, showLoading } from '@/utils/common'
import { mapState } from 'vuex'

//接口
import { createInventoryBidding, } from '@/api/shopManage/biding/biding'
import { suppliersSynthesizeTemporaryGetBySn } from '@/api/frontStage/userCenter'
import { listByEntitySynthesizeTemporary, } from '@/api/platform/order/orders'

//组件
import SupplierModel from '../components/supplier-model.vue'
import editor from '@/components/quillEditor'
import myPagination from '@/components/pagination/myPagination'

export default {

    name: 'InventoryBidingModel',
    components: {
        SupplierModel,
        editor,
        myPagination
    },
    watch: {
        'bidingForm.endTime': {
            handler () {
                this.formatStr()
                this.$forceUpdate()
            },
        },
        'inventoryBidForm.endTime': {
            handler () {
                this.formatInventoryStr()
                this.$forceUpdate()
            },
        },
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            },
        },
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) return
            let newDateSr = dateStr.split(' ')
            return newDateSr[0]
        },
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return this.screenWidth - 300 + 'px'
        },
        // rightTableHeight2 () {
        //     return this.screenHeight - 210
        // },
    },
    data () {
        return {
            biddingOrderListLoading: false,
            inventoryBidFormVisible: false,
            bidingFormLoading: false,
            biddingOrderItemLoading: false,
            inventoryList: [],
            inventoryListPagination: {
                total: 0,
                currentPage: 1,
                pageSize: 20
            },
            screenHeight: 0,
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            inventoryBidForm: {
                productType: 1,
                biddingSourceType: 3,
                billType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                synthesizeTemporaryDtlList: [],
                suppliers: [],
                biddingNotice: `说明：
   1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
    2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为'{year}'年'{month}'月'{day}'日'{minutes}'分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`,
            },
            inventoryProductList: [],
            inventorySelectedProductList: [],
            inventoryKey: '',
            showInventoryList: false,
            biddingorderItemSelectLoading: false,
            bidingFormRules: {
                orderSn: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' },
                ],
                billType: [
                    { required: true, message: '请选择竞价类型', trigger: 'blur' },
                ],
                endTime: [
                    { required: true, message: '请选择截止时间', trigger: 'blur' },
                ],
                linkPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    {
                        pattern: /^1[3456789]\d{9}$/,
                        message: '请输入正确的手机号',
                        trigger: 'blur',
                    },
                ],
            },

        }
    },
    methods: {

        formatStr () {
            let year = this.bidingForm.endTime.substring(0, 4)
            let month = this.bidingForm.endTime.substring(5, 7)
            let day = this.bidingForm.endTime.substring(8, 10)
            let minutes = this.bidingForm.endTime.substring(11, 16)
            this.bidingForm.biddingNotice = `说明：
   1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
    2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`
        },
        formatInventoryStr () {
            let year = this.inventoryBidForm.endTime.substring(0, 4)
            let month = this.inventoryBidForm.endTime.substring(5, 7)
            let day = this.inventoryBidForm.endTime.substring(8, 10)
            let minutes = this.inventoryBidForm.endTime.substring(11, 16)
            // 固定模板
            if (this.inventoryBidForm.billType === 2) {
                this.inventoryBidForm.biddingNotice = `说明：
备注：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求；
4、发票开具均采用一票制。
5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，电子邮件逾期送达的视为报价无效。
7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
8、报价方申明（若有必要）：`
            } else {
                this.inventoryBidForm.biddingNotice = `说明：
备注：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
2、网价参照《我的钢铁网》“成都市场”2023年12月20日流体管对应规格型号公布的最低价格报价（如无该规格型号报价，则选取相应报价），结算以到货当日对应规格型号网价+固定费，若到货当日无网价，按照到货当日接近日期最低网价执行；“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
4、发票开具均采用一票制。
5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，电子邮件逾期送达的视为报价无效。
7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
8、报价方申明（若有必要）：`
            }
        },
        openDialog () {
            this.inventoryBidFormVisible = true
        },
        showSupplierDialog () {
            this.$refs.supplierModelRef.openModel()
        },
        // 选择清单商品
        selectInventoryProduct () {
            this.inventoryList = []
            this.inventoryProductList = []
            this.inventorySelectedProductList = []
            this.showInventoryList = true
            this.getInventoryList()
        },
        removeSelectProductList (row) {
            this.inventorySelectedProductList =
                this.inventorySelectedProductList.filter(
                    t => t.synthesizeTemporaryDtlId != row.synthesizeTemporaryDtlId
                )
        },

        deleteInventoryRow (row) {
            this.inventoryBidForm.synthesizeTemporaryDtlList =
                this.inventoryBidForm.synthesizeTemporaryDtlList.filter(t => {
                    if (t.synthesizeTemporaryDtlId == row.synthesizeTemporaryDtlId) {
                        return false
                    } else {
                        return true
                    }
                })
        },

        async inventoryListClick (row) {
            //  根据清单编号查询：synthesizeTemporarySn
            this.inventoryProductList = []
            this.inventorySelectedProductList = []
            showLoading()
            this.inventoryBidForm.billType = row.billType
            this.currentBillType = row.billType
            let res = await suppliersSynthesizeTemporaryGetBySn({
                sn: row.synthesizeTemporarySn,
            })
            this.inventoryProductList = res.dtls || []
            hideLoading()
        },

        handleCurrentInventoryProductClick (row) {
            if (row.isBidding === 1) {
                return this.$message.warning('该商品已生成竞价')
            }
            for (let i = 0; i < this.inventorySelectedProductList.length; i++) {
                let t = this.inventorySelectedProductList[i]
                if (t.synthesizeTemporaryDtlId == row.synthesizeTemporaryDtlId) {
                    return this.$message.warning('该商品已选择！')
                }
            }
            this.inventorySelectedProductList.push(row)
        },
        getSupplierList (data) {
            this.inventoryBidForm.suppliers = data
        },

        // 确认选定商品
        selectInventoryProductAffirm () {
            this.inventoryBidForm.synthesizeTemporaryDtlList =
                this.inventorySelectedProductList
            this.showInventoryList = false
            // 清空
            this.inventoryList = []
            this.inventoryProductList = []
            this.inventorySelectedProductList = []
        },

        // 获取清单列表数据
        async getInventoryList () {
            let params = {
                pageNum: this.inventoryListPagination.currentPage,
                pageSize: this.inventoryListPagination.pageSize,
            }
            if (this.inventoryKey != null && this.inventoryKey != '') {
                params.synthesizeTemporarySn = this.inventoryKey.trim()
            }
            // params.bidStatus = 0
            params.billType = this.inventoryBidForm.billType
            params.states = [0, 1, 5, 11]
            params.stType = this.inventoryBidForm.productType === 1 ? 0 : 1
            showLoading()
            let res = await listByEntitySynthesizeTemporary(params)
            this.inventoryListPagination.total = res.totalCount
            this.inventoryListPagination.pageSize = res.pageSize
            this.inventoryListPagination.currentPage = res.currPage
            this.inventoryList = res.list || []
            hideLoading()
        },

        reset () {
            this.showInventoryList = false
            this.inventoryBidFormVisible = false
            this.inventoryBidForm.synthesizeTemporaryDtlList = []
            this.inventoryBidForm = {
                productType: 1,
                biddingSourceType: 3,
                billType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                synthesizeTemporaryDtlList: [],
                suppliers: [],
            }
        },
        // 创建清单竞价
        createInventoryBid () {
            this.$refs.inventoryBidingFormRef.validate(valid => {

                if (valid) {
                    if (this.inventoryBidForm.synthesizeTemporaryDtlList.length === 0) {
                        return this.$message.warning('请选择商品！')
                    }
                    if (this.inventoryBidForm.billType != this.currentBillType) {
                        return this.$message.error('价格类型不一致，请重新选择')
                    }
                    if (
                        this.inventoryBidForm.type === 2 &&
                        this.inventoryBidForm.suppliers.length === 0
                    ) {
                        return this.$message.warning('请选择供应商！')
                    }
                    // 浮动价格必须先填写网价
                    if (this.inventoryBidForm.billType === 1) {
                        let arr = this.inventoryBidForm.synthesizeTemporaryDtlList.filter(
                            item => {
                                return item.bidNetPrice == '' || Number(item.bidNetPrice) == 0
                            }
                        )
                        if (arr.length > 0) {
                            return this.$message.error(
                                '商品【' + arr[0].productName + '】未填写网价'
                            )
                        }
                    }
                    // maxPrice 为0或者为空提示确认
                    let arr = this.inventoryBidForm.synthesizeTemporaryDtlList.filter(
                        item => {
                            return item.maxPrice == '' || Number(item.maxPrice) == 0
                        }
                    )

                    if (arr.length > 0) {
                        return this.$confirm(
                            '商品：【' +
                            arr[0].productName +
                            '】未设置最高单价，将不会进行限价，是否确认提交？',
                            '提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        )
                            .then(() => {
                                createInventoryBidding(this.inventoryBidForm).then(res => {
                                    if (res.code === 200) {
                                        this.$message.success('创建成功！')
                                        this.reset()
                                        this.$emit('getList')
                                    }
                                })
                            })
                            .catch(() => {
                                this.$message({
                                    type: 'info',
                                    message: '已取消',
                                })
                            })
                    } else {
                        createInventoryBidding(this.inventoryBidForm).then(res => {
                            if (res.code === 200) {
                                this.$message.success('创建成功！')
                                this.reset()
                                this.$emit('getList')
                            }
                        })
                    }
                } else {
                    this.$message.error('请检查必填项')
                    return
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
@import "../bidingList/index.scss";

.btn10 {
    margin: 2px;
    padding: 4px 12px !important;
    height: auto !important;
    line-height: normal !important;
}
</style>
