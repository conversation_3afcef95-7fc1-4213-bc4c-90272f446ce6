// import { calculateNotTarRateAmount } from '@/utils/common'

import { calculateNotTarRateAmount, calculateNotTarRateAmountFour, toFixed } from '@/utils/common'

function  fixed2 (num) {
    return toFixed(num, 2)
}

function  capitalsAndNum  (reconciliationAmount, reconciliationNoRateAmount, taxRate) {
    console.log(2323 + taxRate + 34)
    return reconciliationNoRateAmount
}
function  capitalsItemNum  (acceptanceNoRateAmount1, noRatePrice, quantity) {
    let acceptanceNoRateAmount = fixed2(Number(noRatePrice) * Number(quantity))
    console.log(acceptanceNoRateAmount)
    return acceptanceNoRateAmount1
}

//零星
export function  reconciliationAmountM  (tableData, taxRate) {
    let  params = {
        tableData: [],
        reconciliationAmount: 0,
        reconciliationNoRateAmount: 0,
        taxAmount: 0
    }
    let reconciliationAmount = 0
    let reconciliationNoRateAmount = 0
    let taxAmount = 0
    // 最终计算
    for (let i = 0; i < tableData.length; i++) {
        let t = tableData[i]
        t.acceptanceAmount  = fixed2(Number(t.quantity) * Number(t.price))
        t.noRatePrice =  calculateNotTarRateAmount(t.price, taxRate)
        t.acceptanceNoRateAmount =  calculateNotTarRateAmount(t.acceptanceAmount, taxRate)
        t.taxAmount =  fixed2(Number(calculateNotTarRateAmountFour(t.acceptanceAmount, taxRate)) * Number(taxRate / 100))
        taxAmount = fixed2(Number(taxAmount) + Number(t.taxAmount))
        reconciliationAmount = fixed2(Number(reconciliationAmount) + Number(t.acceptanceAmount))
        reconciliationNoRateAmount = fixed2(Number(reconciliationNoRateAmount) + Number(t.acceptanceNoRateAmount))
    }
    params.tableData = tableData
    params.reconciliationAmount = reconciliationAmount
    params.reconciliationNoRateAmount = reconciliationNoRateAmount
    params.taxAmount = taxAmount
    return params
}

//浮动
export function  reconciliationFloatCountAmount  (tableData, taxRate) {
    let reconciliationAmount = 0
    let reconciliationNoRateAmount = 0
    let taxAmount = 0
    // 最终计算
    for (let i = 0; i < tableData.length; i++) {
        let t = tableData[i]

        let price = fixed2(Number(t.freightPrice) + Number(t.fixationPrice))
        if (price * 1 != 0.00) {
            t.price = price
        }
        t.noRatePrice =  calculateNotTarRateAmount(t.price, taxRate)
        t.acceptanceAmount  = fixed2(Number(t.quantity) * Number(t.price))
        t.acceptanceNoRateAmount =  calculateNotTarRateAmount(t.acceptanceAmount, taxRate)
        t.taxAmount = fixed2(Number(calculateNotTarRateAmountFour(t.acceptanceAmount, taxRate)) * Number(taxRate / 100))
        taxAmount = fixed2(Number(taxAmount) + Number(t.taxAmount))
        reconciliationAmount = fixed2(Number(reconciliationAmount) + Number(t.acceptanceAmount))
        reconciliationNoRateAmount = fixed2(Number(reconciliationNoRateAmount) + Number(t.acceptanceNoRateAmount))
    }
    let  params = {
        tableData: tableData,
        taxAmount: taxAmount,
        reconciliationAmount: reconciliationAmount,
        reconciliationNoRateAmount: reconciliationNoRateAmount,

    }
    return params
}
export function  reconciliationFixCountAmount  (tableData, taxRate, preserveOriginalNoRatePrice = false) {
    let reconciliationAmount = 0
    let reconciliationNoRateAmount = 0
    let taxAmount = 0

    // 最终计算
    for (let i = 0; i < tableData.length; i++) {
        let t = tableData[i]

        // 含税单价保持原值，不重新计算
        // t.price 保持原值

        // 如果需要保留原始不含税单价且原始值有效，则不重新计算
        if (!preserveOriginalNoRatePrice || !t.originalNoRatePrice || Number(t.originalNoRatePrice) <= 0) {
            // 重新计算不含税单价，保留2位小数
            t.noRatePrice = fixed2(calculateNotTarRateAmount(t.price, taxRate))
        } else {
            // 保留原始不含税单价，保留2位小数
            t.noRatePrice = fixed2(Number(t.originalNoRatePrice))
        }

        // 含税金额 = 含税单价 * 数量
        t.acceptanceAmount = fixed2(Number(t.quantity) * Number(t.price))
        // 不含税金额，保留2位小数
        t.acceptanceNoRateAmount = fixed2(calculateNotTarRateAmount(t.acceptanceAmount, taxRate))
        // 税额，保留2位小数
        t.taxAmount = fixed2(Number(calculateNotTarRateAmountFour(t.acceptanceAmount, taxRate)) * Number(taxRate / 100))

        taxAmount = fixed2(Number(taxAmount) + Number(t.taxAmount))
        reconciliationAmount = fixed2(Number(reconciliationAmount) + Number(t.acceptanceAmount))
        reconciliationNoRateAmount = fixed2(Number(reconciliationNoRateAmount) + Number(t.acceptanceNoRateAmount))
    }

    let  params = {
        tableData: tableData,
        reconciliationAmount: reconciliationAmount,
        reconciliationNoRateAmount: reconciliationNoRateAmount,
        taxAmount: taxAmount
    }
    return params
}
export function  taxRateItemAmount  (price, quantity, taxRate, originalNoRatePrice) {
    let  params = {
        noRatePrice: 0,
        acceptanceAmount: 0,
        acceptanceNoRateAmount: 0,
        taxAmount: 0
    }

    // 如果传入了原始不含税单价且有效，则使用原始值，否则重新计算
    if (originalNoRatePrice && Number(originalNoRatePrice) > 0) {
        params.noRatePrice = Number(originalNoRatePrice)
    } else {
        params.noRatePrice = calculateNotTarRateAmount(price, taxRate)
    }

    params.acceptanceAmount = fixed2(price * quantity)
    params.acceptanceNoRateAmount = calculateNotTarRateAmount(params.acceptanceAmount, taxRate)
    params.taxAmount = fixed2(Number(calculateNotTarRateAmountFour(params.acceptanceAmount, taxRate)) * Number(taxRate / 100))
    return params
}
// function fixed4 (num) {
//     return toFixed(num, 4)
// }

export { capitalsAndNum, capitalsItemNum }