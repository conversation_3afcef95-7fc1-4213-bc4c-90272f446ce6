<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn dfa">
                            <el-button type="primary"  @click="addAffirmM">新增对账单</el-button>
                            <el-button v-if="this.showDevFunc" type="primary" class="btn-greenYellow" @click="addInvoiceM">申请开票</el-button>
                            <!--                            <el-button type="primary" class="btn-greenYellow" @click="batchSubmitCheck">批量提交</el-button>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="0">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="1">按开始时间</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按结束时间</el-radio>
                        <el-input clearable style="width: 300px" type="text" @blur="onSearch"
                                  placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png"
                                                                                       slot="suffix" @click="onSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table"
                          ref="tableRef"
                          :height="rightTableHeight"
                          v-loading="tableLoading"
                          :data="tableData"
                          @selection-change="tableSelectM"
                          @row-click="tableRowClickM"
                          border
                >
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="对账单编号" width="190" prop="billNo">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.billNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票状态" width="100" prop="invoiceState" v-if="showDevFunc">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.invoiceState == 0&&scope.row.state == 3">未申请</el-tag>
                            <el-tag  type="info" v-if="scope.row.invoiceState == 1&&scope.row.state == 3">申请中</el-tag>
                            <el-tag  type="success" v-if="scope.row.invoiceState == 2&&scope.row.state == 3">已开票</el-tag>
                            <el-tag  type="danger" v-if="scope.row.invoiceState == 3&&scope.row.state == 3">申请被拒</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="对账状态" width="" prop="type">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <el-tag v-if="scope.row.state == 1">已提交</el-tag>
                            <el-tag v-if="scope.row.state == 2">待审核</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 3">审核通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 4">审核不通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 7">已作废</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="自营店机构" width="230" prop="supplierName"/>
                    <el-table-column label="二级供应商机构" width="230" prop="twoSupplierName"/>
                    <!-- <el-table-column label="自营店确认状态" width="140" prop="type">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.supplierIsAffirm == 0">否</el-tag>
                            <el-tag type="success" v-if="scope.row.supplierIsAffirm == 1">是</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="二级供应商单位是否确认" width="140" prop="type">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.twoSupplierIsAffirm == 0">否</el-tag>
                            <el-tag type="success" v-if="scope.row.twoSupplierIsAffirm == 1">是</el-tag>
                        </template>
                    </el-table-column> -->
                    <el-table-column label="含税金额" width="" prop="rateAmount"/>
                    <el-table-column label="不含税金额" width="" prop="noRateAmount"/>
                    <el-table-column label="开始时间" width="160" prop="startTime"/>
                    <el-table-column label="结束时间" width="160" prop="endTime"/>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData != null || tableData.length != 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="40%" :close-on-click-modal="false">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="状态：">
                            <el-select style="width: 100%;" v-model="filterData.state" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="新增来源：">
                            <el-select style="width: 100%;" v-model="filterData.createType" placeholder="请选择新增来源">
                                <el-option
                                    v-for="item in filterData.createTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                <el-row>-->
                <!--                    <el-col :span="22" :offset="0">-->
                <!--                        <el-form-item label="自营店机构：" >-->
                <!--                            <el-input clearable maxlength="100" placeholder="自营店机构" v-model="filterData.supplierName"></el-input>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="供应商机构：">
                            <el-input clearable maxlength="100" placeholder="二级供应商机构"
                                      v-model="filterData.twoSupplierName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="对账编号：">
                            <el-input clearable maxlength="100" placeholder="请输入对账编号"
                                      v-model="filterData.billNo"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="源单编号：">
                            <!--                            //退货编号   发货单编号-->
                            <el-input clearable maxlength="100" placeholder="请输入源单编号"
                                      v-model="filterData.sourceBillNo"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="创建时间：" >
                            <el-date-picker style="width: 100%;"
                                :default-time="['00:00:00', '23:59:59']"
                                @change="getTableData"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateScope"
                                type="datetimerange"
                                range-separator="至"
                                :picker-options="pickerOptions"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="expertSearch">确定</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
import { debounce } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { supplierListByEntity, materialReconciliationSubmit } from '@/api/reconciliation/twoReconciliation'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            tableSelectRow: [],
            tableLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近一年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近二年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                        start.setHours('00', '00', '00')
                        end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '全部',
                    onClick (picker) {
                        picker.$emit('pick', [])
                    }
                }]
            },
            // 状态选择查询
            // 表格数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                supplierName: null,
                sourceBillNo: null,
                billNo: null,
                twoSupplierName: null,
                state: null,
                createType: null,
                dateScope: [],
                stateSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '草稿' },
                    { value: 1, label: '已提交' },
                    { value: 2, label: '待审核' },
                    { value: 3, label: '审核通过' },
                    { value: 4, label: '审核不通过' },
                    { value: 7, label: '已作废' },
                ],
                type: null,
                createTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '自营店新增' },
                    { value: 2, label: '供货单位新增' },
                ],
                orderBy: 0,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        addInvoiceM () {
            if (this.tableSelectRow.length === 0) {
                return this.$message.error('请选择数据！')
            }
            let ids =  this.tableSelectRow.filter(t => {
                return t.state === 3 && (t.invoiceState === 0 || t.invoiceState === 3)
            })
            if (ids.length == 0) {
                return this.$message.warning('请勾选有效的数据！')
            }
            let oneTaxRate = ids[0].taxRate
            let onesupplierOrgId = ids[0].twoSupplierId
            for (let i = 0; i < ids.length; i++) {
                if (oneTaxRate != ids[i].taxRate || onesupplierOrgId != ids[i].twoSupplierId) {
                    return this.$message.warning('对账单编号' +
                        ids[i].billNo + '收货单位：' + ids[i].twoSupplierId + '，税率：' +
                        ids[i].taxRate + '%和其他不符合，不能一起开票')
                }
            }
            let billIds =  ids.map(item => {
                return item.billId
            })
            this.$router.push({
                path: '/supplierSys/twoSupplierApply/apply',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'twoEnterpriseApply',
                params: {
                    row: {
                        billIds: billIds,
                    }
                }
            })
        },
        batchSubmitCheck () {
            if (this.tableSelectRow.length === 0) {
                return this.$message.error('请选择数据！')
            }
            let ids = this.tableSelectRow.filter(t => {
                return t.state == 0
            }).map(item => {
                return item.reconciliationId
            })
            if (ids.length == 0) {
                return this.$message.warning('请勾选有效的数据！')
            }
            this.clientPop('info', '您确定要批量提交这些数据吗？', async () => {
                this.tableLoading = true
                materialReconciliationSubmit(ids).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getTableData()
                    }
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },
        // 选择类型确认
        addAffirmM () {
            this.$router.push({
                path: '/performance/twoSheet/floatDetail',
                name: 'supplierSysTwoFloatDetail',
                query: {
                    createType: 1
                }
            })

        },

        // 详情
        handleView (row) {
            this.$router.push({
                path: '/supplierSys/twoSheetDetailInfo',
                name: 'twoSheetDetailInfo',
                query: {
                    billNo: row.billNo
                }
            })
        },
        // 高级搜索
        expertSearch () {
            this.keywords = null
            this.getTableData()
            //重置数据
            this.filterData.twoSupplierName = null
            this.filterData.supplierName = null
            this.filterData.sourceBillNo = null
            this.filterData.billNo = null
            this.filterData.state = null
            this.filterData.createType = null
            this.filterData.type = null
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                businessType: 2,
                userType: 1
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.dateScope != null) {
                params.startDate = this.filterData.dateScope[0],
                params.endDate = this.filterData.dateScope[1]
            }
            if (this.filterData.createType != null) {
                params.createType = this.filterData.createType
            }
            if (this.filterData.twoSupplierName != null) {
                params.twoSupplierName = this.filterData.twoSupplierName
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.sourceBillNo != null) {
                params.sourceBillNo = this.filterData.sourceBillNo
            }
            if (this.filterData.billNo != null) {
                params.billNo = this.filterData.billNo
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.type != null) {
                params.type = this.filterData.type
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            this.tableLoading = true
            supplierListByEntity(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        onSearch () {
            this.getTableData()
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}

/deep/ #addreconciliationId {
    .el-dialog__body {
        height: 180px;
        margin-top: 0px;
    }
}

.tabs-title {
    margin: 0 0 0 10px;
    padding: 0px 0 10px 0px;
    color: #409eff;
    line-height: 22px;
    position: relative;
}
</style>
