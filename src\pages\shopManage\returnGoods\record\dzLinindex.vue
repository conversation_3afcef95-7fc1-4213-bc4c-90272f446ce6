<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <el-select @change="stateOptionsClick" v-model="stateOptionTitle" placeholder="请选择状态">
                            <el-option
                                v-for="item in stateOptions" :key="item.value" :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <el-select style="margin-left: 10px;"
                            @change="stateOptionsClickIsOut" v-model="stateOptionTitleIsOut"
                            placeholder="请选择退货来源"
                        >
                            <el-option
                                v-for="item in stateOptionsIsOut" :key="item.value" :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>

                    </div>
                    <div class="search_box">
                        <el-input type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"  alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table">
                <el-table
                    class="table" :height="rightTableHeight" :data="tableData" border
                    @selection-change="selectionChangeHandle"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="订单号" width="240" prop="orderSn"></el-table-column>
<!--                    <el-table-column label="订单号" width="240" prop="otherOrderSn"></el-table-column>-->
                    <el-table-column label="退货编号" width="200" prop="orderReturnNo">
                        <template v-slot="scope">
                            <span
                                class="action"
                                @click="handleView(scope.row.orderReturnId)"
                            >{{ scope.row.orderReturnNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" width="240" prop="untitled"></el-table-column>
                    <el-table-column label="采购机构" width="240" prop="enterpriseName"></el-table-column>
                    <!--                    <el-table-column label="商品图片" width="240" prop="productMinImg">-->
                    <!--                        <template v-slot="scope">-->
                    <!--                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg"-->
                    <!--                                      fit="cover"></el-image>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column label="状态" prop="state">
                        <template v-slot="scope">
                            <el-tag v-show="scope.row.state==1">已申请</el-tag>
                            <el-tag v-show="scope.row.state==2" type="info">退货中</el-tag>
                            <el-tag v-show="scope.row.state==3" type="success">退货成功</el-tag>
                            <el-tag v-show="scope.row.state==4" type="danger">退货失败</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="退货金额" width="100" prop="totalAmount"/>
                    <!--                    <el-table-column label="退货数量" width="100" prop="count"/>-->
                    <el-table-column label="退货来源" width="200" prop="returnMethod">
                        <template v-slot="scope">
                            <el-tag v-show="scope.row.isOut==1">PCWP退货</el-tag>
                            <el-tag v-show="scope.row.isOut==0" type="waring">商城退货</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单类型" width="200" prop="returnMethod">
                        <!--<template v-slot="scope">-->
                            <el-tag  type="waring">多供方订单</el-tag>
                        <!--</template>-->
                    </el-table-column>
                    <el-table-column label="申请退货时间" width="160" prop="gmtCreate"/>
                    <el-table-column label="退货成功时间" width="160" prop="flishTime">
                        <template v-slot="scope">
                            <span
                                v-if="scope.row.state==3"
                            >{{ scope.row.flishTime }}</span>
                            <span
                                v-if="scope.row.state!=3"
                            >-</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="退款原因" width="300" prop="orderRemark"></el-table-column>-->
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData || tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog class="dialogHeightAuto" v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="状态：">
                            <el-select style="width: 100%;"
                                @change="stateOptionsClick" v-model="filterData.state"
                                placeholder="请选择状态"
                            >
                                <el-option
                                    v-for="item in stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="退货来源：">
                            <el-select style="width: 100%;"
                                @change="stateOptionsClick" v-model="filterData.isOut"
                                placeholder="请选择退货来源"
                            >
                                <el-option
                                    v-for="item in stateOptionsIsOut"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                    <el-form-item label="采购机构名称：">
                        <el-input
                            v-model="filterData.enterpriseName" placeholder="请输入采购机构"
                        ></el-input>
                    </el-form-item>
                    </el-col>
                </el-row>
                <!--                <el-row>-->
                <!--                    <el-col :span="22">-->
                <!--                        <el-input v-model="filterData.shipEnterPriseName" placeholder="请输入二级供应商名称"-->
                <!--                        ></el-input>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <!--                <el-row>-->
                <!--                    <el-col :span="12">-->
                <!--                        <el-input v-model="filterData.supplierName" placeholder="请输入供应商名称"-->
                <!--                        >-->
                <!--                        </el-input>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <el-row>
                    <el-col :span="11">
                        <el-form-item label="实际金额以上：">
                            <el-input
                                type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="实际金额以下：">
                            <el-input
                                type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapActions, mapMutations, mapState } from 'vuex'
import { shopMangeOrderReturnList } from '@/api/shopManage/refund/refundapply'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            stateOptionTitle: '', // 选中的状态标题
            stateOptionTitleIsOut: '', // 选中的状态标题
            stateOptions: [
                {
                    value: null,
                    label: '全部'
                }, {
                    value: 1,
                    label: '已申请'
                }, {
                    value: 2,
                    label: '退货中'
                }, {
                    value: 3,
                    label: '退货成功'
                }, {
                    value: 4,
                    label: '退货失败'
                }],
            stateOptionsIsOut: [
                {
                    value: null,
                    label: '全部'
                }, {
                    value: 1,
                    label: 'PCWP推送'
                }, {
                    value: 0,
                    label: '商城退货'
                }],
            // 表格数据
            tableStateTitle: null, // 表格的状态
            dataListSelections: [], //选中的数据
            className: null,
            classId: null, // 分类id
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                enterpriseName: null,
                isOut: null,
                state: null,
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
                stateOptions: [
                    {
                        value: null,
                        label: '全部'
                    }, {
                        value: 1,
                        label: '已申请'
                    }, {
                        value: 2,
                        label: '退货中'
                    },
                    {
                        value: 3,
                        label: '退货成功'
                    },
                    {
                        value: 4,
                        label: '退货失败'
                    }],
                shipEnterPriseName: null,
                supplierName: null,
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    },
    methods: {
        // 显示状态
        showTableState (row) {
            let stateValue = row.state
            if (stateValue === 6) {
                return false
            }
            for (let i = 0; i < this.stateOptions.length; i++) {
                if (stateValue === this.stateOptions[i].value) {
                    this.tableStateTitle = this.stateOptions[i].label
                    return true
                }
            }
        },
        // 选中状态进行查询
        stateTopOptionsClickIsOut (value) {
            this.stateOptionTitleIsOut = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            //利用$router.push进行跳转
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/returnGoods/recodDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'recodDetail',
                params: {
                    row: row
                }
            })
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.stateOptionTitle = ''
            this.stateOptionTitleIsOut = ''
            this.getTableData()
            this.queryVisible = false
        },

        resetSearchConditions () {
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.isOut = null // 以上价格
            this.filterData.state = null // 以上价格
            this.filterData.enterpriseName = null // 以上价格
            this.filterData.supplierName = null // 以上价格
            this.filterData.shipEnterPriseName = null // 以上价格
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.selectStateTitle = null// 选中的标题
            this.filterData.selectSateValue = null // 选中的值
        },
        // 高级搜索状态选中
        stateOptionsClick (value) {
            this.filterData.selectSateValue = value
        },
        stateOptionsClickIsOut (value) {
            this.filterData.isOut = value
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        ...mapActions('equip', ['setUnitMeasur']),
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                sourceType: 6,
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.shipEnterPriseName != null) {
                params.shipEnterPriseName = this.filterData.shipEnterPriseName
            }
            if (this.filterData.enterPriseName != null) {
                params.shipEnterPriseName = this.filterData.shipEnterPriseName
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.enterPriseName != null) {
                params.enterPriseName = this.filterData.enterPriseName
            }

            if (this.filterData.selectSateValue != null) {
                params.state = this.filterData.selectSateValue
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            showLoading()
            shopMangeOrderReturnList(params).then(res => {
                this.tableData = res.list || []
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                console.log(this.paginationInfo)
                hideLoading()
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}
.dialogHeightAuto /deep/ .el-dialog__body {
  height: auto;
}

.e-table {
    min-height: auto;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}
</style>
