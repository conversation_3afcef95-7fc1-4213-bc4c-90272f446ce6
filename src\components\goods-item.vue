<template>
    <div class="goods-item">
        <div>
            <img :src="itemData.productMinImg
                ? imgUrlPrefixAdd + itemData.productMinImg
                : require('@/assets/images/img/queshen5.png')
                " alt="" @click="
                    openWindowTab({
                        path: '/mFront/productDetail',
                        query: { productId: itemData.productId },
                    })
                    " />
            <div class="name textOverflowName">{{ itemData.productName }}</div>
            <div class="type dfc textOverflowType">
                {{ itemData.skuName }}
            </div>
            <div style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
        ">
                <div class="price" style="margin-left: 10px">
                    ￥<span>{{ itemData.sellPrice }}</span>
                </div>
                <div style="width: 100px">
                    <QuantitySelector v-model="itemData.goodsNum" @changeSelector="handleChange" :min="1"
                        :max="100000" />
                </div>
                <img style="width: 20px; height: 20px; margin-right: 10px" src="../assets/images/shopping_cart.png"
                    @click="handleView" alt="" />
            </div>
            <el-dialog :visible.sync="shoppingShowDialog" width="30%" class="dialog-shopping" @close="resetForm">
                <template #title>
                    <div class="tabs-title">
                        <span>商品信息选择</span>
                    </div>
                </template>
                <el-form :model="form" :rules="rules" ref="ruleForm" style="margin-right: 30px; margin-top: 40px">
                    <el-form-item v-if="productInfo.priceType == 0" label="账期：" label-width="100px"
                        prop="accountPeriod">
                        <el-select v-model="form.accountPeriod" placeholder="请选择账期" style="width: 400px"
                            @change="handleZqChange">
                            <el-option v-for="item in accountPeriodOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="区域：" label-width="100px" prop="region">
                        <!-- <el-select v-model="form.areaCode" placeholder="请选择区域" style="width: 400px"
                            @change="handleQyChange">
                            <el-option v-for="item in getDeliveryAreaOptions()" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select> -->
                        <el-select v-model="form.areaCode" placeholder="请选择区域" style="width: 400px"
                            @change="handleQyChange">
                            <el-option v-for="item in regionOption" :key="item.areaCode" :label="item.area"
                                :value="item.areaCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="shoppingShowDialog = false">取 消</el-button>
                    <el-button type="primary" @click="handleAddToCart" :loading="addToCartLoading">确 定</el-button>
                </span>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import {
    addCart,
    getLogInMaterialInfo,
    getMaterialInfo,
} from '@/api/frontStage/productDetail'
import QuantitySelector from '@/components/quantity-selector'
export default {
    name: 'goodItem',
    components: {
        QuantitySelector,
    },
    props: {
        itemData: Object,
    },
    computed: {
        rules () {
            return {
                accountPeriod: this.productInfo.priceType === 0
                    ? [{ required: true, message: '请选择账期', trigger: 'blur' }]
                    : [],
                // 区域始终需要验证
                areaCode: [{ required: true, message: '请选择区域', trigger: 'blur' }]
            }
        }
    },
    data () {
        return {
            goodsNum: 1,
            productInfo: {},
            accountPeriodOptions: [],
            shoppingShowDialog: false,
            addToCartLoading: false,
            showClose: true,
            form: {
                accountPeriod: '',
                region: '',
                zoneId: '',
                zoneAddr: '',
                regionPriceId: '', // 价格区域id
                areaCode: '',
                areaValue: '',
                area: '',
            },
            deliveryAreaOptions: [],
            accountPeriodOption: [],
            regionOption: [],
        }
    },
    mounted () {
    },

    methods: {
        resetForm () {
            if (this.$refs.ruleForm) {
                this.$refs.ruleForm.clearValidate()
            }
        },
        handleAddToCart () {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    this.addToCartLoading = true
                    const params = {
                        productId: this.productInfo.productId,
                        cartNum: this.goodsNum,
                        zoneId: this.form.areaCode || null, // 获取配送区域字段id
                        zoneAddr: this.form.area || null, // 获取配送区域值
                        paymentPeriod: this.form.accountPeriod || null,
                        regionPriceId: this.form.areaValue || null
                    }
                    addCart(params)
                        .then(res => {
                            if (res.code == 200) {
                                this.$message({
                                    message: '加入购物车成功！',
                                    type: 'success',
                                })
                                this.$bus.$emit('refreshCart')
                                this.shoppingShowDialog = false
                            }
                        })
                        .catch(() => {
                        })
                        .finally(() => {
                            this.addToCartLoading = false
                        })
                } else {
                    return false
                }
            })

        },

        handleQyChange (val) {
            let rp = this.regionOption.filter(item => item.areaCode === val)[0]
            this.form.areaValue = rp.regionPriceId
            this.from.area = rp.area
        },

        handleZqChange (val) {
            this.getRegionOption(val)
        },

        getRegionOption (val) {
            let productInfoFil = []
            if (this.productInfo.priceType == 1) { //参考价没有账期
                productInfoFil = this.productInfo.regionPrice
            } else {
                productInfoFil = this.productInfo.regionPrice.filter(
                    item => item.accountPeriod === val
                )
            }
            let regionOptions = []
            for (let itemI of productInfoFil) {
                let areaArr = itemI.area ? itemI.area.split(',') : []
                let areaCodeArr = itemI.areaCode ? itemI.areaCode.split(',') : []
                for (let index = 0; index < areaArr.length; index++) {
                    regionOptions.push({ ...itemI, area: areaArr[index], areaCode: areaCodeArr[index] })
                }
            }
            this.regionOption = regionOptions
            if (this.regionOption.length) {
                // let rp = res.regionPrice.filter(item => item.regionPriceId === this.area)[0]
                let rp = this.regionOption[0]
                this.form.area = rp.area
                this.form.areaValue = rp.regionPriceId
                this.form.areaCode = rp.areaCode
            }
        },

        // 获取商品当前账期对应的配送区域选项
        getDeliveryAreaOptions () {
            if (!this.productInfo.regionPriceList || !Array.isArray(this.productInfo.regionPriceList)) {
                return []
            }

            // 过滤当前账期对应的regionPrice
            const filteredRegionPrices = this.productInfo.regionPriceList.filter(regionPrice => {
                return regionPrice.accountPeriod === this.productInfo.paymentPeriod
            })

            // 提取配送区域选项
            const areaMap = new Map()

            filteredRegionPrices.forEach(regionPrice => {
                if (regionPrice.area && regionPrice.areaCode) {
                    // 拆分area字符串（如"北京市,天津市"）
                    const areaNames = regionPrice.area.split(',').map(name => name.trim())
                    // 解析areaCode数组（如["110000","120000"]）
                    let areaCodes = []
                    try {
                        areaCodes = Array.isArray(regionPrice.areaCode)
                            ? regionPrice.areaCode
                            : JSON.parse(regionPrice.areaCode)
                    } catch (e) {
                        areaCodes = [regionPrice.areaCode]
                    }

                    // 将area和areaCode一一对应展示
                    areaNames.forEach((areaName, index) => {
                        const areaCode = areaCodes[index] || areaCodes[0] // 如果数量不匹配，使用第一个code
                        if (areaName && areaCode && !areaMap.has(areaCode)) {
                            areaMap.set(areaCode, {
                                label: areaName, // 单个区域名称
                                value: areaCode // 单个区域代码
                            })
                        }
                    })
                }
            })

            // 转换为数组并排序
            return Array.from(areaMap.values()).sort((a, b) => a.label.localeCompare(b.label))
        },
        // 根据zoneId获取配送区域的中文名称
        getDeliveryAreaName (product, zoneId) {
            if (!zoneId) return ''

            const deliveryOptions = this.getDeliveryAreaOptions(product)
            const option = deliveryOptions.find(opt => opt.value === zoneId)
            return option ? option.label : zoneId
        },

        // 根据选择的区域获取对应的regionPriceId
        getRegionPriceId (areaCode) {
            if (!this.productInfo.regionPriceList || !Array.isArray(this.productInfo.regionPriceList) || !areaCode) {
                this.form.regionPriceId = ''
                return
            }

            // 过滤当前账期对应的regionPrice
            const filteredRegionPrices = this.productInfo.regionPriceList.filter(regionPrice => {
                return regionPrice.accountPeriod === this.form.accountPeriod
            })

            // 查找匹配的区域价格信息
            for (let regionPrice of filteredRegionPrices) {
                if (regionPrice.areaCode) {
                    let areaCodes = []
                    try {
                        areaCodes = Array.isArray(regionPrice.areaCode)
                            ? regionPrice.areaCode
                            : JSON.parse(regionPrice.areaCode)
                    } catch (e) {
                        areaCodes = [regionPrice.areaCode]
                    }

                    // 检查是否包含当前选择的区域代码
                    if (areaCodes.includes(areaCode)) {
                        this.form.regionPriceId = regionPrice.regionPriceId
                        return
                    }
                }
            }

            // 如果没有找到匹配的，清空regionPriceId
            this.form.regionPriceId = ''
        },
        // 获取商品详情
        getProductInfoM () {
            let params = {
                productId: this.itemData.productId,
            }
            if (!localStorage.getItem('token')) {
                getMaterialInfo(params)
                    .then(res => {
                        this.productInfo = res
                        if (this.productInfo.shopState === 0) {
                            return this.$message.error('该店铺已被冻结，无法购买商品')
                        } else {
                            this.form.accountPeriod = null
                            this.form.region = null
                            this.shoppingShowDialog = true
                        }
                    })
                    .catch(() => {
                        this.$message({
                            message: '商品不存在或商品已下架',
                            type: 'warning',
                        })
                    })
            } else {
                getLogInMaterialInfo(params).then(res => {
                    this.accountPeriodOptions = []
                    this.productInfo = res
                    let ap = res.accountPeriod.split(',').map(Number)
                    this.form.accountPeriod = ap[0]
                    ap.forEach(e => {
                        if (e == 1) {
                            this.accountPeriodOptions.push({ value: 1, label: '1个月账期' })
                        }
                        if (e == 2) {
                            this.accountPeriodOptions.push({ value: 2, label: '2个月账期' })
                        }
                        if (e == 3) {
                            this.accountPeriodOptions.push({ value: 3, label: '3个月账期' })
                        }
                        if (e == 4) {
                            this.accountPeriodOptions.push({ value: 4, label: '4个月账期' })
                        }
                        if (e == 5) {
                            this.accountPeriodOptions.push({ value: 5, label: '5个月账期' })
                        }
                        if (e == 6) {
                            this.accountPeriodOptions.push({ value: 6, label: '6个月账期' })
                        }
                    })
                    // this.regionOption = res.regionPrice.filter(item => item.accountPeriod === this.accountPeriod)
                    this.getRegionOption(this.form.accountPeriod)
                    this.shoppingShowDialog = true
                }).catch(() => {
                    // let codes = { '500101': '商品不存在', '500102': '商品已下架', }
                    // if (Object.keys(codes).includes(res.code)) {
                    //     this.noProductText = codes[String(res.code)]
                    //     return this.showProduct = false
                    // }
                    this.shoppingShowDialog = false
                    this.$message({
                        message: '商品不存在或商品已下架',
                        type: 'warning',
                    })
                })
            }
        },
        handleChange (goodsNum) {
            this.goodsNum = goodsNum
        },

        changeRegion (row) {
            // this.form.zoneAddr = row.zoneAddr
            // this.form.zoneId = row.zoneId
            // console.log('changeRegion', row)
            this.form.zoneId = row

            // 根据选择的区域获取对应的regionPriceId
            this.getRegionPriceId(row)
        },

        // 账期改变时的处理
        changeAccountPeriod () {
            // 账期改变时，如果已选择区域，需要重新获取regionPriceId
            if (this.form.zoneId) {
                this.getRegionPriceId(this.form.zoneId)
            }
        },
        handleView () {
            this.getProductInfoM()
            this.$nextTick(() => {
                this.resetForm()
            })
        },
    },
}
</script>

<style scoped lang="scss">
@import '../assets/css/floor.scss';

.dialog-shopping {
    /deep/ .el-dialog__header {
        padding: 0;
    }

    .tabs-title {
        margin-left: 20px;
        border-left: #2e61d7 6px solid;
        padding-left: 6px;
    }

    /deep/ .el-dialog__footer {
        padding: 20px 20px 10px 0;
    }
}

.textOverflowName {
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.textOverflowType {
    overflow: hidden;
    height: 28px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    // text-align: center;
    word-break: break-all;
}

/*.tabs-title::before {
  content: '';
  height: 15px;
  width: 6px;
  border-radius: 2px;
  background-color: #2e61d7;
  // display: block;
  position: absolute;
  left: -2px;
  margin-top: 1px;
  margin-left: 10px;
  margin-right: 12px;
}*/

:deep(.el-button) {
    padding: 0px 20px !important;
}

:deep(.el-button--text) {
    color: #3b3c3d !important;
}

// :deep( .el-select){
//   display: block  !important;
//   position: relative;
// }</style>
