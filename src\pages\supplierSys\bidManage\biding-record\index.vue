<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <div class="top">
          <div class="left">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="待审核" name="first"> </el-tab-pane>
              <el-tab-pane label="竞价记录" name="second"> </el-tab-pane>
            </el-tabs>
          </div>
          <div class="search_box1">
            <el-radio v-model="filterData.orderBy" :label="1"
            >按创建时间排序</el-radio
            >
            <el-radio v-model="filterData.orderBy" :label="2"
            >按发布时间</el-radio
            >
            <el-radio v-model="filterData.orderBy" :label="3"
            >按截止时间</el-radio
            >
            <el-input
              clearable
              style="width: 300px"
              type="text"
              @blur="handleInputSearch"
              placeholder="输入搜索关键字"
              v-model="keywords"
            >
              <img
                src="@/assets/search.png"
                slot="suffix"
                @click="handleInputSearch"
                alt=""
              />
            </el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="queryVisible = true"
              >高级搜索</el-button
              >
            </div>
          </div>
        </div>
      </div>
      <!--表格-->
      <div class="e-table">
        <el-table
          class="table"
          ref="tableRef"
          :height="rightTableHeight"
          @selection-change="tableSelectM"
          @row-click="tableRowClickM"
          v-loading="tableLoading"
          :data="tableData"
          border
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column
            label="序号"
            type="index"
            width="60"
          ></el-table-column>
          <el-table-column label="竞价编号" width="200" prop="biddingSn">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope.row)">{{
                scope.row.biddingSn
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="标题"
            width="200"
            prop="title"
          ></el-table-column>
          <el-table-column label="竞价采购类型" width="90" prop="type">
            <template v-slot="scope">
              <span v-if="scope.row.type == 1"
                ><el-tag type="success">公开竞价</el-tag></span
              >
              <span v-if="scope.row.type == 2"> <el-tag>邀请竞价</el-tag></span>
            </template>
          </el-table-column>
          <el-table-column label="截止时间" width="160" prop="endTime">
            <template v-slot="scope">
              <span>{{ scope.row.endTime | dateStr }}</span>
            </template>
          </el-table-column>
          <el-table-column label="商品类型" prop="productType" width="140">
            <template v-slot="scope">
              <el-tag v-if="scope.row.productType == 0">低值易耗品</el-tag>
              <el-tag v-if="scope.row.productType == 1">大宗临购订单</el-tag>
              <el-tag v-if="scope.row.productType == 2">周转材料清单</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="生成来源"
            prop="biddingSourceType"
            width="100"
          >
            <template v-slot="scope">
              <el-tag v-if="scope.row.biddingSourceType == 1">订单</el-tag>
              <el-tag v-if="scope.row.biddingSourceType == 2"
                >物资基础库</el-tag
              >
              <el-tag v-if="scope.row.biddingSourceType == 3">清单</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="state">
            <template v-slot="scope">
              <el-tag type="info" v-if="scope.row.state == 4">草稿</el-tag>
              <el-tag v-if="scope.row.state == 6">审核中</el-tag>
              <el-tag type="danger" v-if="scope.row.state == 10"
                >已作废</el-tag
              >
              <el-tag type="success" v-if="scope.row.state == 7 || scope.row.state == 8 || scope.row.state == 9"
                >已审核</el-tag
              >
              <!-- <el-tag type="" v-if="scope.row.state == 6">中标待审核</el-tag>
              <el-tag type="success" v-if="scope.row.state == 7">已中标</el-tag>
              <el-tag type="danger" v-if="scope.row.state == 9">已流标</el-tag> -->
            </template>
          </el-table-column>
          <el-table-column label="联系人名称" width="" prop="linkName" />
          <el-table-column label="联系电话" width="" prop="linkPhone" />
          <el-table-column label="发布时间" width="200" prop="startTime" />
        </el-table>
      </div>
      <!--分页-->
      <Pagination
        v-show="tableData && tableData.length > 0"
        :total="paginationInfo.total"
        :pageSize.sync="paginationInfo.pageSize"
        :currentPage.sync="paginationInfo.currentPage"
        @currentChange="getTableData"
        @sizeChange="getTableData"
      />
    </div>
    <!--高级查询-->
    <el-dialog title="高级查询" :visible.sync="queryVisible" width="40%">
      <el-form
        :model="filterData"
        ref="form"
        label-width="120px"
        :inline="false"
      >
        <el-row>
          <el-col :span="22">
            <el-form-item label="商品类型：">
              <el-select style="width: 100%;"
                v-model="filterData.productType"
                placeholder="请选择商品类型"
              >
                <el-option
                  v-for="item in filterData.productTypeSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="状态：">
              <el-select style="width: 100%;" v-model="filterData.state" placeholder="请选择状态">
                <el-option
                  v-for="item in filterData.stateSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="单据时间状态：">
              <el-select style="width: 100%;"
                v-model="filterData.biddingState"
                placeholder="请选择订单类型"
              >
                <el-option
                  v-for="item in filterData.biddingStateSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="竞价类型：">
              <el-select style="width: 100%;" v-model="filterData.type" placeholder="请选择竞价类型">
                <el-option
                  v-for="item in filterData.typeSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="竞价编号：">
              <el-input
                clearable
                maxlength="100"
                placeholder="请输入竞价编号"
                v-model="filterData.biddingSn"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="标题：">
              <el-input
                clearable
                maxlength="100"
                placeholder="请输入标题"
                v-model="filterData.title"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="创建时间：">
              <el-date-picker style="width: 100%;"
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="filterData.createDate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="竞价截止时间：">
              <el-date-picker style="width: 100%;"
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="filterData.endDate"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="22">
            <el-radio v-model="filterData.orderBy" :label="1"
              >按创建时间排序</el-radio
            >
            <el-radio v-model="filterData.orderBy" :label="2"
              >按发布时间</el-radio
            >
            <el-radio v-model="filterData.orderBy" :label="3"
              >按截止时间</el-radio
            >
          </el-col>
        </el-row> -->
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="confirmSearch">查询</el-button>
        <el-button @click="resetSearchConditions">清空</el-button>
        <el-button @click="queryVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import {
    // listMyCreateBiding,
    getBiddingRecordInfo
} from '@/api/shopManage/biding/biding'
import Pagination from '@/components/pagination/pagination'

import { getSupplierShopsList } from '@/api/platform/supplier/supplierAudit'
import { getShopList } from '@/api/platform/mail/inbox'
import { mapState } from 'vuex'
import { debounce } from '@/utils/common'

export default {
    components: { Pagination },
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) return
            let newDateSr = dateStr.split(' ')
            return newDateSr[0]
        },
    },
    data () {
        return {
            // 第二个选择框的供应商
            activeName: 'first',
            materialSelectRow: [],
            shopList: {
                tableData: [],
                keywords: null,
                paginationInfo: {
                    // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            showTableLoading: false,
            showSupplierList: false,
            tableSelectRow: [],
            bidingOrderItemState: null,
            bidingOrderItemStateSelect: [
                { value: null, label: '全部' },
                { value: 2, label: '待分配' },
                { value: 3, label: '待分配竞价' },
            ],
            // 竞价
            bidingOrderItemSelectRow: [],
            biddingOrderItemLoading: false,
            biddingOrderListLoading: false,
            biddingOrderItemLoading2: false,

            showSelectOrderItem: false,
            showDialog: false,
            orderProductType: 10,
            bidingFormLoading2: false,
            bidingForm: {
                productType: 0,
                biddingSourceType: 1,
                billType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                orderItems: [],
                suppliers: [],
            },
            infoStr: '选择供应商',
            bidingFormOrderItems: [],
            bidingOrderList: [],
            bidingFormOrderItems2: [],
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            bidingFormLoading: false,
            tableLoading: false,
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                createDate: [],
                endDate: [],
                orderSn: null,
                title: null,
                biddingSn: null,
                state: 4,
                productType: null,
                stateSelect: [
                    { value: null, label: '全部' },
                    { value: 4, label: '待提交' },
                    { value: 6, label: '待审核' },
                    { value: 8, label: '审核失败' },
                    { value: 7, label: '审核通过' },
                    // { value: 7, label: '已确认' },
                    { value: 9, label: '已流标' },
                ],
                productTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '低值易耗品' },
                    { value: 1, label: '大宗临购' },
                    { value: 2, label: '大宗临购清单' },
                ],
                biddingState: null,
                biddingStateSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '未开始' },
                    { value: 2, label: '进行中' },
                    { value: 3, label: '已结束' },
                ],
                publicityState: null,
                publicityStateSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '未发布' },
                    { value: 1, label: '已发布' },
                ],
                orderBy: 1,
                type: null,
                typeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '公开竞价' },
                    { value: 2, label: '邀请竞价' },
                ],
            },
            orderSn: null,
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        // this.filterData.state = 6
        // this.getTableData()
        console.log('index  this.$route.query.Tab', this.$route.query.Tab)
        if(this.$route.query.Tab == 'second') {
            this.activeName = 'second'
            this.filterData.state = null
            this.filterData.biddingState = null
            this.getTableData()
        }else {
            this.activeName = 'first'
            this.filterData.state = 6
            this.getTableData()
        }
    },
    watch: {
        'filterData.orderBy' () {
            this.getTableData()
            this.getshopLists()
        },
    },
    created () {},
    methods: {
        handleClick (tab) {
            if (tab.label === '待审核') {
                this.filterData.state = 6
                this.getTableData()
            } else {
                this.filterData.state = null
                this.getTableData()
            }
        },
        sizeChange () {
            this.getshopLists()
        },
        currentChange () {
            this.getshopLists()
        },
        getShopList,
        async showSupplierDialog () {
            await this.getshopLists()
            this.showSupplierList = true
        },
        getshopLists () {
            let params = {
                page: this.shopList.paginationInfo.currentPage,
                limit: this.shopList.paginationInfo.pageSize,
                orderBy: this.filterData.orderBy,
            }
            if (this.shopList.keywords != null) {
                params.keywords = this.shopList.keywords
            }
            this.inventoryTableLoading = true
            getSupplierShopsList(params).then(res => {
                this.shopList.tableData = res.list || []
                this.shopList.paginationInfo.currentPage = res.currPage
                this.shopList.paginationInfo.pageSize = res.pageSize
                this.shopList.paginationInfo.total = res.totalCount
            })
            this.inventoryTableLoading = false
        },
        tableSelectM (value) {
            this.tableSelectRow = value
        },
        // 选择当前行
        selectMaterialRow (value) {
            this.materialSelectRow = value
        },
        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },

        // 详情
        handleView (row) {
            const Tab = this.activeName
            console.log('tab', Tab)
            console.log('row', row)
            this.$router.push({
                path: '/supplierSys/bidManage/bidingRecordDetail',
                name: 'bidingRecordDetail',
                query: {
                    state: this.filterData.state,
                    biddingId: row.biddingId,
                    Tab: Tab,
                },
            })
        },
        resetSearchConditions () {
            this.filterData.orderSn = null
            this.filterData.title = null
            this.filterData.biddingSn = null
            this.filterData.createDate = []
            this.filterData.endDate = []
            this.filterData.state = null
            this.filterData.biddingState = null
            this.filterData.productType = null
            this.filterData.publicityState = null
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = null
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            // 路由参数作为关键词
            if (this.$route.query.keywords != null) {
                this.keywords = this.$route.query.keywords
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.title != null) {
                params.title = this.filterData.title
            }
            if (this.filterData.biddingSn != null) {
                params.biddingSn = this.filterData.biddingSn
            }
            if (this.filterData.createDate != null) {
                params.createStartDate = this.filterData.createDate[0]
                params.createEndDate = this.filterData.createDate[1]
            }
            if (this.filterData.endDate != null) {
                params.startDate = this.filterData.endDate[0]
                params.endDate = this.filterData.endDate[1]
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.biddingState != null) {
                params.biddingState = this.filterData.biddingState
            }
            if (this.filterData.productType != null) {
                params.productType = this.filterData.productType
            }
            if (this.filterData.publicityState != null) {
                params.publicityState = this.filterData.publicityState
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.type != null) {
                params.type = this.filterData.type
            }
            let { mallRoles, shopName } = this.userInfo
            if (shopName === '四川路桥自营店' && this.showDevFunc) {
                let lg = mallRoles.some(item => item.name === '大宗临购管理人员')
                if (lg) params.productType = 1
                let lx = mallRoles.some(item => item.name === '零星采购管理人员')
                if (lx) params.productType = 0
                if (lg && lx) params.productType = null
            }
            this.tableLoading = true
            getBiddingRecordInfo(params)
                .then(res => {
                    this.paginationInfo.total = res.totalCount
                    this.paginationInfo.pageSize = res.pageSize
                    this.paginationInfo.currentPage = res.currPage
                    this.tableData = res.list
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        },
    },
}
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
