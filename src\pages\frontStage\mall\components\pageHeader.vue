<template>
  <div class="pageHeader">
    <!-- 导航条 -->
    <div style="background-color: #d9272c;display: flex;justify-content: center;">
      <div class="menuList dfa">
        <div class="fl dfa" @click="currentTab = 0" @mouseover="showFl_left = true">
          <img src="../../../../assets/images/img/a1.png" alt="" />
          <span class="ml20">产品分类</span>
        </div>
        <template v-for="(item, i) in tabs">
          <div v-show="item.show !== false" class="menuItem" @click="
            item.dropdownItems && item.dropdownItems.length > 0
              ? (currentTab = currentTab)
              : (currentTab = i)
            " :style="{ backgroundColor: i === currentTab ? activeColor : '' }" :key="i">
            {{ item.columnName }}
            <div class="dropdown-content" v-if="item.dropdownItems && item.dropdownItems.length > 0">
              <a v-for="item1 in item.dropdownItems" :key="item1.path || item1.name" href="#"
                @click.stop="headerItemM(item1, i)">
                {{ item1.name }}
              </a>
            </div>
          </div>
        </template>
      </div>
    </div>
    <!-- 分类 -->
    <div class="flBox" v-if="showFl_left" :class="{ 'flBox--expanded': showFl_right }">
      <div class="flContent df" @mouseleave="close">
        <div class="flContent_left" v-show="showFl_left">
          <div @click="onTreeClick(item)" @mouseover="switch1(i)" :style="{
            background:
              flBox_left_current === i ? 'rgba(232, 240, 249, 1)' : '',
          }" class="item dfb" v-for="(item, i) in flBox_left_list" :key="i">
            <div class="dfa">
              <img src="@/assets/images/img/a2.png" alt="" />
              <div :style="{ color: flBox_left_current === i ? activeColor : '' }" class="ml20">
                {{ item.className }}
              </div>
            </div>
            <i :style="{ color: flBox_left_current === i ? activeColor : '' }" class="el-icon-arrow-right"></i>
          </div>
        </div>
        <div class="flContent_right" v-if="showFl_left && showFl_right && flBox_right_list.length > 0">
          <div class="item df" v-for="(item, i) in flBox_right_list" :key="i">
            <div style="cursor: pointer" @click="onTreeClick(item)" class="title">
              {{ item.className }}
            </div>
            <img src="@/assets/images/img/a9.png" alt="" />
            <div class="taglist">
              <div @click="onTreeClick(item1)" v-for="(item1, j) in item.arr" :key="j">
                {{ item1.className }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 轮播图 -->
    <div class="Lbt relative" v-if="showFl_left">
      <el-carousel arrow="always" v-show="currentTab === 0" :interval="3000" height="400px"
        indicator-position="outside">
        <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
          <el-image style="width: 100%; height: 100%" :src="item.pictureUrl
            ? item.pictureUrl
            : require('@/assets/images/img/queshen3.png')
            " fit="cover"></el-image>
        </el-carousel-item>
      </el-carousel>

      <!-- 模块容器放在轮播组件外部，确保不随图片切换 -->
      <!-- <div class="carousel-modules fixed-bottom" :class="{ 'fixed-bottom--top': !showFl_right }">
        <div class="carousel-module">路桥工程</div>
        <div class="carousel-module">房建工程</div>
        <div class="carousel-module">市政工程</div>
        <div class="carousel-module">轨道交通</div>
      </div> -->
      <div class="carousel-modules fixed-bottom" :class="{ 'fixed-bottom--top': !showFl_right }">
        <div v-for="(item, index) in middleItems" :key="index">

          <el-image style="max-width: 340px; max-height: 100px;" class="carousel-module"
            @click="goToLink(item.pictureLinkAddress)" :src="item.pictureUrl">
          </el-image>
        </div>
        <!-- <el-image
          class="carousel-module"
          @click="goToLink('http://www.scrbc.com.cn/')"
          :src="require('@/assets/images/luqiao-gongcheng.png')"
        >
        </el-image>
        <el-image
          class="carousel-module"
          @click="goToLink('http://www.scrbc.com.cn/')"
          :src="require('@/assets/images/fangjian-gongcheng.png')"
          >路桥工程</el-image
        >
        <el-image
          class="carousel-module"
          @click="goToLink('http://www.scrbc.com.cn/')"
          :src="require('@/assets/images/shizheng0gongcheng.png')"
          >路桥工程</el-image
        >
        <el-image
          class="carousel-module"
          @click="goToLink('http://www.scrbc.com.cn/')"
          :src="require('@/assets/images/guidao-jiaotong.png')"
          >路桥工程</el-image
        > -->
      </div>
    </div>
  </div>
</template>

<script>
import { getCategoryTree } from '@/api/frontStage/productCategory'
import { getCategoryColumns } from '@/api/frontStage/floor'
import { getListAdPicture } from '@/api/frontStage/adPicture'
// import { getPublishList } from '@/api/platform/content/adImg'
export default {
    name: 'pageHeader',
    props: {
        tabs: Array,
        carouselItems: Array,
        middleItems: Array,
        pageType: String
    },
    data () {
        return {
            activeColor: '#F6353B',
            backUrl: null,
            currentTab: 0,
            showImg: false,
            headerItem: {},
            menuList: [],
            showFl_left: true,
            showFl_right: false,
            flBox_left_current: -1,
            enableHoverShowNav: false,
            // 分类
            flBox_left_list: [],
            flBox_right_list: [],
            init: {
                productType: 0,
                mallType: 0,
            },
        }
    },
    watch: {
        currentTab (val) {
            this.showFl_left = val === 0
            this.enableHoverShowNav = val !== 0
            this.$emit('onTabChange', val)
        },
        headerItem (val) {
            this.$emit('onTabItemChange', val, this.currentTab)
        },
    },
    computed: {
        dynamicLength () {
            return this.tabs.filter(item => {
                return typeof item.columnId === 'string'
            }).length
        },
    },
    created () {
        if (this.$route.query.BD === 1) this.currentTab = 3
        this.getListAdPictureM()
        // 获取分类
        this.getProductCategory()
        // 获取栏目
        this.getCategoryColumnsM()

    // this.getPublishListM()
    },
    // mounted () {
    //     this.handlePageType()

    // },
    methods: {
    // handlePageType () {
    //     if(this.pageType === 'noticeType') {
    //         this.currentTab = 5
    //     }
    // },
    // getPublishListM () {
    //     getPublishList({ useType: 2 }).then(res => {
    //         //轮播图
    //         const images01Urls = res.filter(item => {
    //             return item.usePositioning != null && item.usePositioning === 1
    //         })
    //         this.carouselItems = images01Urls

        //         //中部广告
        //         const images02 = res.filter(item => {
        //             return item.usePositioning != null && item.usePositioning === 3
        //         })
        //         this.middleItems = images02

        //         //底部广告
        //         const images03 = res.data.list.filter(item => {
        //             return item.usePositioning != null && item.usePositioning === 2
        //         })
        //         console.log(images03)
        //     })
        // },
        goToLink (url) {
            window.open(url, '_blank')
        },
        setActiveTab (index) {
            this.currentTab = index
        },
        headerItemM (item, index) {
            this.currentTab = index
            this.headerItem = item
        },
        getListAdPictureM () {
            getListAdPicture({ useType: 2, size: 1 }).then(res => {
                if (res.length !== 0) this.backUrl = res[0].pictureUrl
            })
        },
        // 树点击
        onTreeClick ({ classId, classPath }) {
            let routeData = this.$router.resolve({
                path: '/mFront/productList',
                query: { classId, classPath: classPath.replaceAll('/', ' > ') },
            })
            window.open(routeData.href, '_blank')
        },
        // 获取栏目
        getCategoryColumnsM () {
            getCategoryColumns({ size: 0 }).then(res => (this.menuList = res))
        },
        // 获取分类
        getProductCategory () {
            let parses = {
                productType: this.init.productType,
                mallType: this.init.mallType,
            }
            getCategoryTree(parses).then(res => {
                this.flBox_left_list = res
                this.flBox_right_list = []
            })
        },
        // 分类悬浮
        switch1 (i) {
            this.flBox_left_current = i
            this.getRightCateGory() // 渲染分类右边
            this.showFl_right = this.flBox_right_list.length !== 0
        },
        // 获取3级分类
        getRightCateGory () {
            this.flBox_right_list = []
            let arr = this.flBox_left_list[this.flBox_left_current]
            if (arr.children != null) {
                arr.children.forEach(t2 => {
                    let obj = {}
                    obj.classId = t2.classId
                    obj.className = t2.className
                    obj.classPath = t2.classPath
                    if (t2.children == null) {
                        obj.arr = []
                    } else {
                        obj.arr = []
                        t2.children.forEach(t => {
                            let t3 = {}
                            t3.classId = t.classId
                            t3.className = t.className
                            t3.classPath = t.classPath
                            obj.arr.push(t3)
                        })
                    }
                    this.flBox_right_list.push(obj)
                })
            }
        },
        close () {
            this.enableHoverShowNav ? (this.showFl_left = false) : ''
            this.showFl_right = false
            this.flBox_left_current = -1
        },
    },
}
</script>

<style scoped lang="scss">
.pageHeader {
  width: 100%;
  position: relative;

  .menuList {
    width: 1292px;display: inline-flex;
    //margin: 0 auto;
    height: 60px;
    color: #fff;

    .fl {
      width: 260px;
      background: rgba(0, 0, 0, 0.5);
      height: 100%;
      padding-left: 20px;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
      }

      span {
        font-size: 18px;
        font-weight: 500;
        color: rgba(255, 255, 255, 1);
      }
    }

    .menuItem {
      min-width: 172px;
      height: 100%;
      padding: 0 30px;
      font-size: 16px;
      text-align: center;
      font-weight: 500;
      line-height: 60px;
      color: #fff;
      cursor: pointer;
      position: relative;
    }

    .menuItem:hover {
      background-color: #f6353b;

      .dropdown-content {
        display: inline-block;
      }
    }
  }

  .flBox {
    width: 100%;
    position: absolute;
    top: 60px;
    left: 0;
    height: 450px;
    z-index: 1000;

    &.flBox--expanded {
      z-index: 10000;
    }

    .flContent {
      max-width: 1326px;
      //width: 260px;
      //padding-right: 1066px;
      margin-left: 292px;
      margin-left: calc(50% - 646px);

      .flContent_left {
        width: 260px;
        height: 450px;
        max-height: 450px;
        background: rgba(0, 0, 0, 0.6);
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        .item {
          cursor: pointer;
          height: 60px;
          padding: 0 20px;

          img {
            width: 24px;
            height: 24px;
          }

          .dfa {
            div {
              color: #fff;
              font-size: 18px;
            }
          }

          i {
            color: #fff;
          }
        }
      }

      .flContent_right {
        padding: 30px;
        background: #fff;
        overflow: auto;
        flex: 1;
        border: 1px solid rgba(33, 110, 198, 1);

        .item {
          width: 100%;
          margin-bottom: 16px;

          img {
            width: 14px;
            height: 14px;
            margin: 4px 15px 0;
            flex-shrink: 0;
          }

          .title {
            font-size: 14px;
            flex-shrink: 0;
          }

          .taglist {
            width: 100%;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            // padding-bottom: 16px;
            border-bottom: 1px rgba(204, 204, 204, 1) dashed;

            div {
              font-size: 14px;
              font-weight: 400;
              color: rgba(51, 51, 51, 1);
              padding: 0 10px;
              line-height: 18px;
              border-left: solid 1px rgba(204, 204, 204, 1);
              margin-bottom: 12px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  // 轮播图容器样式
  .Lbt {
    position: relative;
    height: 450px;

    // margin-top: 100px;
    // 固定底部的模块容器
    .fixed-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;

      &.fixed-bottom--top {
        z-index: 10001;
      }
    }

    .carousel-modules {
      display: flex;
      justify-content: center;
      // padding: 10px 0;
    }

    .carousel-module {
      // width: 260px;
      height: 100px;
      // margin: 0 30px;
      // background-color: rgba(255, 255, 255, 0.5); /* 半透明白色背景 */
      background: rgba(0, 0, 0, 0.6);
      // border: 1px solid #000; /* 黑色边框 */
      // border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 34px;
      font: bold;
      // color: #000;
      color: #fff;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #f6353b;
        color: #fff;
        border-color: #f6353b;
        /* 悬停时边框颜色与背景一致 */
      }
    }
  }
}
</style>

<style scoped>
.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.el-carousel :deep(.el-carousel__arrow) {
  display: block !important;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  z-index: 999;
}

.dropdown-content {
  left: 0;
  top: 60px;
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 172px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 10002;
}

.dropdown-content a {
  color: black;
  /* padding:-6px 16px; */
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {
  background-color: #edf0f3;
}
</style>