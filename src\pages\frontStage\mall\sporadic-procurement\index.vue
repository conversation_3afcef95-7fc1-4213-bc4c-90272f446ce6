<!-- 零星采购专区 -->
<template>
    <div class="root" v-loading="showLoading">
        <div class="main">
            <template v-if="ItemData.name == '平台自营'">
                <div class="titleBox dfb" >
                    <div class="dfa left">
                        <div >合作商家</div>
                        <!-- <div v-else >热门企业</div> -->
                        <span class="ml10">精细挑选 为您选购</span>
                    </div>
                    <div class="right" @click="openWindowTab({path: '/mFront/shopList', query: { currentTab: currentTab }})">
                        更多<i class="el-icon-arrow-right"></i>
                    </div>
                </div>
                <div class="qyList df">
                    <div class="mb20 dfa pointer" v-for="item in companiesList" :key="item.shopId" @click="openWindowTab({path: '/mFront/shopIndex',query: {shopId: item.shopId,currentTab: currentTab}})">
                        <el-image
                            style="width: 55px;height: 55px;margin-right: 15px;"
                            :src="item.shopImg ? imgUrlPrefixAdd + item.shopImg : require('@/assets/images/img/queshen5.png')"
                            fit="cover"
                            lazy
                        />
                        <span>{{ item.shopName }}</span>
                    </div>
                </div>
            </template>
            <div v-for="(item, i) in floorGoodsList" :key="i">
                <div class="titleBox dfb">
                    <div class="dfa left">
                        <div>{{ item.floorName }}</div>
                        <span class="ml10">{{ item.floorNameText }}</span>
                    </div>
                    <div class="right" @click="openWindowTab({ path: '/mFront/productList', query: { classId: item.classId, classPath: item.className }})">
                        更多<i class="el-icon-arrow-right"></i>
                    </div>
                </div>
                <div class="goodList df">
                    <div class="floorImg">
                        <el-image
                            style="width: 250px;height: 620px;"
                            :src="item.backgroundUrl ? imgUrlPrefixAdd + item.backgroundUrl : require('@/assets/images/default_bgc.png')"
                            fit="cover"
                            lazy
                        />
                        <el-image
                            style="width: 190px;height: 250px;margin-left: -95px;margin-top: -50%;z-index: 1;top: 50%;left: 50%;"
                            :src="item.imgUrl ? imgUrlPrefixAdd + item.imgUrl : require('@/assets/images/img/queshen5.png')"
                            fit="cover"
                            lazy
                        />
                    </div>
                    <div class="df">
                        <div class="goodItem mb20" v-show="item.goodsVOS" v-for="(item2, i) in item.goodsVOS"
                             :key="i">
<!--                            @click="openWindowTab({path:'/mFront/productDetail',query:{productId: item2.productId}})"-->
<!--                            <el-image
                                style="width: 250px;height: 200px;"
                                :src="item2.productMinImg ? imgUrlPrefixAdd + item2.productMinImg : require('@/assets/images/img/queshen5.png')"
                                fit="cover"
                                lazy
                            />
                            <div class="name textOverflow1" style="margin-top: 12px">{{ item2.productName }}</div>
                            <div class="type textOverflow2">{{ item2.skuName || '   ' }}</div>
                            <div class="price">￥{{ item2.sellPrice.toFixed(2) }}</div>
                            <div :title="item2.shopName" class="shopName textOverflow1" v-if="item2.shopName" @click.stop="goToShop(item2.shopId)">
                                {{ item2.shopName }}
                            </div>-->
                            <GoodsItem :itemData="item2"></GoodsItem>
                        </div>
                    </div>
                </div>
            </div>
            <el-empty v-show="floorGoodsList.length === 0" style="height: 500px;" :image="emptyIcon" description="没有商品"></el-empty>
        </div>
    </div>
</template>
<script>
import { getIndexShopList, getIndexSupplierList } from '@/api/w/indexShop'
import GoodsItem from '@/components/goods-item.vue'

export default {
    name: 'mallIndex',
    components: { GoodsItem },
    props: {
        ItemData: {},
        floorGoodsList: [],
    },
    data () {
        return {
            emptyIcon: require('@/assets/images/ico_kong.png'),
            showLoading: false,
            showBidding: false,
            showMass: false,
            showNotifications: false,
            currentTab: 0,
            tabsArr: [],
            completeData: [],
            companiesList: [],
            dynamicLength: 0,
            tabItem: {},
        }
    },

    watch: {
        ItemData (val) {
            console.log('ItemData', val)
            console.log('floorGoodsList', this.floorGoodsList)
        },
    },
    created () {
        this.getIndexShopListM()
        // this.getCategoryColumnsM()
    },
    methods: {
        goToShop (shopId) {
            this.openWindowTab({ path: '/mFront/shopIndex', query: { shopId } })
        },
        getIndexShopListM () {
            if (this.currentTab == 3) {
                let params = {
                    limit: 5,
                    page: 0,
                }
                getIndexSupplierList(params).then(res => {
                    this.companiesList.concat(res)
                })
            } else {
                getIndexShopList({ size: 12 }).then(res => {
                    if(res && res.length > 0) {
                        Array.prototype.push.apply(this.companiesList, res)
                    }
                    let params = {
                        limit: 4,
                        page: 0,
                    }
                    getIndexSupplierList(params).then(res => {
                        if(res.list && res.list.length > 0) {
                            Array.prototype.push.apply(this.companiesList, res.list)
                        }
                    })
                })
            }
        },
        // 切换tab时
        onTabChange (i) {
            var oldTab = this.currentTab
            this.currentTab = i
            // if (i > this.completeData.length - 1) return
            if(i == 0) {
                this.floorGoodsList = this.completeData[i].floorVOS
            }
            if (this.currentTab == 3) {
                this.getIndexShopListM()
            } else if (oldTab == 3 && this.currentTab != 3) {
                this.getIndexShopListM()
            }
        },
        onTabItemChange (item, index) {
            this.currentTab = index
            this.tabItem = item
            if(item.columnName == '平台自营') {
                this.floorGoodsList = this.completeData[1].floorVOS
            }
            if(item.columnName == '优选商品') {
                this.floorGoodsList = this.completeData[2].floorVOS
            }
        }
    },
}
</script>
<style scoped lang="scss">
@import "../../../../assets/css/floor.scss";
div {
    line-height: 1;
}

.root {
    width: 100%;
    padding-bottom: 20px;
    background: #f5f5f5;
}

.main {
    width: 1326px;
    margin: 0 auto;

}
.goodItem {
    position: relative;
    .price {
        width: 100%;
        text-align: right;
        padding-right: 12px;
    }
}
.shopName {
    position: absolute;
    left: 12px;
    max-width: 90px;
    bottom: 10px;
    font-size: 14px;
    color: gray;
}
</style>
