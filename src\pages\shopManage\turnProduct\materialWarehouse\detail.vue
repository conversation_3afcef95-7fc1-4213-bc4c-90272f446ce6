<template>    <!-- 自营店新增、上架周材商品 详情 -->
  <div class="e-form">
    <BillTop @cancel="handleClose"/>
    <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
      <el-tabs v-model="tabsName" tab-position="left" @tab-click="onChangeTab">
        <el-tab-pane ref="Goods" :disabled="clickTabFlag" label="物资" name="baseInfo">

        </el-tab-pane>
        <el-tab-pane ref="ListingHistory" :disabled="clickTabFlag" label="上架历史" v-if="viewType != 'add'" name="sjHistory">

        </el-tab-pane>
        <el-tab-pane ref="AuditRecords" :disabled="clickTabFlag" label="审核记录" name="auditRecords" v-if="addForm.formData.jcState != null">
        </el-tab-pane>
        <div id="tabs-content">
          <div id="baseInfCon" class="con">
            <div id="baseInfo" class="tabs-title">物资</div>
            <!--新增-->
            <div v-if="showForm" class="form">
              <el-form
                  ref="formEdit" :model="addForm.formData" :rules="formRules"
                  class="demo-ruleForm" label-width="200px"
              >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="物资名称：" prop="relevanceName">
                        <el-input
                            disabled
                            v-model="addForm.formData.relevanceName"
                            placeholder="请选择物资"
                        ></el-input>
                        <el-button size="mini" type="primary" @click="importDeviceSelect" :disabled="IsProductSource">选择
                        </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-show="this.viewType != 'add'">
                        <el-form-item label="商品编号：">
                        <el-input disabled  v-model="addForm.formData.serialNum"></el-input>
                        </el-form-item>
                    </el-col>
                    </el-row>
                    <el-row>
                    <el-col :span="12">
                        <el-form-item label="分类：" prop="classId">
                        <category-cascader
                            customStyle="width: 100%"
                            style="width: 100%"
                            :disabled="true"
                            :catelogPath="addForm.formData.classPath"
                            :classId.sync='addForm.formData.classId'
                            :classPath.sync="addForm.formData.classPath" :productType="0" :is-lc="3"
                            @change="resetRelevanceAndBrand"
                        ></category-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="税率（%）：" prop="taxRate">
                            <el-input :disabled="IsProductSource" v-model="addForm.formData.taxRate" :step="0.01" type="number" placeholder="填写税率"
                                    @change="writeTaxRate"/>
                        </el-form-item>
                    </el-col>
                    </el-row>
                    <el-row>
                    <el-col :span="12">
                        <el-form-item label="品牌：" prop="brandName">
                        <el-input
                            v-model="addForm.formData.brandName" disabled placeholder="请选择品牌"
                        ></el-input>
                        <el-button size="mini" type="primary" @click="brandDialog" :disabled="IsProductSource">选择</el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="排序：">
                        <el-input
                            :disabled="IsProductSource"
                            v-model="addForm.formData.shopSort" clearable oninput="if(value.length > 10)value = value.slice(0, 10)"
                            placeholder="请输入排序"
                            type="number"
                        ></el-input>
                        </el-form-item>
                    </el-col>
                    </el-row>
                    <el-row>
                    <el-col :span="12">
                        <el-form-item label="规格：" prop="skuName">
                        <el-input v-model="addForm.formData.skuName" disabled clearable placeholder="请输入规格" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="计量单位" prop="unit">
                        <el-select
                            disabled
                            v-model="addForm.formData.unit" filterable placeholder="请选择计量单位"
                            @change="numUnitChange"
                            :popper-append-to-body="false"
                        >
                            <el-option
                                v-for="item in addForm.numUnitOptions" :key="item.value"
                                :label="item.label" :value="item.label"
                            >
                            </el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                    </el-row>
                    <el-row>
                    <el-col :span="12">
                        <el-form-item label="库存：" prop="stock">
                        <el-input
                            :disabled="IsProductSource"
                            v-model="addForm.formData.stock" clearable oninput="if(value.length > 16)value = value.slice(0, 16)"
                            placeholder="请输入库存"
                            type="number"
                        ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="材质：" prop="productTexture">
                        <el-input :disabled="IsProductSource" v-model="addForm.formData.productTexture" clearable placeholder="请输入材质"/>
                        </el-form-item>
                    </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12" v-if="userInfo.isBusiness == 1 && (rowData.viewType == 'add' || addForm.formData.supplierSubmitState === 0)">
                            <el-form-item label="采购进价：" prop="purchasePrice">
                                <el-input
                                    :disabled="IsProductSource"
                                    v-model="addForm.formData.purchasePrice" clearable
                                    placeholder="请输入采购进价"
                                    type="number"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="年化率（%）：" prop="annualizedRate">
                                <el-input
                                    :disabled="IsProductSource"
                                    v-model="addForm.formData.annualizedRate" clearable
                                    placeholder="请输入年化率"
                                    @change="arChange"
                                    :step="0.01"
                                    type="number"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="userInfo.isBusiness == 1">
                            <el-col :span="12" v-if="addForm.formData.priceType == 0">
                                <el-form-item label="账期：" prop="accountPeriod">
                                  <el-select :disabled="IsProductSource" v-model="addForm.formData.accountPeriod" placeholder="请选择账期" multiple :popper-append-to-body="false">
                                    <el-option
                                      v-for="item in accountPeriodOptions"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value">
                                    </el-option>
                                  </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="价格类型：" prop="priceType">
                                  <el-radio-group :disabled="IsProductSource" v-model="addForm.formData.priceType" @change="changePriceType">
                                    <el-radio :label="0">一口价</el-radio>
                                    <el-radio :label="1">参考价</el-radio>
                                  </el-radio-group>
                                </el-form-item>
                            </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12" v-if="addForm.formData.supplierSubmitState == 3">
                            <el-form-item label="加成率：">
                                <el-radio v-model="addForm.formData.markUp" label="1">固定加成率</el-radio>
                                <el-radio v-model="addForm.formData.markUp" label="2">自定义加成率</el-radio>
                                <el-input v-model="addForm.formData.markUpNum"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 撮合模式、自营模式参考价 没有先款后货 -->
                    <div v-if="userInfo.isBusiness !== 1 || addForm.formData.priceType == 1">
                      <el-row style="margin-left: 10%">
                        <el-button :disabled="IsProductSource" type="primary" @click="addRegion">新增区域</el-button>
                      </el-row>
                      <div style="margin: 10px 0;">
                        <div>
                          <div v-for="(item, index) in addForm.formData.regionTableData" :key="item.index">
<!--                              参考价全区域-->
                            <el-row>
                              <el-col :span="12">
                                <el-form-item :label="item.regionName">
                                  <div v-if="item.regionName === '全区域'" style="width: 100%">
                                    <el-select :disabled="IsProductSource" v-model="item.detailAddress" multiple placeholder="请选择" @change="handleAddressChange1(item)" :popper-append-to-body="false">
                                      <el-option
                                        v-for="item in economizeData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                      >
                                      </el-option>
                                    </el-select>
                                  </div>
                                  <div v-else style="width: 100%">
                                    <el-cascader
                                        :disabled="IsProductSource"
                                      style="width: 100%"
                                      v-model="item.selectAddressOptions"
                                      @change="handleAddressChange(item)"
                                      :options="marketData"
                                      :props="{ multiple: true, checkStrictly: true }"
                                      :append-to-body="false"
                                      clearable>
                                    </el-cascader>
                                  </div>
                                </el-form-item>
                              </el-col>
                              <el-col :span="5">
                                <el-form-item label="销售价格（含税）：" :prop="'regionTableData.' + index + '.taxInPrice'" :rules="formRules.taxInPrice" label-width="155px">
                                  <el-input
                                      :disabled="IsProductSource"
                                    v-model="item.taxInPrice"
                                    style="width: 100%"
                                    type="number"
                                    @change="taxInPriceChange(item.index)"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                              <el-col :span="5">
                                <el-form-item label="销售价格（不含税）：" label-width="155px">
                                  <el-input
                                      :disabled="IsProductSource"
                                    v-model="item.price"
                                    type="number"
                                    style="width: 100%"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                              <el-col :span="1">
                                <el-form-item v-if="item.regionName !== '全区域'">
                                  <el-button :disabled="IsProductSource" icon="el-icon-delete" type="text" size="mini" @click="removeOneParamsData(item)"></el-button>
                                </el-form-item>
                              </el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                <div v-if="userInfo.isBusiness === 1 && addForm.formData.priceType == 0 && addForm.formData.accountPeriod.length > 0">
                  <div class="tabContent">
                    <div v-for="item in addForm.formData.accountPeriod" :key="item" class="accountPeriodTabs">
                      <div :class="currentTab === item ? 'tabTitle' : 'tabTitleNoSelect'" @click="getCurrentTab(item)">{{item + "个月账期信息"}}</div>
                    </div>
                  </div>
                  <el-row style="margin: 1% 0 1% 9%">
                    <el-button :disabled="IsProductSource" type="primary" @click="addAccountPeriodRegion" style="line-height: 20px;">新增区域</el-button>
                  </el-row>
                  <div style="margin: 10px 0;">
                    <div>
                      <div v-for="(item, index) in addForm.formData.currentRegionTableData" :key="item.index">
<!--                          一口价全区域-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item :label="item.regionName">
                              <div v-if="item.regionName === '全区域'" style="width: 100%">
                                <el-select :disabled="IsProductSource" v-model="item.detailAddress" multiple placeholder="请选择" @change="handleAddressChange2(item)" :popper-append-to-body="false">
                                  <el-option
                                    v-for="item in economizeData"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                  >
                                  </el-option>
                                </el-select>
                              </div>
                              <div v-else style="width: 100%">
                                <el-cascader
                                  style="width: 100%"
                                  :disabled="IsProductSource"
                                  v-model="item.selectAddressOptions"
                                  @change="handleAddressChange3(item)"
                                  :options="marketData"
                                  :props="{ multiple: true, checkStrictly: true }"
                                  :append-to-body="false"
                                  clearable>
                                </el-cascader>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="5">
                            <el-form-item label="先款后货：" label-width="150px" :prop="'currentRegionTableData.' + index + '.payBeforeDelivery'" :rules="formRules.payBeforeDelivery">
                              <el-input
                                  :disabled="IsProductSource"
                                v-model="item.payBeforeDelivery"
                                style="width: 100%"
                                type="number"
                                @change="payBeforeDeliveryChange(item.index)"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="5">
                            <el-form-item label="销售价格（含税）：" :prop="'currentRegionTableData.' + index + '.taxInPrice'" :rules="formRules.taxInPrice" label-width="150px">
                              <el-input
                                  :disabled="IsProductSource"
                                v-model="item.taxInPrice"
                                style="width: 100%"
                                type="number"
                                @change="taxInPriceChange1(item.index)"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="5">
                            <el-form-item label="销售价格（不含税）：" :prop="'currentRegionTableData.' + index + '.price'" :rules="formRules.price" label-width="150px">
                              <el-input
                                  :disabled="IsProductSource"
                                v-model="item.price"
                                style="width: 100%"
                                type="number"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="1">
                            <el-form-item v-if="item.regionName !== '全区域'">
                              <el-button :disabled="IsProductSource" icon="el-icon-delete" type="text" size="mini" @click="removeOneParamsData1(item)"></el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                  </div>
                </div>
                <el-row>
                  <el-col :span="24">
                    <el-form-item
                        class="uploader" label="物资主图（推荐654x490）：" prop="adminFile" required
                    >
                      <el-upload
                          :disabled="IsProductSource"
                          ref="adminFileRef"
                          action="fakeaction"
                          :auto-upload="false"
                          :show-file-list="false"
                          :before-upload="checkFileSize"
                          :on-change="(file) => setCropperImg(file, 0, [654, 490])"
                      >
                        <template v-if="mainImg">
                          <div class="cover dfc" @click.stop="()=>{}">
                            <i class="el-icon-zoom-in" @click.stop="handleImgPreview('mainImg')"></i>
                            <i v-if="!IsProductSource" class="el-icon-delete" @click.stop="delImg('mainImg')"></i>
                          </div>
                          <img :src="mainImg" alt="">
                        </template>
                        <i v-else class="el-icon-plus"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item
                        class="uploader" label="物资图片（推荐654x490）：" prop="productFiles" required
                    >
                      <el-upload
                          :disabled="IsProductSource"
                          ref="productFileRef"
                          action="fakeaction"
                          list-type="picture-card"
                          :auto-upload="false"
                          :multiple="true"
                          :limit="uploadMax"
                          :file-list="addForm.formData.productFiles"
                          :before-upload="checkFileSize"
                          :on-change="(file, fileList) => { fileList.pop(); setCropperImg(file, 2, [654, 490])}"
                          :on-exceed="handleExceed"
                          :on-remove="productFileRemove"
                          :on-preview="handlePictureCardPreview"
                      >
                        <i class="el-icon-plus"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item
                        class="uploader" label="物资小图 （推荐250x200）：" prop="minFile" required
                    >
                      <el-upload
                          :disabled="IsProductSource"
                          ref="minFileRef"
                          action="fakeaction"
                          :auto-upload="false"
                          :show-file-list="false"
                          :before-upload="checkFileSize"
                          :on-change="(file) => setCropperImg(file, 1, [250, 200])"
                      >
                        <template v-if="smallImg">
                          <div class="cover dfc" @click.stop="()=>{}">
                            <i class="el-icon-zoom-in" @click.stop="handleImgPreview('minImg')"></i>
                            <i v-if="!IsProductSource" class="el-icon-delete" @click.stop="delImg('minImg')"></i>
                          </div>
                          <img :src="smallImg" alt="">
                        </template>
                        <i v-else class="el-icon-plus"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="物资描述：" prop="productDescribe">
                      <editor :disabled="IsProductSource" v-model="addForm.formData.productDescribe" @blur="onEditorBlur"></editor>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
            <div id="baseInfCon" class="con" v-if="viewType != 'add'">
            <div id="sjHistory" class="tabs-title" style="margin-top: 30px">上架历史</div>
            <div class="e-table">
              <el-button type="primary" @click="exportLog" class="btn-blue">
                导出
              </el-button>
              <el-table
                  @row-click="handleCurrentInventoryClick" ref="tableRef" class="table"
                  :height="rightTableHeight" :data="tableData" border highlight-current-row
                  v-loading="tableLoading"
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="40"></el-table-column>
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <!-- 名称 -->
                <el-table-column label="操作" width="" prop="operation">
                  <template v-slot="scope">
                    <a @click="deleteShelfLog(scope.row.id)" style="color: red;cursor: pointer;">删除记录</a>
                  </template>
                </el-table-column>
                <el-table-column label="名称" prop="productName">
                </el-table-column>
                <el-table-column label="操作类型" prop="operationType">
                  <template v-slot="scope">
                    <el-tag v-if="scope.row.operationType == 0" type="success">删除</el-tag>
                    <el-tag v-if="scope.row.operationType == 1" type="success">上架</el-tag>
                    <el-tag v-if="scope.row.operationType == 2" type="success">下架</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作时间" prop="operationTime">
                </el-table-column>
                <el-table-column label="库存" prop="stock">
                </el-table-column>
                <el-table-column label="排序" prop="sort">
                </el-table-column>
              </el-table>
              <!-- 分页器 -->
              <Pagination
                  :total="pages.totalCount" :pageSize.sync="pages.pageSize"
                  :currentPage.sync="pages.currPage"
                  @currentChange="currentChange" @sizeChange="sizeChange"/>
            </div>
          </div>
          <div id="auditRecordsCon" class="con"  v-if="addForm.formData.jcState != null">
            <div id="auditRecords" class="tabs-title">审核记录</div>
            <div class="e-table">
              <el-table
                ref="auditTableRef"
                class="table"
                :data="auditTableData"
                border
                highlight-current-row
                v-loading="auditTableLoading"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="审核类型" prop="auditType">
                </el-table-column>
                <el-table-column label="审核人" prop="auditName">
                </el-table-column>
                <el-table-column label="人员类型" prop="type">
                </el-table-column>
                <el-table-column label="审核时间" prop="auditTime">
                </el-table-column>
                <el-table-column label="审核意见" prop="record" show-overflow-tooltip>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tabs>
    </div>
    <div class="buttons">
      <el-button v-if="viewType != 'add' &&(addForm.formData.state===2 || addForm.formData.state===0)"
                 type="success"
                 @click="updateStateBatch(3,'保存并上架')">保存并上架
      </el-button>
      <el-button v-if="viewType != 'add' &&addForm.formData.state===1"
                 type="danger"
                 @click="updateStateBatch(2,'下架')">下架
      </el-button>
      <el-button type="success" @click="submit">保存</el-button>
      <el-button @click="handleClose">返回</el-button>
    </div>
    <!--表格-->
    <el-dialog
        v-dialogDrag v-loading="brandTableLoading" :close-on-click-modal="false" :visible.sync="showBrandDialog"
        style="margin-left: 20%;" title="选择品牌" width="60%"
    >
      <div class="e-table box-right" style="background-color: #fff">
        <div class="top" style="height: 50px;">
          <div class="left">
            <el-input
                v-model="brand.keywords" placeholder="输入搜索关键字" style="width: 200px" type="text"
                @blur="getBrandTableData"
            >
              <img slot="suffix" :src="require('@/assets/search.png')" @click="getBrandTableData"/>
            </el-input>
          </div>
        </div>
        <el-table
            ref="tableRef" :data="brand.tableData" :max-height="$store.state.tableHeight" border class="table"
            highlight-current-row style="width: 100%" @row-click="handleCurrentClick"
        >
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="品牌名" prop="name" width="150"></el-table-column>
          <!--                    <el-table-column prop="logo" label="品牌logo" width="90"></el-table-column>-->
          <el-table-column label="介绍" prop="descript"></el-table-column>
          <el-table-column label="创建时间" prop="gmtCreate" width="160"></el-table-column>
          <el-table-column label="更新时间" prop="gmtModified" width="160"></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
                <Pagination
                    v-show="brand.tableData && brand.tableData.length > 0"
                    :currentPage.sync="brand.paginationInfo.currentPage"
                    :pageSize.sync="brand.paginationInfo.pageSize" :total="brand.paginationInfo.total"
                    @currentChange="getBrandTableData" @sizeChange="getBrandTableData"
                />
                <el-button style="margin-top: 20px" @click="showBrandDialog = false">取消</el-button>
            </span>

    </el-dialog>
    <!--选择物资库-->
    <el-dialog
        v-dialogDrag v-loading="inventoryTableLoading" :close-on-click-modal="false" :visible.sync="showDeviceDialog"
        style="margin-left: 20%;" title="选择物资库" width="70%"
    >
      <div class="e-table" style="background-color: #fff">
        <div class="top" style="height: 50px; padding-left: 10px">
          <div class="left">
            <el-input
                v-model="inventory.keyWord" clearable placeholder="输入搜索关键字" type="text"
                @blur="getDeviceInventory"
            >
              <img slot="suffix" :src="require('@/assets/search.png')" @click="getDeviceInventory"/>
            </el-input>
          </div>
        </div>
        <el-table
            ref="tableRef" :data="inventory.tableData" :max-height="$store.state.tableHeight" border class="table"
            highlight-current-row style="width: 100%" @row-click="handleCurrentInventoryClick"
        >
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="编号" prop="billNo" width="200"></el-table-column>
          <el-table-column label="名称" prop="materialName" width="200"></el-table-column>
          <el-table-column label="规格型号" prop="spec"></el-table-column>
          <el-table-column label="类别名称" prop="className"></el-table-column>
          <el-table-column label="计量单位" prop="unit"></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
                <Pagination
                    v-show="inventory.tableData && inventory.tableData.length > 0"
                    :currentPage.sync="inventory.paginationInfo.currentPage"
                    :pageSize.sync="inventory.paginationInfo.pageSize"
                    :total="inventory.paginationInfo.total" @currentChange="getDeviceInventory"
                    @sizeChange="getDeviceInventory"
                />
                <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
            </span>
    </el-dialog>
    <el-dialog :visible.sync="imgPreviewDialog" title="图片预览">
      <img class="center mb20" style="display: block" :src="previewImg" alt="">
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <Cropper ref="cropperImage" @handleUploadSuccess="handleUploadSuccess"/>
  </div>
</template>

<script>
import { regionData, TextToCode } from 'element-china-area-data'
import CategoryCascader from '@/components/category-cascader'
import editor from '@/components/quillEditor'
import Cropper from '@/components/cropper'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import $ from 'jquery'
import { addImgUrl, spliceImgUrl, throttle, toFixed } from '@/utils/common'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { getBrandPageList } from '@/api/shopManage/brand/brand'
import { uploadFile } from '@/api/platform/common/file'
import { getEnterpriseInfoTaxRate } from '@/api/platform/shop/shopManager'
import {
    createMaterial,
    getMaterialInfo,
    getShelfLogInfo,
    removeShelfLogInfo,
    updateMaterial,
    exportShelfLog
} from '@/api/shopManage/product/materialManage'
import { queryPageMaterialDtl } from '@/api/shopManage/product/prodcutInventory'
import { updateMaterialAndState, updateProductState } from '@/api/platform/product/materialManage'
import { getList } from '@/api/platform/system/systemParam'
export default {
    data () {
        return {
            //productSource等于2的，除加成率外全部禁用
            IsProductSource: false,
            //整个四川的市
            x: ' ',
            y: ' ',
            allSC: [],
            allSC2: [],
            allSCone: [],
            allSCone2: [],
            // 用于存储上一次的选项
            lastSelectedOptions: [],
            // 存储所有被选择的城市
            selectedCity: [],
            selectedCityone: [],
            // 存储所有城市选项数据
            allCityOptions: [],
            regionNameCounter: 1,
            regionTablePeriodIndex: 7,
            //账期1-6月
            regionTableIndex: 1,
            regionTableIndex1: 1,
            regionTableIndex2: 1,
            regionTableIndex3: 1,
            regionTableIndex4: 1,
            regionTableIndex5: 1,
            regionTableIndex6: 1,
            addressData: regionData, // 地址数据
            economizeData: [], //只有省数据
            marketData: [], //省市数据
            imgPreviewDialog: false,
            dialogVisible: false,
            previewImg: '',
            mainImg: '',
            smallImg: '',
            dialogImageUrl: '',
            uploadMax: 10,
            uploadType: null,
            // 数据加载
            formLoading: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            formRules: {
                relevanceName: [
                    { required: true, message: '请选择名称', trigger: 'change' },
                ],
                // productTexture: [
                //     { required: true, message: '请输入材质', trigger: 'blur' },
                // ],
                secondUnitNum: [
                    { required: true, message: '请输入单位换算的系数', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                payBeforeDelivery: [{ required: true, message: '请输入先款后货', trigger: 'change' }],
                taxInPrice: [{ required: true, message: '请输入销售价格', trigger: 'change' }],
                //price: [{ required: true, message: '请输入销售价格', trigger: 'change' }],
                secondUnit: [
                    { required: true, message: '请选择单位换算', trigger: 'blur' },
                ],
                // brandName: [
                //     { required: true, message: '请选择品牌', trigger: 'blur' },
                // ],
                productName: [
                    { required: true, message: '请输入物资名称', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' },
                ],
                productTexture: [
                    { required: true, message: '请输入材质', trigger: 'blur' },
                ],
                purchasePrice: [
                    { required: true, message: '请输入采购进价', trigger: 'blur' },
                ],
                annualizedRate: [
                    { required: true, message: '请输入年化率', trigger: 'blur' },
                ],
                taxRate: [
                    { required: true, message: '请输入税率', trigger: 'blur' },
                ],
                accountPeriod: [
                    { required: true, message: '请选择账期', trigger: 'blur' },
                ],
                priceType: [
                    { required: true, message: '请选择价格类型', trigger: 'blur' },
                ],
                settlePrice: [
                    { required: true, message: '请输入结算价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                stock: [
                    { required: true, message: '请输入库存', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,3})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                costPrice: [
                    { required: true, message: '请输入成本价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                unit: [
                    { required: true, message: '请选择计量单位', trigger: 'change' },
                ],
                originalPrice: [
                    { required: true, message: '请输入原价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                sellPrice: [
                    { required: true, message: '请输入销售价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                adminFile: [
                    { required: true, message: '请上传物资主图', trigger: 'blur' },
                ],
                productFiles: [
                    { required: true, message: '请上传物资图片', trigger: 'blur' },
                ],
                minFile: [
                    { required: true, message: '请上传物资小图', trigger: 'blur' },
                ],
                productDescribe: [
                    { required: true, message: '请输入描述', trigger: 'blur' },
                ],
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            pages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 20,
            },
            // 商品库
            inventory: {
                tableData: [],
                keyWord: null,
                classId: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 0,
                },
            },
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            uploadImgSize: 10, // 上传文件大小
            accountPeriodOptions: [{
                value: 1,
                label: '1个月账期'
            }, {
                value: 2,
                label: '2个月账期'
            }, {
                value: 3,
                label: '3个月账期'
            }, {
                value: 4,
                label: '4个月账期'
            }, {
                value: 5,
                label: '5个月账期'
            }, {
                value: 6,
                label: '6个月账期'
            }],
            currentTab: 2,
            addForm: {
                formData: {
                    productSource: null,
                    annualizedRate: null,
                    priceType: 0,
                    regionTableData: [
                        { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 2, taxInPrice: '', price: '', detailAddress: [] }
                    ],
                    currentRegionTableData: [
                        { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 1, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] }
                    ],
                    taxRate: null,
                    productType: 2,
                    relevanceName: null,
                    productName: null,
                    classId: null,
                    classPath: [],
                    productMinPrice: null,
                    brandId: null,
                    brandName: null,
                    shopSort: null,
                    skuName: null,
                    stock: 1,
                    unit: null,
                    productFiles: [],
                    minFile: [],
                    adminFile: [],
                    productDescribe: null,
                    productTexture: null,
                    accountPeriod: [2]
                },
                adminFileLength: 0,
                minFileLength: 0,
                numUnitOptions: [],
            },
            // 品牌数据
            brand: {
                classId: null,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableData: [],
            tableLoading: false,
            auditTableData: [],
            auditTableLoading: false,
            markUpNum: null,
        }
    },
    components: {

        Pagination,
        CategoryCascader,
        editor,
        Cropper
    },
    created () {
        this.getFixed()
        this.addForm.numUnitOptions = this.materialUnit
        this.rowData = this.$route.params.row
        if(this.$route.params.row.productSource == '2') {
            this.IsProductSource = true
        }
        if (this.rowData.viewType === 'add') {
            this.getEnterpriseInfoTaxRateM()
            this.viewType = 'add'
            if (this.rowData.classPath != null) {
                this.addForm.formData.classPath = this.rowData.classPath
                this.addForm.formData.classId = this.rowData.classPath[this.rowData.classPath.length - 1]
            }
            this.showForm = true
            if (this.userInfo.isBusiness === 1 && this.addForm.formData.priceType == 0) {
                this.addForm.formData.regionTableData = [
                    { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 1, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 2, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 2, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 3, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 3, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 4, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 4, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 5, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 5, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 6, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 6, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] }
                ]
                this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
            } else {
                this.addForm.formData.regionTableData = [
                    { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 2, taxInPrice: '', price: '', detailAddress: [] }
                ]
            }
            getList({ keywords: '账期年化利率' }).then(res => {
                this.addForm.formData.annualizedRate = Number(res.list[0].keyValue)
            })
            if (this.userInfo.isBusiness === 1) {
                this.addForm.formData.productSource = 3
            }else{
                this.addForm.formData.productSource = 1
            }
        } else {
            this.getMaterialInfo()
            this.getShelfLog()
        }
        this.getEconomizeAndMarketList()
        this.allSC2 = this.allSC
        this.allSCone2 = this.allSCone
    },
    mounted () {
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['materialUnit']),
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    watch: {
    // 处理是否有第二计量单位
        'isSecondUnit': {
            handler (val) {
                if (val === 0) {
                    this.addForm.formData.secondUnit = ''
                    this.addForm.formData.secondUnitNum = 0
                }
            }
        },
        // 'addForm.formData.secondUnitNum': {
        //     handler (val) {
        //         console.log(val)
        //         if(val === null) {
        //             this.addForm.formData.isSecondUnit = 1
        //         }else {
        //             this.addForm.formData.isSecondUnit = 0
        //         }
        //     }
        // },
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        //参考价删除多余区域
        removeOneParamsData (row) {
            const releasedCities = new Set()
            if (row.regionName === '全区域') {
                if (row.selectAddressOptionsAll) {
                    row.selectAddressOptionsAll.forEach(city => releasedCities.add(city))
                }
            }
            else {
                if (row.selectAddressOptions) {
                    row.selectAddressOptions.forEach(opt => {
                        const cityCode = Array.isArray(opt) ? opt[opt.length - 1] : opt
                        if (cityCode) releasedCities.add(cityCode)
                    })
                }
                else if (row.areaCode && typeof row.areaCode === 'string') {
                    const cleanCode = row.areaCode.replace(/[[]"]/g, '')
                    cleanCode.split(',').forEach(code => {
                        if (code) releasedCities.add(code)
                    })
                }
            }
            const index = this.addForm.formData.regionTableData.findIndex(
                item => item.index === row.index
            )
            if (index !== -1) {
                this.addForm.formData.regionTableData.splice(index, 1)
                this.addForm.formData.currentRegionTableData =
                    this.addForm.formData.regionTableData.filter(
                        item => item.accountPeriod === this.currentTab
                    )
                const occupiedCities = new Set()
                const currentPeriodRegions = this.addForm.formData.regionTableData.filter(
                    item => item.accountPeriod === this.currentTab
                )
                currentPeriodRegions.forEach(region => {
                    if (region.regionName !== '全区域') {
                        // 处理数组格式
                        if (region.selectAddressOptions) {
                            region.selectAddressOptions.forEach(opt => {
                                const cityCode = Array.isArray(opt) ? opt[opt.length - 1] : opt
                                if (cityCode) occupiedCities.add(cityCode)
                            })
                        }
                        else if (region.areaCode && typeof region.areaCode === 'string') {
                            const cleanCode = region.areaCode.replace(/[[]"]/g, '')
                            cleanCode.split(',').forEach(code => {
                                if (code) occupiedCities.add(code)
                            })
                        }
                    }
                })
                this.marketData = this.marketData.map(city => ({
                    ...city,
                    disabled: occupiedCities.has(city.value)
                }))
                const fullRegion = currentPeriodRegions.find(
                    item => item.regionName === '全区域'
                )
                if (fullRegion) {
                    fullRegion.selectAddressOptionsAll = []
                    fullRegion.detailAddress = []
                    this.marketData.forEach(city => {
                        if (!occupiedCities.has(city.value)) {
                            fullRegion.selectAddressOptionsAll.push(city.value)
                            fullRegion.detailAddress.push(city.label)
                        }
                    })
                }
                this.$nextTick(() => {
                    this.$forceUpdate()
                })
            }
            this.addForm.formData.regionPrice = this.addForm.formData.regionPrice.filter(obj => obj.index !== row.index)
        },
        //一口价删除区域
        removeOneParamsData1 (row) {
            const releasedCities = new Set()
            if (row.regionName === '全区域') {
                if (row.selectAddressOptionsAll) {
                    row.selectAddressOptionsAll.forEach(city => releasedCities.add(city))
                }
            }
            else {
                if (row.selectAddressOptions) {
                    row.selectAddressOptions.forEach(opt => {
                        const cityCode = Array.isArray(opt) ? opt[opt.length - 1] : opt
                        if (cityCode) releasedCities.add(cityCode)
                    })
                }
                else if (row.areaCode && typeof row.areaCode === 'string') {
                    const cleanCode = row.areaCode.replace(/[[]"]/g, '')
                    cleanCode.split(',').forEach(code => {
                        if (code) releasedCities.add(code)
                    })
                }
            }
            const index = this.addForm.formData.regionTableData.findIndex(
                item => item.index === row.index
            )
            if (index !== -1) {
                this.addForm.formData.regionTableData.splice(index, 1)
                this.addForm.formData.currentRegionTableData =
                    this.addForm.formData.regionTableData.filter(
                        item => item.accountPeriod === this.currentTab
                    )
                const occupiedCities = new Set()
                const currentPeriodRegions = this.addForm.formData.regionTableData.filter(
                    item => item.accountPeriod === this.currentTab
                )
                currentPeriodRegions.forEach(region => {
                    if (region.regionName !== '全区域') {
                        // 处理数组格式
                        if (region.selectAddressOptions) {
                            region.selectAddressOptions.forEach(opt => {
                                const cityCode = Array.isArray(opt) ? opt[opt.length - 1] : opt
                                if (cityCode) occupiedCities.add(cityCode)
                            })
                        }
                        else if (region.areaCode && typeof region.areaCode === 'string') {
                            const cleanCode = region.areaCode.replace(/[[]"]/g, '')
                            cleanCode.split(',').forEach(code => {
                                if (code) occupiedCities.add(code)
                            })
                        }
                    }
                })
                this.marketData = this.marketData.map(city => ({
                    ...city,
                    disabled: occupiedCities.has(city.value)
                }))
                const fullRegion = currentPeriodRegions.find(
                    item => item.regionName === '全区域'
                )
                if (fullRegion) {
                    fullRegion.selectAddressOptionsAll = []
                    fullRegion.detailAddress = []
                    this.marketData.forEach(city => {
                        if (!occupiedCities.has(city.value)) {
                            fullRegion.selectAddressOptionsAll.push(city.value)
                            fullRegion.detailAddress.push(city.label)
                        }
                    })
                }
                this.$nextTick(() => {
                    this.$forceUpdate()
                })
            }
            this.addForm.formData.regionPrice = this.addForm.formData.regionPrice.filter(obj => obj.index !== row.index)
        },
        //参考价的全区域
        handleAddressChange1 (item) {
            if (item) {
                this.x = item
            }
            if (item.detailAddress.includes('510000')) {
                item.detailAddress = this.allSC.map(e => e.label)
            }
        },
        //一口价的全区域
        handleAddressChange2 (item) {
            if (item) {
                this.y = item
            }
            if (item.detailAddress.includes('510000')) {
                item.detailAddress = this.allSCone.map(e => e.label)
            }
        },
        // 参考价新增区域
        handleAddressChange (item) {
            const newSelections = [...item.selectAddressOptions]
            item._prevSelections = newSelections
            const occupiedCities = new Set()
            const currentPeriodRegions = this.addForm.formData.regionTableData.filter(
                region => region.regionName !== '全区域'
            )
            currentPeriodRegions.forEach(region => {
                if (region.selectAddressOptions) {
                    region.selectAddressOptions.forEach(opt => {
                        const cityCode = Array.isArray(opt) ? opt[opt.length - 1] : opt
                        if (cityCode) occupiedCities.add(cityCode)
                    })
                }
            })
            this.marketData = this.marketData.map(city => ({
                ...city,
                disabled: occupiedCities.has(city.value)
            }))
            const fullRegion = this.addForm.formData.regionTableData.find(
                r => r.regionName === '全区域'
            )
            if (fullRegion) {
                fullRegion.selectAddressOptionsAll = []
                fullRegion.detailAddress = []
                this.marketData.forEach(city => {
                    if (!occupiedCities.has(city.value)) {
                        fullRegion.selectAddressOptionsAll.push(city.value)
                        fullRegion.detailAddress.push(city.label)
                    }
                })
            }
            if (!item.detailAddress) {
                this.$set(item, 'detailAddress', [])
            } else {
                item.detailAddress.splice(0, item.detailAddress.length)
            }
            newSelections.forEach(option => {
                const cityCode = Array.isArray(option) ? option[option.length - 1] : option
                const cityInfo = this.marketData.find(city => city.value === cityCode)
                if (cityInfo && cityInfo.label) {
                    item.detailAddress.push(cityInfo.label)
                }
            })
        },
        //一口价的新增区域
        handleAddressChange3 (item) {
            const newSelections = [...item.selectAddressOptions]
            item._prevSelections = newSelections
            const occupiedCities = new Set()
            const currentPeriodRegions = this.addForm.formData.regionTableData.filter(
                region => region.accountPeriod === this.currentTab && region.regionName !== '全区域'
            )
            currentPeriodRegions.forEach(region => {
                if (region.selectAddressOptions) {
                    region.selectAddressOptions.forEach(opt => {
                        const cityCode = Array.isArray(opt) ? opt[opt.length - 1] : opt
                        if (cityCode) occupiedCities.add(cityCode)
                    })
                }
            })
            this.marketData = this.marketData.map(city => ({
                ...city,
                disabled: occupiedCities.has(city.value)
            }))
            const fullRegion = this.addForm.formData.regionTableData.find(
                r => r.regionName === '全区域' && r.accountPeriod === this.currentTab
            )
            if (fullRegion) {
                fullRegion.selectAddressOptionsAll = []
                fullRegion.detailAddress = []
                this.marketData.forEach(city => {
                    if (!occupiedCities.has(city.value)) {
                        fullRegion.selectAddressOptionsAll.push(city.value)
                        fullRegion.detailAddress.push(city.label)
                    }
                })
            }
            if (!item.detailAddress) {
                this.$set(item, 'detailAddress', [])
            } else {
                item.detailAddress.splice(0, item.detailAddress.length)
            }
            newSelections.forEach(option => {
                const cityCode = Array.isArray(option) ? option[option.length - 1] : option
                const cityInfo = this.marketData.find(city => city.value === cityCode)
                if (cityInfo && cityInfo.label) {
                    item.detailAddress.push(cityInfo.label)
                }
            })
        },
        getFixed () {
            getList({ keywords: '固定加成率' }).then(res => {
                this.markUpNum = res.list[0].keyValue
            })
        },
        taxInPriceChange (index) {
            let regionOption = this.addForm.formData.regionTableData.filter(item => item.index === index)[0]
            if (this.addForm.formData.taxRate == null || this.addForm.formData.taxRate == '') {
                regionOption.taxInPrice = ''
                return this.$message.error('税率不能为空')
            }
            regionOption.taxInPrice = this.fixed2(regionOption.taxInPrice)
            regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
        },
        getCurrentTab (val) {
            this.marketData = this.marketData.map(city => ({
                ...city,
                disabled: false
            }))
            this.currentTab = val
            this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
        },
        addAccountPeriodRegion () {
            if (this.currentTab === 1) {
                this.addForm.formData.regionTableData.push({ index: this.regionTablePeriodIndex++, regionName: '区域' + this.regionTableIndex1++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 2) {
                this.addForm.formData.regionTableData.push({ index: this.regionTablePeriodIndex++, regionName: '区域' + this.regionTableIndex2++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 3) {
                this.addForm.formData.regionTableData.push({ index: this.regionTablePeriodIndex++, regionName: '区域' + this.regionTableIndex3++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 4) {
                this.addForm.formData.regionTableData.push({ index: this.regionTablePeriodIndex++, regionName: '区域' + this.regionTableIndex4++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 5) {
                this.addForm.formData.regionTableData.push({ index: this.regionTablePeriodIndex++, regionName: '区域' + this.regionTableIndex5++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 6) {
                this.addForm.formData.regionTableData.push({ index: this.regionTablePeriodIndex++, regionName: '区域' + this.regionTableIndex6++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            }
            this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
        },
        taxInPriceChange1 (index) {
            let regionOption = this.addForm.formData.currentRegionTableData.filter(item => item.index === index)[0]
            if (this.addForm.formData.taxRate == null || this.addForm.formData.taxRate == '') {
                regionOption.taxInPrice = ''
                return this.$message.error('税率不能为空')
            }
            regionOption.taxInPrice = this.fixed2(regionOption.taxInPrice)
            regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
        },
        payBeforeDeliveryChange (index) {
            let regionOption = this.addForm.formData.currentRegionTableData.filter(item => item.index === index)[0]
            if (this.addForm.formData.annualizedRate == null || this.addForm.formData.annualizedRate == '') {
                regionOption.payBeforeDelivery = ''
                return this.$message.error('年化率不能为空')
            }
            let taxInPrice = regionOption.payBeforeDelivery * (1 + (this.addForm.formData.annualizedRate / 1200 * regionOption.accountPeriod))
            regionOption.taxInPrice = this.fixed2(taxInPrice)
            if (this.addForm.formData.taxRate == null || this.addForm.formData.taxRate == '') {
                regionOption.payBeforeDelivery = ''
                regionOption.taxInPrice = ''
                return this.$message.error('税率不能为空')
            }
            regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
        },
        getEconomizeAndMarketList () {
            const sichuanData = this.addressData.filter(item => item.value === '510000')
            let economizeList = []
            let marketList = []
            sichuanData.forEach((e, i)=>{
                economizeList.push( { label: e.label, value: e.value } )
                marketList.push( { label: e.label, value: e.value, children: [] } )
                e.children.forEach(s=>{
                    marketList[i].children.push({ label: s.label, value: s.value, disabled: false })
                })
            })
            this.economizeData = economizeList.filter(item => item.label === '四川省')
            this.marketData = marketList.filter(item => item.label === '四川省')
            this.marketData = this.marketData[0].children
            this.allSC = this.marketData
            this.allSCone = this.marketData
        },
        //获取当前企业的企业税率
        getEnterpriseInfoTaxRateM () {
            getEnterpriseInfoTaxRate().then(res => {
                this.addForm.formData.taxRate = res
            })
        },
        addRegion () {
            this.addForm.formData.regionTableData.push({
                index: this.regionTableIndex + 1,
                regionName: '区域' + this.regionTableIndex,
                accountPeriod: 2,
                selectAddressOptions: [],
                taxInPrice: '',
                price: '',
                detailAddress: []
            })
            this.regionTableIndex++
        },
        arChange () {
            if (this.addForm.formData.annualizedRate == null || this.addForm.formData.annualizedRate == '') {
                return this.$message.error('年化率不能为空')
            }
            if (!(0 <= this.addForm.formData.annualizedRate && this.addForm.formData.annualizedRate <= 100)) {
                this.addForm.formData.annualizedRate = ''
                return this.$message.error('年化率不能小于0或大于100')
            }
            if (this.addForm.formData.regionTableData.length > 0) {
                this.addForm.formData.regionTableData.forEach(e=>{
                    if (e.payBeforeDelivery.length > 0) {
                        let taxInPrice = e.payBeforeDelivery * (1 + (this.addForm.formData.annualizedRate / 1200 * e.accountPeriod))
                        e.taxInPrice = this.fixed2(taxInPrice)
                        e.price = this.fixed2(e.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
                    }
                })
            }
        },
        writeTaxRate () {
            if (this.addForm.formData.taxRate == null || this.addForm.formData.taxRate == '') {
                return this.$message.error('税率不能为空')
            }
            if (!(0 <= this.addForm.formData.taxRate && this.addForm.formData.taxRate <= 100)) {
                this.addForm.formData.taxRate = ''
                return this.$message.error('税率不能小于0或大于100')
            }
            this.addForm.formData.taxRate = this.fixed2(this.addForm.formData.taxRate)//税率保留2位小数
            if (this.userInfo.isBusiness == 0 || this.addForm.formData.priceType == 1 ) { //参考价，没有账期和先款后货
                if (this.addForm.formData.regionTableData.length > 0) {
                    this.addForm.formData.regionTableData.forEach(r => {
                        if (r.taxInPrice != null && r.taxInPrice.length > 0) {
                            r.price = this.fixed2(r.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
                        }
                    })
                }
            }else{
                if (this.addForm.formData.regionTableData.length > 0) {
                    this.addForm.formData.regionTableData.forEach(e=>{
                        if (e.payBeforeDelivery != null && e.payBeforeDelivery.length > 0) {
                            let taxInPrice = e.payBeforeDelivery * (1 + (this.addForm.formData.annualizedRate / 1200 * e.accountPeriod))
                            e.taxInPrice = this.fixed2(taxInPrice)
                            e.price = this.fixed2(e.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
                        }
                    })
                }
            }
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        limitZonePrice (row) {
            if (row.zonePrice <= 0 || row.zonePrice >= this.addForm.formData.originalPrice) {
                row.zonePrice = this.fixed2(0)
                this.$message.error('销售价格不能小于0或销售价格不能大于原价')
            } else {
                row.zonePrice = this.fixed2(row.zonePrice)
            }

        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        setCropperImg (file, type, [w, h]) {
            this.uploadType = type
            this.$refs.cropperImage.handleOpen(file, { autoCropWidth: w, autoCropHeight: h }, {
                type: 'fixed',
                sizeWidth: w,
                sizeHeight: h
            })
        },
        handleUploadSuccess (file) {
            if (this.uploadType === 0) {
                this.uploadAdminFile(file)
            } else if (this.uploadType === 1) {
                this.uploadMinFile(file)
            } else if (this.uploadType === 2) {
                this.uploadProductFile(file)
            }
        },
        handleSecondMinNum (val) {
            if (val <= 0 && this.isSecondUnit === 1) {
                this.$message.error('请输入大于0的第二单位系数')
                return false
            }
            return true
        },
        countBouns (regionPrice) {
            if (this.addForm.formData.markUpNum != null && this.addForm.formData.markUpNum != '') {
                regionPrice.forEach(e=>{
                    e.bonusTaxInPrice = this.fixed2(e.taxInPrice * (1 + (this.addForm.formData.markUpNum / 100)))
                    e.bonusPrice = this.fixed2(e.price * (1 + (this.addForm.formData.markUpNum / 100)))
                    e.markUpNum = this.addForm.formData.markUpNum
                })
            }
            return regionPrice
        },
        resetRelevanceAndBrand () {
            let arr = ['relevanceName', 'brandName', 'brandId', 'relevanceId']
            arr.forEach(item => this.addForm.formData[item] = '')
        },
        handleExceed () {
            this.$message.warning('请最多上传 ' + this.uploadMax + ' 个文件。')
        },
        //selectAddressOptions和selectAddressOptionsAll处理
        HandleArrays (arr) {
            let Arr = []
            let Arrall = []
            for(let item of arr) {
                if(item.regionName != '全区域') {
                    let x = item.selectAddressOptions || []
                    for(let i of x) {
                        Arr.push(i[0])
                    }
                    item.selectAddressOptions = Arr
                }
                if(item.regionName == '全区域') {
                    let x = item.selectAddressOptionsAll || []
                    for(let i of x) {
                        Arrall.push(i)
                    }
                    item.selectAddressOptionsAll = Arrall
                }
            }
        },
        // 修改状态
        updateStateBatch (state, title) {
            this.HandleArrays(this.addForm.formData.regionPrice)
            for (let i = 1; i < this.addForm.formData.regionTableData.length; i++) {
                if (this.addForm.formData.regionTableData[i].selectAddressOptions && Array.isArray(this.addForm.formData.regionTableData[i].selectAddressOptions)) {
                    this.addForm.formData.regionTableData[i].selectAddressOptions = this.addForm.formData.regionTableData[i].selectAddressOptions.flat()
                }
            }
            for (let i = 1; i < this.addForm.formData.currentRegionTableData.length; i++) {
                if (this.addForm.formData.currentRegionTableData[i].selectAddressOptions && Array.isArray(this.addForm.formData.currentRegionTableData[i].selectAddressOptions)) {
                    this.addForm.formData.currentRegionTableData[i].selectAddressOptions = this.addForm.formData.currentRegionTableData[i].selectAddressOptions.flat()
                }
            }
            let params = {
                productIds: [this.addForm.formData.productId],
                state: state,
                saveAndSubmit: 0
            }
            const regionMap = new Map()
            this.marketData.forEach(item => {
                regionMap.set(item.label, item.value)
            })
            this.addForm.formData.regionTableData.forEach(item => {
                if (item.regionName === '全区域') {
                    const detailAddress = item.detailAddress || []
                    const codes = detailAddress
                        .map(name => regionMap.get(name))
                        .filter(code => code !== undefined)
                    const uniqueCodes = [...new Set(codes)]
                    item.selectAddressOptionsAll = uniqueCodes
                }
            })
            const addressMap = {}
            this.marketData.forEach(item => {
                if (!addressMap[item.label]) {
                    addressMap[item.label] = []
                }
                addressMap[item.label].push(item.value)
            })
            this.addForm.formData.regionPrice.forEach(item => {
                if(item.regionName != '全区域') {
                    const matchedValues = []
                    let addressString = ''
                    if (item.detailAddress) {
                        addressString = String(item.detailAddress)
                    }
                    if (addressString) {
                        const addresses = addressString.split(',').map(addr => {
                            return typeof addr === 'string' ? addr.trim() : ''
                        })
                        const validAddresses = addresses.filter(addr => addr !== '')
                        validAddresses.forEach(address => {
                            if (addressMap[address]) {
                                matchedValues.push(...addressMap[address])
                            }
                        })
                    }
                    this.$set(item, 'selectAddressOptions', matchedValues)
                }
            })
            if (state === 3) {
                // 保存并上架
                this.$refs.formEdit.validate(valid => {
                    if (valid) {
                        if (this.addForm.formData.putawayDate != null && this.addForm.formData.priceState === 1) {
                            this.$message.error('当前季度已修改过商品价格')
                        }
                        // if ((this.addForm.formData.secondUnitNum === 0 || this.addForm.formData.secondUnitNum === null) && this.isSecondUnit === 1) {
                        //     return this.$message.error('请填写大于0的副级单位数量')
                        // }
                        // if (!this.handleSecondMinNum(this.addForm.formData.secondUnitNum)) {
                        //     return
                        // }
                        //this.addForm.formData.regionPrice = this.addForm.formData.regionTableData
                        if (this.userInfo.isBusiness === 1 && this.addForm.formData.priceType === 0) {
                            this.addForm.formData.regionPrice = []
                            for (let item of this.addForm.formData.accountPeriod) {
                                let regionList = this.addForm.formData.regionTableData.filter(item1 => item1.accountPeriod === item)
                                this.addForm.formData.regionPrice = this.addForm.formData.regionPrice.concat(regionList)
                            }
                        } else {
                            this.addForm.formData.regionPrice = this.addForm.formData.regionTableData
                        }
                        this.countBouns(this.addForm.formData.regionPrice)
                        this.addForm.formData.accountPeriod = this.addForm.formData.accountPeriod.toString()
                        spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                        let newParams = {
                            productIds: [this.addForm.formData.productId],
                            state: state,
                            ...this.addForm.formData
                        }
                        newParams.state = state
                        params.saveAndSubmit = 1
                        this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                            updateMaterialAndState(newParams).then(res => {
                                this.handleClose()
                                this.message(res)
                                return
                            })
                        })

                    }/* else {
                        this.$message({
                            message: '请检查非空输入框',
                            type: 'error'
                        })
                    }*/
                })
            } else {
                // 下架
                this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                    updateProductState(params).then(res => {
                        this.handleClose()
                        this.message(res)
                    })
                })

            }

        },
        // 获取物资详情
        getMaterialInfo () {
            let params = {
                productId: this.rowData.productId,
                productType: 2
            }
            this.formLoading = true
            getMaterialInfo(params).then(res => {
                for(let itemI of res.regionPrice) {
                    if(itemI.regionName != '全区域') {
                        let ab = itemI.selectAddressOptions || []
                        let abArr = []
                        for(let aItem of ab) {
                            abArr.push([aItem])
                        }
                        itemI.selectAddressOptions = abArr
                    }
                }
                let allSelectedCities = []
                res.regionPrice.forEach(item => {
                    if (item.regionName === '全区域') {
                        item.detailAddress = item.area.split(',')
                        if (item.areaCode.startsWith('[')) {
                            try {
                                item.selectAddressOptionsAll = JSON.parse(item.areaCode)
                            } catch (e) {
                                item.selectAddressOptionsAll = item.areaCode.split(',')
                            }
                        } else {
                            item.selectAddressOptionsAll = item.areaCode.split(',')
                        }
                    }
                    else {
                        item.detailAddress = item.area.split(',')
                        if (!item.selectAddressOptions) {
                            if (item.areaCode.startsWith('[[')) {
                                try {
                                    item.selectAddressOptions = JSON.parse(item.areaCode)
                                } catch (e) {
                                    const cityCodes = item.areaCode.split(',')
                                    item.selectAddressOptions = cityCodes.map(code => code)
                                }
                            } else {
                                const cityCodes = item.areaCode.split(',')
                                item.selectAddressOptions = cityCodes.map(code =>  code)
                            }
                        }
                    }
                })

                this.selectedCity = [...new Set(allSelectedCities)]
                this.marketData = this.marketData.map(city => ({
                    ...city,
                    disabled: this.selectedCity.includes(city.value)
                }))
                // this.addressFormatShow(res)
                addImgUrl(res, this.imgUrlPrefixAdd)
                this.addForm.minFileLength = res.minFile.length
                this.addForm.adminFileLength = res.adminFile.length
                res['currentRegionTableData'] = []
                res['regionTableData'] = []
                this.addForm.formData = res
                if (this.userInfo.isBusiness === 1 && this.addForm.formData.priceType == 0) {
                    this.addForm.formData.accountPeriod = res.accountPeriod.split(',').map(Number)
                    this.currentTab = this.addForm.formData.accountPeriod[0]
                    let regionList = []
                    for (let i = 1;i < 7;i++) {
                        let regionOption = res.regionPrice.filter(item1 => item1.accountPeriod === i)
                        if (regionOption.length > 0) {
                            regionOption[0]['index'] = i
                            regionList.push(regionOption[0])
                            regionOption.splice(0, 1)
                            for (let item of regionOption) {
                                item.index = this.regionTablePeriodIndex++
                                regionList.push(item)
                            }
                            if (regionOption.length > 0) {
                                let indexNumber = Number(regionOption[regionOption.length - 1].regionName.replace('区域', '')) + 1
                                switch (i) {
                                case 1:
                                    this.regionTableIndex1 = indexNumber
                                    break
                                case 2:
                                    this.regionTableIndex2 = indexNumber
                                    break
                                case 3:
                                    this.regionTableIndex3 = indexNumber
                                    break
                                case 4:
                                    this.regionTableIndex4 = indexNumber
                                    break
                                case 5:
                                    this.regionTableIndex5 = indexNumber
                                    break
                                case 6:
                                    this.regionTableIndex6 = indexNumber
                                    break
                                }
                            }
                        } else {
                            regionList.push({ index: i, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: i, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
                        }
                    }
                    this.addForm.formData.regionTableData = regionList
                    this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
                } else {
                    this.addForm.formData.regionTableData = res.regionPrice
                    this.addForm.formData.regionTableData.forEach((e, i) => {
                        e['index'] = i + 1
                        if (i === (this.addForm.formData.regionTableData.length - 1)) {
                            this.regionTableIndex = Number(e.regionName.replace('区域', '')) + 1
                        }
                    })
                }
                this.mainImg = res.adminFile[0].url
                this.smallImg = res.minFile[0].url
                // 更新的时候处理是否第二单位
                if (res.secondUnitNum != 0 && res.secondUnit !== '') {
                    this.isSecondUnit = 1
                } else {
                    this.isSecondUnit = 0
                }
                this.inventory.classId = this.addForm.formData.classId
                if (this.addForm.formData.supplierSubmitState == 3 && this.addForm.formData.markUp == null) {
                    this.addForm.formData.markUp = '1'
                    this.addForm.formData.markUpNum = this.markUpNum
                } else {
                    this.addForm.formData.markUp = String(this.addForm.formData.markUp)
                    this.showForm = true
                }
                let auditTableList = []
                let optionTj = {
                    auditType: '提交',
                    auditName: this.addForm.formData.jcTjName,
                    type: '供应商',
                    auditTime: this.addForm.formData.jcTjTime,
                    record: this.addForm.formData.jcTjYj
                }
                auditTableList.push(optionTj)
                let optionFzr = {
                    auditType: '审核',
                    auditName: this.addForm.formData.jcFzrName,
                    type: '电商运营部负责人',
                    auditTime: this.addForm.formData.jcFzrTime,
                    record: this.addForm.formData.jcFzrYj
                }
                auditTableList.push(optionFzr)
                let optionLd = {
                    auditType: '审定',
                    auditName: this.addForm.formData.jcLdName,
                    type: '分管领导',
                    auditTime: this.addForm.formData.jcLdTime,
                    record: this.addForm.formData.jcLdYj
                }
                auditTableList.push(optionLd)
                this.auditTableData = []
                if (this.addForm.formData.jcState >= 3) {
                    this.auditTableData = auditTableList
                } else if (this.addForm.formData.jcState >= 1) {
                    this.auditTableData = auditTableList.slice(0, 2)
                } else if (this.addForm.formData.jcState >= 0) {
                    this.auditTableData.push(auditTableList[0])
                }
                this.addForm.formData.regionTableData.forEach(item => {
                    if (item.selectAddressOptionsAll) {
                        item.selectAddressOptionsAll = item.selectAddressOptionsAll.filter(
                            code => code === '510000'
                        )
                    }
                })
                this.addForm.formData.currentRegionTableData.forEach(item => {
                    if (item.selectAddressOptionsAll) {
                        item.selectAddressOptionsAll = item.selectAddressOptionsAll.filter(
                            code => code === '510000'
                        )
                    }
                })
                this.showForm = true
            }).finally(() => {
                this.formLoading = false
            })
        },
        getShelfLog () {
            let params = {
                productId: this.rowData.productId,
                productType: 2,
                page: this.pages.currPage,
                limit: this.pages.pageSize || 20
            }
            getShelfLogInfo(params).then(res => {
                this.tableData = res.list
                this.pages.totalCount = res.totalCount
            })
        },
        exportLog () {
            this.tableLoading = true
            exportShelfLog([this.rowData.productId]).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '上架记录.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                this.currentQuery.ids = []
                this.dataListSelections = []
                this.$message.success('操作成功')
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        onEditorBlur () {
            this.$refs.formEdit.validateField('productDescribe')
        },
        deleteShelfLog (id) {
            this.clientPop('info', '您确定要删除该记录吗?', async () => {
                this.tableLoading = true
                removeShelfLogInfo(id).then(res => {
                    if (res.code === 200) {
                        this.$message.success('删除成功')
                        this.getShelfLog()
                    }
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.formData.unit = value
        },
        // 副级单位选择
        numSecondUnitChange (value) {
            this.addForm.formData.secondUnit = value
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null) return
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.addForm.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 物资库点击
        handleCurrentInventoryClick (row) {
            this.addForm.formData.relevanceName = row.materialName
            this.addForm.formData.relevanceId = row.billId
            this.addForm.formData.relevanceNo = row.billNo
            this.addForm.formData.skuName = row.spec
            this.addForm.formData.unit = row.unit
            this.addForm.formData.classId = row.classId
            this.addForm.formData.classPath = row.classIdPath.split('/')
            this.showDeviceDialog = false
            this.$refs.formEdit.validateField('classId')
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                isActive: 1,
                pageIndex: this.inventory.paginationInfo.currentPage,
                pageSize: this.inventory.paginationInfo.pageSize,
            }
            // if (this.inventory.classId != null) {
            //     params.classId = this.inventory.classId
            // }
            params.classId = 'a927249575ee-ba4a-3d4e-bcef-a93d5c45'//周转材料
            if (this.inventory.keyWord != null) {
                params.keyWord = this.inventory.keyWord
            }
            this.inventoryTableLoading = true
            queryPageMaterialDtl(params).then(res => {
                this.inventory.tableData = res.list
                this.inventory.paginationInfo.total = res.totalCount
                // this.inventory.paginationInfo.pageSize = res.pageSize
                // this.inventory.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.inventoryTableLoading = false
            })
        },
        // 选择物资名称
        importDeviceSelect () {
            /*if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            } else {
                this.inventory.classId = this.addForm.formData.classId
            }*/
            this.showDeviceDialog = true
            this.getDeviceInventory()
        },

        // 提交
        submit () {
            for (let i = 1; i < this.addForm.formData.regionTableData.length; i++) {
                if (this.addForm.formData.regionTableData[i].selectAddressOptions && Array.isArray(this.addForm.formData.regionTableData[i].selectAddressOptions)) {
                    this.addForm.formData.regionTableData[i].selectAddressOptions = this.addForm.formData.regionTableData[i].selectAddressOptions.flat()
                }
            }
            for (let i = 1; i < this.addForm.formData.currentRegionTableData.length; i++) {
                if (this.addForm.formData.currentRegionTableData[i].selectAddressOptions && Array.isArray(this.addForm.formData.currentRegionTableData[i].selectAddressOptions)) {
                    this.addForm.formData.currentRegionTableData[i].selectAddressOptions = this.addForm.formData.currentRegionTableData[i].selectAddressOptions.flat()
                }
            }
            const regionMap = new Map()
            this.marketData.forEach(item => {
                regionMap.set(item.label, item.value)
            })
            this.addForm.formData.regionTableData.forEach(item => {
                if (item.regionName === '全区域') {
                    const detailAddress = item.detailAddress || []
                    const codes = detailAddress
                        .map(name => regionMap.get(name))
                        .filter(code => code !== undefined)
                    const uniqueCodes = [...new Set(codes)]
                    item.selectAddressOptionsAll = uniqueCodes
                }
            })
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    if (this.userInfo.isBusiness === 1 && this.addForm.formData.priceType === 0) {
                        this.addForm.formData.regionPrice = []
                        for (let item of this.addForm.formData.accountPeriod) {
                            let regionList = this.addForm.formData.regionTableData.filter(item1 => item1.accountPeriod === item)
                            this.addForm.formData.regionPrice = this.addForm.formData.regionPrice.concat(regionList)
                        }
                    } else {
                        this.addForm.formData.regionPrice = this.addForm.formData.regionTableData
                    }
                    this.countBouns(this.addForm.formData.regionPrice)
                    this.addForm.formData.markUp = this.addForm.formData.markUp != null ? this.addForm.formData.markUp : 0
                    this.addForm.formData.accountPeriod = this.addForm.formData.accountPeriod.toString()
                    //转为数组
                    for (let item of this.addForm.formData.regionPrice) {
                        if (typeof item.selectAddressOptionsAll === 'object' &&
                            item.selectAddressOptionsAll !== null &&
                            !Array.isArray(item.selectAddressOptionsAll)) {
                            item.selectAddressOptionsAll = Object.entries(item.selectAddressOptionsAll)
                        }
                    }
                    for (let item of this.addForm.formData.regionTableData) {
                        if (typeof item.selectAddressOptionsAll === 'object' &&
                            item.selectAddressOptionsAll !== null &&
                            !Array.isArray(item.selectAddressOptionsAll)) {
                            item.selectAddressOptionsAll = Object.entries(item.selectAddressOptionsAll)
                        }
                    }
                    spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                    if (this.viewType === 'add') {
                        this.formLoading = true
                        createMaterial(this.addForm.formData).then(res => {
                            if (res.code != null && res.code != 200) {
                                addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                                this.formLoading = false
                                return
                            }
                            let classInfo = {
                                classPath: this.addForm.formData.classPath,
                                classId: this.addForm.formData.classId
                            }
                            // 重置
                            // this.resetFormData()
                            // 恢复分类
                            this.addForm.formData.classPath = classInfo.classPath
                            this.addForm.formData.classId = classInfo.classId
                            /*this.$refs.adminFileRef.clearFiles()
              this.$refs.productFileRef.clearFiles()
              this.$refs.minFileRef.clearFiles()*/
                            this.addForm.minFileLength = 0
                            this.addForm.adminFileLength = 0
                            this.viewType = 'add'
                            this.message(res)
                            this.handleClose()
                        }).finally(() => {
                            this.formLoading = false
                        })
                    } else {
                        this.formLoading = true
                        if (this.addForm.formData.putawayDate != null && this.addForm.formData.priceState === 1) {
                            this.$message.error('当前季度已修改过商品价格')
                        }
                        updateMaterial(this.addForm.formData).then(res => {
                            this.message(res)
                            this.handleClose()
                        }).finally(() => {
                            this.getMaterialInfo()
                            this.formLoading = false
                        })
                    }
                }/* else {
                    this.$message.error('请检查非空输入框')
                }*/
            })
        },
        changePriceType (val) {
            this.marketData = this.marketData.map(city => ({
                ...city,
                disabled: false
            }))
            if (val === 0) {
                this.addForm.formData.regionTableData = [
                    { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 1, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 2, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 2, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 3, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 3, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 4, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 4, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 5, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 5, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                    { index: 6, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 6, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] }
                ]
                this.currentTab = 1
                this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
                this.regionTablePeriodIndex = 7
                this.regionTableIndex1 = 1
                this.regionTableIndex2 = 1
                this.regionTableIndex3 = 1
                this.regionTableIndex4 = 1
                this.regionTableIndex5 = 1
                this.regionTableIndex6 = 1
                this.addForm.formData.accountPeriod = [1]
            } else {
                this.addForm.formData.regionTableData = [
                    { index: 1, regionName: '全区域', selectAddressOptionsAll: [], taxInPrice: '', price: '', detailAddress: [] }
                ]
                this.regionTableIndex = 1
            }
            this.$nextTick(()=> {
                this.$refs['formEdit'].clearValidate()
            })
        },
        // 品牌单选
        handleCurrentClick (row) {
            this.addForm.formData.brandId = row.brandId
            this.addForm.formData.brandName = row.name
            this.showBrandDialog = false
        },
        // 分类点击
        classNodeClick (data) {
            this.brand.classId = data.classId
            this.getBrandTableData()
        },
        // 获取品牌表格
        getBrandTableData () {
            let params = {
                page: this.brand.paginationInfo.currentPage,
                limit: this.brand.paginationInfo.pageSize,
            }
            if (this.brand.keywords != null) {
                params.name = this.brand.keywords
            }
            if (this.brand.classId != null) {
                params.classId = this.brand.classId
            }
            this.brandTableLoading = true
            getBrandPageList(params).then(res => {
                this.brand.tableData = res.list
                this.brand.paginationInfo.total = res.totalCount
                this.brand.paginationInfo.pageSize = res.pageSize
                this.brand.paginationInfo.currentPage = res.currPage
                this.brandTableLoading = false
            }).catch(() => {
                this.brandTableLoading = false
            })
        },
        brandDialog () {
            if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            }
            this.brand.classId = this.addForm.formData.classId
            this.showBrandDialog = true
            this.getBrandTableData()
        },
        uploadFileInfo (file) {
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 上传主图
        async uploadAdminFile (file) {
            let currFile = this.addForm.formData.adminFile[0]
            if (currFile) await createFileRecordDelete({ recordId: currFile.fileFarId + '' })
            let form = this.uploadFileInfo(file)
            this.addForm.adminFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 1
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 0
                this.mainImg = file.url
                this.addForm.formData.adminFile = [file]
                this.$message({ message: '上传成功', type: 'success' })
                this.$refs.formEdit.validateField('adminFile')
            }).finally(() => {
                this.addForm.adminFileLength = 0
                this.formLoading = false
            })
        },
        // 上传物资图片
        uploadProductFile (file) {
            let form = this.uploadFileInfo(file)
            this.formLoading = true
            uploadFile(form).then(res => {
                let productFile = {}
                productFile.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                productFile.name = res[0].objectName
                productFile.isMain = 0
                productFile.relevanceType = 1
                productFile.fileType = 1
                productFile.fileFarId = res[0].recordId
                productFile.imgType = 0
                this.addForm.formData.productFiles.push(productFile)
                this.$message({ message: '上传成功', type: 'success' })
                this.$refs.formEdit.validateField('productFiles')
            }).finally(() => {
                this.formLoading = false
            })
        },
        // 上传小图
        async uploadMinFile (file) {
            let currFile = this.addForm.formData.minFile[0]
            if (currFile) await createFileRecordDelete({ recordId: currFile.fileFarId + '' })
            let form = this.uploadFileInfo(file)
            this.addForm.minFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 1
                this.smallImg = file.url
                this.addForm.formData.minFile = [file]
                this.$message({ message: '上传成功', type: 'success' })
                this.$refs.formEdit.validateField('minFile')
            }).finally(() => {
                this.addForm.minFileLength = 0
                this.formLoading = false
            })
        },

        handleImgPreview (name) {
            if (name === 'mainImg') {
                this.previewImg = this.mainImg
            } else if (name === 'minImg') {
                this.previewImg = this.smallImg
            }
            this.imgPreviewDialog = true
        },
        delImg (name) {
            if (name === 'mainImg') {
                this.mainImg = ''
                this.addForm.formData.adminFile = []
                this.$refs.formEdit.validateField('adminFile')
            } else if (name === 'minImg') {
                this.smallImg = ''
                this.addForm.formData.minFile = []
                this.$refs.formEdit.validateField('minFile')
            }
        },
        // 判断上传的图片大小
        checkFileSize (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 主图删除
        adminFileRemove (file, fileList) {
            this.addForm.adminFileLength = fileList.length
            let recordId = this.addForm.formData.adminFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.adminFile = []
            })
        },
        // 物资图片删除
        productFileRemove (file) {
            let files = this.addForm.formData.productFiles
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.productFiles = newFiles
                this.$refs.formEdit.validateField('productFiles')
            })

        },
        // 小图删除
        minFileRemove (file, fileList) {
            this.addForm.minFileLength = fileList.length
            let recordId = this.addForm.formData.minFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.minFile = []
            })
        },
        //取消
        handleClose () {
            this.$router.replace('/supplierSys/product/turnMaterialWarehouse')
        },
        //点击标签滚动屏幕到对应标签页
        onChangeTab (e) {
            this.tabsName = e.name
            this.$nextTick(() => {
                const targetId = e.name
                const targetElement = document.getElementById(targetId)
                if (targetElement) {
                    const topOffset = 120
                    const targetPosition = targetElement.offsetTop - topOffset
                    const container = document.getElementById('tabs-content')
                    container.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    })
                }
                this.clickTabFlag = true
                setTimeout(() => {
                    this.clickTabFlag = false
                }, 1000)
            })
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
              document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                productType: 2,
                relevanceName: null,
                productName: null,
                productKeyword: null,
                classId: null,
                classPath: [],
                productMinPrice: null,
                brandId: null,
                brandName: null,
                shopSort: null,
                skuName: null,
                settlePrice: null,
                costPrice: null,
                stock: 1,
                unit: null,
                originalPrice: null,
                sellPrice: null,
                province: null,
                city: null,
                county: null,
                detailedAddress: null,
                productFiles: [],
                adminFile: [],
                minFile: [],
                productDescribe: null,
            }
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        currentChange (val) {
            this.pages.currPage = val
            this.getShelfLog()
        },
        sizeChange (val) {
            this.pages.pageSize = val
            this.pages.currPage = 1
            this.getShelfLog()
        },
        handleCurrentChange () {

        },
        handleSelectionChange () {

        },
    }
}
</script>

<style lang='scss' scoped>
/deep/ .el-dialog .el-dialog__body {
  height: unset !important;
  margin: 0 20px;
}

/deep/ .el-upload {
  width: 148px;
  height: 148px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  .el-icon-delete {
    margin-left: 10px;
    line-height: unset;
    color: #e9513e;
  }

  .cover {
    width: 100%;
    height: 100%;
    position: relative;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
  }

  &:hover {
    border-color: #409EFF;

    .cover {
      display: block;
    }
  }

  img, & > i {
    width: 148px;
    height: 148px;
  }

  i {
    font-size: 28px;
    color: #8c939d;
    line-height: 148px;
    text-align: center;
  }

  img {
    object-fit: cover;
    display: block;
  }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.e-table {
  min-height: auto;
  background: #fff;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-tabs__content {
  // overflow: hidden;
  &::-webkit-scrollbar {
    width: 0;
  }
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
  display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
  display: none;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 300px;
    margin-top: 0px;
  }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}
.tabContent {
  margin-left: 9%;
  margin-bottom: 1%;
  border-bottom: 1px solid #cdcdcd;
}
.accountPeriodTabs {
  font-size: 19px;
  display: inline-block;
  margin-right: 2%;
  .tabTitle {
    border-bottom: 5px solid #2e65d9;
    padding: 6px 0;
    color: #2e65d9;
    cursor: pointer;
  }
  .tabTitleNoSelect {
    padding: 6px 0;
    cursor: pointer;
  }
}
#tabs-content {
  scroll-behavior: smooth; /* 启用平滑滚动 */
  position: relative; /* 确保偏移计算准确 */
  overflow-y: auto; /* 确保滚动生效 */
}
</style>
