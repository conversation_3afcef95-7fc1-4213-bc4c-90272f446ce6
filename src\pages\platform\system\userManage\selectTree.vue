<template>
  <div class="tree" style="max-height: 10vh" v-loading="treeLoading">
    <div class="search_box">
      <el-input
          clearable
          class="ipt"
          type="text"
          placeholder="输入搜索关键字"
          v-model="searchKey"
          @select="onSearch"
          @change="onSearch"
      >
        <img src="@/assets/search.png" slot="suffix" @click="onSearch"  alt=""/>
      </el-input>
    </div>
    <el-tree
        ref="tree"
        :props="props"
        node-key="id"
        :load="loadNode"
        lazy
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        :data="treeData"
    >
    </el-tree>
  </div>
</template>

<script>
import { getTree4User, } from '@/api/platform/supplier/supplierAudit'
export default {
    props: ['productType', 'isHaveProduct', 'isLc'],
    data () {
        return {
            treeLoading: false,
            treeData: null, // 树data
            searchKey: null, // 关键字
            props: {
                label: 'name',
                value: 'id',
                children: 'children',
            },
            nodePath: [],
        }
    },
    created () {
        // this.onSearch()
    },
    watch: {
        'searchKey': {
            handler () {
                this.$emit('searchValueChange', this.searchKey)
            }
        }
    },
    methods: {
    // 搜索框
        onSearch () {
            let params = { name: '' }
            if(this.searchKey != null && this.searchKey.trim().length > 0) {
                params.name = this.searchKey
            }else{
                params.id = '00000000-0000-0000-0000-000000000000CCE7AED4'
            }
            this.treeLoading = true
            getTree4User(params).then(res => {
                this.treeData = res.filter(item=> !item.name.endsWith('机关'))
                this.treeLoading = false
            })
        },
        // 树节点点击，调用父组件函数（递归）
        handleNodeClick (data, node) {
            this.nodePath = []
            this.getNodePath(node)
            this.nodePath = this.nodePath.reverse()
            if(this.$parent.classNodeClick == null) {
                this.$emit('classNodeClick', data)
            }else {
                this.$parent.classNodeClick(data, this.nodePath)
            }
        },
        filterNode (value, data) {
            return !data.name.endsWith('机关')
        },
        loadNode (node, resolve) {
            if (node.level === 0) {
                return getTree4User({ id: '00000000-0000-0000-0000-000000000000CCE7AED4' }).then(data => {
                    resolve(data)
                    this.$refs.tree.filter('')
                })
            }
            setTimeout(() => {
                getTree4User({ id: node.data.id }).then(res=>{
                    resolve(res)
                    this.$refs.tree.filter('')
                })
            }, 100)
        },
        getNodePath (node) {
            if(node.parentid == null) {
                return
            }
            this.nodePath.push(node.data.classId)
            node = node.parentid
            this.getNodePath(node)
        },
    }
}
</script>

<style lang="scss" scoped>
.search_box {
  flex-direction: column; margin: 5px 0;
}
/deep/ .el-input__inner {
  border-radius: 5px;
}
/deep/ .el-tree-node__content{
  width: 100%;
  .tree{display: flex; align-items: center;
    .txt{max-width: 134px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
    .btn{display: none; margin-left: 5px; width: 22px; height: 22px;}
  }
  &:hover{
    .tree{
      .btn{display: block;}
    }
  }
}
</style>