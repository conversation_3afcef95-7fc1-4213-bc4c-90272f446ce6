<template>
  <div class="base-page" v-loading="showLoading">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <div class="top">
          <div class="left">
          </div>
          <div class="search_box">
            <el-radio v-model="init.recordType" :label="1">入库</el-radio>
            <el-radio v-model="init.recordType" :label="2">出库</el-radio>
            <el-input
                clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字"
                v-model="init.keywords"
            >
              <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
            </el-input>
            <div class="adverse">
              <el-button type="primary" size="small" @click="highSearch">高级搜索</el-button>
            </div>
          </div>
        </div>
      </div>
      <!--表格-->
      <div class="e-table">
        <el-table
            @row-click="handleCurrentInventoryClick2" ref="mainTable2" v-loading="tableLoading" class="table"
            :height="rightTableHeight" :data="tableData" border
            @selection-change="selectionChangeHandle"
        >
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="订单号" width="160">
            <template v-slot="scope">
              <span class="action" @click="handleView(scope.row)">{{ scope.row.orderSn }}</span>
            </template>
          </el-table-column>
          <el-table-column label="类型" prop="recordType">
            <template v-slot="scope">
              <span v-if="scope.row.recordType==1">入库</span>
              <span v-if="scope.row.recordType==2">出库</span>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" width="120" prop="productName" type="index">
          </el-table-column>
          <el-table-column label="供货机构" prop="supplierName"/>
          <el-table-column label="收货机构" prop="purchasingAgencyName"/>
          <el-table-column label="商品类型" prop="productType">
            <template v-slot="scope">
              <span v-if="scope.row.productType==0">低值易耗品</span>
              <span v-if="scope.row.productType==1" type="success">大宗临购</span>
              <span v-if="scope.row.productType==2" type="danger">大宗临购清单</span>
            </template>
          </el-table-column>
          <el-table-column label="总金额(含税)" prop="bidRateAmount"/>
          <el-table-column label="数量" prop="num"></el-table-column>
          <el-table-column label="库房" prop="warehouseId">
            <template v-slot="scope">
              <span v-if="scope.row.warehouseId==1">库房一</span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="operationUser"/>
          <el-table-column label="申请退货时间" prop="returnTime"/>
          <el-table-column label="创建时间" width="160" prop="gmtCreate"/>

        </el-table>
      </div>
      <!--            分页-->
      <Pagination
          v-show="tableData != null || tableData.length != 0"
          :total="paginationInfo.total"
          :pageSize.sync="paginationInfo.pageSize"
          :currentPage.sync="paginationInfo.currentPage"
          @currentChange="getTableData"
          @sizeChange="getTableData"
      />
    </div>
    <!--高级查询-->
    <el-dialog class="dialogHeightAuto" v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
        <el-row>
          <el-col :span="22">
            <el-form-item label="商品名称：">
              <el-input
                  clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.productName"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="商品编码：">
              <el-input
                  clearable maxlength="100" placeholder="请输入商品编码" v-model="filterData.serialNum"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="供货机构：">
              <el-input
                  clearable maxlength="100" placeholder="请输入供货机构"
                  v-model="filterData.purchasingAgencyName"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="价格以上：">
              <el-input
                  clearable v-model="filterData.abovePrice" placeholder="请输入价格区间"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="价格以下：">
              <el-input
                  clearable type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="创建时间：">
              <el-date-picker style="width: 100%;"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="filterData.createDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import ImportExcel from '@/components/importExcel.vue'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapMutations, mapState } from 'vuex'
import {
    supplierStockList,
} from '@/api/supplierSys/stock/stockManage'

export default {
    components: {
    // eslint-disable-next-line vue/no-unused-components
        SelectMaterialClass, Pagination, ImportExcel
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            //fixedNum: null,
            productId: null,
            showJcLoading: false,
            fileUrl: '', //上传文件的域名地址
            limitNum: 1, //文件上传个数限制
            fileList: [], //文件列表
            shopState: '', //店铺状态
            showLoading: false,
            showImportExcelLoading: false,
            deviceInventoryLoading: false,
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
                productType: 0,
                orderBy: 2,
                recordType: null,
                keywords: null,
            },
            // 商品库
            showDeviceDialog: false,
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 0,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                supplierName: null,
                productName: null,
                serialNum: null,
                purchasingAgencyName: null,
                belowPrice: null,
                abovePrice: null,
                createDate: [], // 创建时间
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            excelResult: []
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    },
    methods: {
        toDetail () {
            this.skipView( { productId: this.productId } )
            this.showJcLoading = false
        },
        handleChangeSort (value) {
            this.init.orderBy = value
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 选择
        selectDeviceRow (value) {
            this.inventory.selectRow = value
        },
        skipView (data) {
            this.$router.push({
                path: '/supplierSys/analysis/inOnBoundRecordDetail',
                name: 'inOnBoundRecordDetail',
                params: {
                    row: data
                }
            })
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        exportData () {

        },
        // 物资详情
        handleView (row) {
            row.classPath = this.classPath
            this.skipView(row)
        },
        highSearch () {
            this.resetSearchConditions()
            this.queryVisible = true
        },
        resetSearchConditions () {
            this.filterData.productName = ''
            this.filterData.serialNum = ''
            this.filterData.purchasingAgency = ''
            this.filterData.createDate = []
            this.filterData.belowPrice = ''
            this.filterData.abovePrice = ''
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 批量删除
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.getTableData()
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                state: this.init.state,
                shopId: this.init.shopId,
                productType: this.init.productType,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.serialNum != null) {
                params.serialNum = this.filterData.serialNum
            }
            if (this.filterData.purchasingAgency != null) {
                params.purchasingAgency = this.filterData.purchasingAgency
            }
            if (this.init.keywords != null) {
                params.keywords = this.init.keywords
            }
            if (this.filterData.createDate != null) {
                params.startCreateDate = this.filterData.createDate[0]
                params.endCreateDate = this.filterData.createDate[1]
            }
            if (this.init.recordType != null) {
                params.recordType = this.init.recordType
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            this.tableLoading = true
            supplierStockList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.el-dialog__body {
  margin: 220px;
}

.base-page .left {
  min-width: 200px;
  height: 100%;
  padding: 0;
  overflow: auto;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .el-dropdown {
  min-width: 75px;
  margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.e-table {
  min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}
.dialogHeightAuto /deep/ .el-dialog__body {
  height: auto;
}
</style>
