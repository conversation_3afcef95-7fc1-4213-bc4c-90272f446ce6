<template>
    <main>
        <div class="banner">
            <el-carousel height="540px">
                <el-carousel-item v-for="item in imgList" :key="item">
                    <el-image style="width: 100%; height: 100%" :src="imgUrlPrefixAdd + item" fit="cover"></el-image>
                </el-carousel-item>
            </el-carousel>
        </div>
        <div class="content center">
            <div class="entrance dfb">
                <img class="pointer" src="@/assets/images/huicai.png" @click="openWindowTab('/mFront/mallIndex')" alt="">
                <img class="pointer" src="@/assets/images/zhaocai.png" @click="openWindowTab('/mFront/biddingIndex')" alt="">
            </div>
            <div class="company-news">
                <div class="content-box">
                    <div><img src="@/assets/images/tit_news.png" alt="" class="tit-img"></div>
                    <div class="dfb">
                        <div class="news-item pointer" v-for="item in newsList" :key="item.contentId" @click="handleViewNews(item.contentId)">
                            <img :src="item.bannerImg ? imgUrlPrefixAdd + item.bannerImg : require('@/assets/images/img/queshen5.png')" alt="">
                            <div class="release-time dfa">
                                <img src="@/assets/images/ico_time.png" alt="">
                                <span>{{ item.gmtRelease.slice(0, 10) }}</span>
                            </div>
                            <div class="textOverflow2">{{ item.title }}</div>
                        </div>
                        <div class="list-of-more">
                            <div class="item pointer" v-for="item in newsListOfMore" :key="item.title" @click="handleViewNews(item.contentId)">
                                <div class="textOverflow2">{{ item.title }}</div>
                                <div class="release-time dfa">
                                    <img src="@/assets/images/ico_time.png" alt="">
                                    <span>{{ item.gmtRelease.slice(0, 10) }}</span>
                                </div>
                            </div>
                            <div v-if="newsListOfMore.length > 0" class="button center pointer" @click="openWindowTab('/mFront/newsList')">查看更多</div>
                            <el-empty v-else :image="require('@/assets/images/ico_kong.png')" description="暂无更多" style="height: 100%"></el-empty>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bidding-announcement">
                <div class="content-box">
                    <div><img src="@/assets/images/tit_zbgg.png" alt="" class="tit-img"></div>
                    <div class="df">
                        <div class="bidding-item pointer" v-for="item in bidList" :key="item.billId" @click="goDetail(item)">
                            <div class="bidding-company">{{ item.orgName }}</div>
                            <div class="bidding-title">{{ item.tenderName }}</div>
                            <div class="end-time">截止时间：{{ item.tenderEndTime }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="shop-display">
            <div class="content-box">
                <div><img class="tit-img" src="../../assets/images/tit_sjzs.png" alt=""></div>
                <div class="companies-box">
                    <div
                        @click="openWindowTab({path: '/mFront/shopIndex', query: {shopId: item.shopId}})"
                        class="company-item" v-for="(item, i) in companiesList" :key="i"
                    >
                        <img
                            class="logo"
                            :src="item.shopImg ? imgUrlPrefixAdd + item.shopImg : require('@/assets/images/img/queshen5.png')"
                            alt=""
                        >
                        <p>{{ item.shopName }}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 友情链接 -->
        <div class="linkBox">
            <div class="friendshipBox dfa">
                <span>友情链接：</span>
                <!-- <div> -->
                <div class="item pointer" v-for="(item, i) in friendLinks" :key="i" @click="openWindowTab(item.url)">
                    {{ item.name }}<i class="el-icon-arrow-right"></i>
                </div>
                <!-- </div> -->
            </div>
        </div>
        <el-dialog
            class="front"
            width="80%"
            top="10vh"
            :visible.sync="termsVisible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            destroy-on-close
        >
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>物资商城协议</div>
                    </div>
                    <!--<div class="dialog-close" @click="termsVisible = false">
                        <img src="@/assets/images/close.png" alt=""/>
                    </div>-->
                </div>
                <div></div>
            </div>
            <div class="dialog-body">
                <div class="term-content p20 mb10" style="height: 650px; overflow: auto">
                    <p v-html="mallProtocol"></p>
                </div>
                <el-checkbox v-model="readTerm">
                    我已确认阅读并同意<span style="color: #66b1ff">《物资商城协议》</span>
                </el-checkbox>
            </div>
            <span class="dfc" slot="footer">
                <el-button @click="confirmTerms" type="primary">确认</el-button>
            </span>
        </el-dialog>
<!--        初审通过弹窗-->
        <el-dialog
            class="shopDialog"
            width="80%"
            top="10vh"
            v-dialogDrag
            :visible.sync="shopDialog"
            :close-on-click-modal="true"
            :close-on-press-escape="false"
            destroy-on-close
        >
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>温馨提示</div>
                    </div>
                </div>
                <div></div>
            </div>
            <div style="height: 200px" v-if="diaLogMap != null && diaLogMap.checkShopCode != null">
                <div style="margin-top: 20px">{{diaLogMap.checkShopMessage}}</div>
            </div>
            <span class="dfc" slot="footer">
                <el-button @click="shopDialogAffirm">否</el-button>
                <el-button @click="outFee" v-if="diaLogMap!= null && diaLogMap.checkShopMessage != null && diaLogMap.checkShopMessage.length > 20" type="primary">前往缴费</el-button>
                <el-button @click="outFee" v-else type="primary">是</el-button>
            </span>
        </el-dialog>
        <el-dialog
            class="shopDialog"
            width="80%"
            top="10vh"
            v-dialogDrag
            :visible.sync="shopDialog2"
            :close-on-click-modal="true"
            :close-on-press-escape="false"
            destroy-on-close
        >
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>温馨提示</div>
                    </div>
                </div>
                <div></div>
            </div>
            <div style="height: 200px" v-if="diaLogMap != null && diaLogMap.checkBidCode != null">
                <div style="margin-top: 20px">{{diaLogMap.checkBidMessage}}</div>
            </div>
            <span class="dfc" slot="footer">
                <el-button @click="shopDialogAffirm2">否</el-button>
                <el-button @click="outFee2" type="primary">是</el-button>
            </span>
        </el-dialog>
        <el-dialog
            class="shopDialog"
            width="80%"
            top="10vh"
            v-dialogDrag
            v-loading="bidDialogLoading"
            :visible.sync="showBidDialog"
            :close-on-click-modal="true"
            :close-on-press-escape="false"
            destroy-on-close
        >
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>温馨提示</div>
                    </div>
                </div>
                <div></div>
            </div>
            <div style="height: 200px" >
                <div style="margin-top: 20px">检测到您未开通电子招标服务，是否开启电子招标服务？</div>
            </div>
            <span class="dfc" slot="footer">
                <el-button @click="updateUserIsShowBid">否</el-button>
                <el-button @click="openBidFeeServer" type="primary">是</el-button>
            </span>
        </el-dialog>
        <Dialog title="密码修改提醒" :visible.sync="alertPwdDialogVisible" height="20px">
           <div v-show="pwdAlertType == 1" style="margin-top: 25px;margin-bottom: 20px">
            您的密码将于<span style="color: orangered">{{removeHours(pwdData.time)}}</span>到期，请尽快去修改密码。
            </div>
            <div v-show="pwdAlertType == 2" style="margin-top: 25px;margin-bottom: 20px">
                您的密码已到期，请尽快去修改密码。
            </div>
            <span slot="footer">
                <el-button @click="alertPwdDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleToChange">修改</el-button>
            </span>
        </Dialog>
         <!--   消息提醒     -->
        <Dialog
            class="front" title="消息提醒" width="55%" top="20vh" :visible.sync="msgAlertVisible"  :before-close="handleClose" :close-on-click-modal="false"
        >
            <el-table
                v-show="!viewShow"
                ref="msgTable"
                height="250"
                :data="msgInfo.list"
                :header-row-style="{ fontSize: '16px', color: '#216EC6', fontWeigth: '500' }"
                :row-style="{ fontSize: '14px', color: '#666666', height: '48px' }"
            >
                <el-table-column prop="sendDate" label="发送时间" width="190px">
                </el-table-column>
                <el-table-column prop="sendName" label="发信人" width="120px">
                </el-table-column>
                <el-table-column prop="title" label="主题" width="223px">
                    <template v-slot="scope">
                        <div class="textOverflow1">{{ scope.row.title }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="count" label="内容摘要" width="223px">
                    <template v-slot="scope">
                        <div class="textOverflow2" style="max-height: 43px;">
                            {{ stripHtmlTags(scope.row.content) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100px">
                    <template v-slot="scope">
                        <div class="action">
                            <!--                            <span @click="handleDeleteMsg(scope.row.stationMessageReceiveId)">删除</span>-->
                            <span class="pointer" @click="handleViewMsg(scope.$index)">查看</span>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="right" v-show="viewShow">
                <!-- ---------------------消息查看窗口--------------------- -->
                <div class="e-form" style="padding: 0 10px 10px;">
                    <div class="tabs-title">我的消息</div>
                    <el-form ref="formEdit" :model="unreadData" label-width="150px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="发件人名称：" prop="sendName">
                                    <span>{{ unreadData.sendName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="时间：" prop="sendDate">
                                    <span>{{ unreadData.sendDate }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="标题：" prop="title">
                                    <span>{{ unreadData.title }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24" class="editorCol">
                                <el-form-item label="内容：" prop="content">
                                    <div v-html="unreadData.content"></div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div class="p20 e-form"  style="overflow: auto;"  >
                    <div class="tabs-title mb10">附件资料</div>
                    <el-table border :data="fileList" :header-cell-style="{ background: '#f7f7f7' }" v-loading="fileLoading">
                        <el-table-column label="序号" type="index" width="120"></el-table-column>
                        <el-table-column prop="name" label="附件名称" width=""></el-table-column>
                        <el-table-column label="操作" width="240">
                            <template slot-scope="scope">
                                <el-button type="primary" class="pointer" @click="handleDownload(scope.row)">下载</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <!--                <div class="theBtn" @click="updateMessageState(unreadData.stationMessageReceiveId)">确定-->
                <!--                </div>-->
                <div class="theBtn pointer" v-if = "showNext" @click="nextMsg">下一条
                </div>
                <div class="theBtn pointer" v-else   @click="handleClose">关闭
                </div>
            </div>

        </Dialog>
        <el-dialog
            v-loading="showAddFeeLoading"
            top="5vh"
            class="showImage"
            title="电子招标服务申请 "
            v-dialogDrag
            :close-on-click-modal="false"
            :visible.sync="showAddFee"
            width="70%"
        >
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>电子招标服务申请</div>
                    </div>
                </div>
                <div></div>
            </div>
            <el-form label-width="200px"  ref="showAddFeeRef"  :data="addFeeForm"  :rules="showAddFeeRoles">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户公司名称：">
                            {{platformAccountObj.platformFreeyhOrgName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="收款账户开户行：">
                            {{platformAccountObj.platformFreeyhAddress}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收款账户：">
                            {{platformAccountObj.platformFreeyhAccount}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费方式：" prop="payType">
                            <el-radio v-model="addFeeForm.payType" :label="1" >线下</el-radio>
                            <el-radio v-model="addFeeForm.payType" disabled :label="2" >线上</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收费标准" prop="tot">
                            <span style="color: red">2000元/年</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费时长单位：" prop="paymentDurationType">
                            <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="1" >天</el-radio>-->
                            <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="2" >周</el-radio>-->
                            <!--                            <el-radio v-model="addFeeForm.paymentDurationType" :label="3" >月</el-radio>-->
                            <el-radio v-model="addFeeForm.paymentDurationType" :label="4" >年</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="缴费时长：" prop="paymentDuration">
                            <el-input placeholder="请输入缴费时长" clearable type="number"  @change="checkInputQty" v-model="addFeeForm.paymentDuration"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" >
                        <el-form-item label="缴费金额（元）：" prop="payAmount">
                           <span style="color: red">
                               {{addFeeForm.payAmount}}
                           </span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks">
                            <el-input
                                type="textarea"
                                :auto-resize="false"
                                v-model="addFeeForm.remarks"
                                placeholder="请输入备注，格式：单位名称+缴费金额" maxlength="1000"
                                show-word-limit
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item  label="缴费证明：" prop="file">
                            <el-upload
                                :class="addFeeForm.files.length === 1 ? 'hide_box_min' : ''"
                                v-loading="uploadLoading"
                                class="upload-demo"
                                action="fakeaction"
                                :limit="1"
                                :file-list="fileList2"
                                :before-upload="handleBeforeUpload"
                                :auto-upload="true"
                                :http-request="uploadLicenseBusiness"
                                list-type="picture-card">
                                <div slot="tip" class="el-upload__tip">只能上传图片文件</div>
                                <i slot="default" class="el-icon-plus"></i>
                                <div slot="file" slot-scope="{file}">
                                    <img
                                        class="el-upload-list__item-thumbnail"
                                        :src="file.url" alt="">
                                    <span class="el-upload-list__item-actions">
                                    <span
                                        class="el-upload-list__item-preview"
                                        @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="handleDownload2(file)">
                                      <i class="el-icon-download"></i>
                                    </span>
                                    <span
                                        class="el-upload-list__item-delete"
                                        @click="formDtlFileRemove(file)">
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </span>
                                </div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
<!--                <el-button type="primary" class="btn-greenYellow" @click="save(0)">保存</el-button>-->
                <el-button type="primary" class="btn-greenYellow"  @click="save(1)">提交</el-button>
                <el-button @click="showAddFee = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog class="showImage" v-dialogDrag :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <!--        消息查看窗口-->
    </main>
</template>
<script>
import { getTenderLogOutPages } from '@/api/frontStage/bidding'
import { getIndexShopList } from '@/api/w/indexShop'
import { getInfo } from '@/api/platform/content/aboutUs'
import { getMaterialHomeInfo, getRemindMessage, updateIsShowBid } from '@/api/frontStage/homePage'
import { checkExpiring, tokenLogin } from '@/api/frontStage/login'
import { mapActions, mapState } from 'vuex'
import { Loading } from 'element-ui'
import Dialog from '@/pages/frontStage/components/dialog.vue'
import { removeHours, stripHtmlTags } from '@/utils/common'
import { selectFileList } from '@/api/base/file'
import { updateState } from '@/api/frontStage/messages'
import { previewFile, uploadFile } from '@/api/platform/common/file'
import {
    createFileRecordDelete,
    getPlatformFreeAccountAndAddress
} from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { createFeeAndDealFree } from '@/api/fee/feeApi'
import { isUserAdrees } from '@/api/frontStage/shippingAddr'

export default {
    components: { Dialog },
    watch: {
    },
    data () {
        return {
            platformAccountObj: {},
            showAddFeeRoles: {
                paymentDuration: [
                    { required: true, validator: this.validateAddress, trigger: 'blur' },
                ],
                payAmount: [
                    { required: true, validator: this.validateAddress, trigger: 'blur' },
                ],
                file: [
                    { required: true, message: '请输入证明', trigger: 'blur' },
                ],
            },
            addFeeForm: {
                state: 0,
                payType: 1,
                recordType: 2,
                paymentDurationType: 4,
                paymentDuration: 0,
                files: [],
                payAmount: 0,
                remarks: null,
            },
            freeAmount: 2000,
            showAddFee: false,
            uploadLoading: false,
            showAddFeeLoading: false,
            // 招标开通服务以上
            alertPwdDialogVisible: false,
            showBidDialog: false,
            bidDialogLoading: false,
            // 弹窗提示切换
            pwdAlertType: 0,
            pwdData: {

            },
            friendLinks: [],
            imgList: [],
            bidList: [],
            newsList: [],
            newsListOfMore: [],
            companiesList: [],
            shopDialog: false,
            shopDialog2: false,
            diaLogMap: null,
            msgAlertVisible: false,
            viewShow: false,
            dialogVisible: false,
            dialogImageUrl: null,
            // 消息弹窗
            msgInfo: {
                count: 0,
                list: []
            },
            uploadImgSize: 10, // 上传文件大小
            currentIndex: 0,
            // 待阅读data
            unreadData: {},
            fileList: [],
            fileList2: [],
            fileLoading: false,
            termsVisible: false,
            readTerm: false,
            mallProtocol: ''
        }
    },
    computed: {
        ...mapState(['userInfo']),
        showNext () {
            return !(this.currentIndex === this.msgInfo.list.length - 1)
        }
    },
    mounted () {
        if(this.$route.query.bid) {
            getPlatformFreeAccountAndAddress().then(res => {
                this.platformAccountObj = res
                this.thisEnName = res.enterpriseName
                this.addFeeForm.recordType = 2
                this.addFeeForm.remarks = this.thisEnName + ' 电子招标年度服务费'
                this.showAddFee = true
            }).finally(() =>{
            })
        }
    },
    async created () {
        if(this.userInfo.isShowBid != null && this.userInfo.isShowBid == 1) {
            this.showBidDialog = true
            this.userInfo.isShowBid = 0
            this.$store.commit('setUserInfo', {
                ...this.userInfo
            })
        }
        let token = this.$route.query.token
        if (token != null) {
            let loading = Loading.service({
                lock: true,
                text: '加载中……',
                background: 'rgba(0, 0, 0, 0.4)'
            })
            localStorage.removeItem('token')
            this.$store.commit('setUserInfo', {})
            let res = await tokenLogin({ token, mallType: 0 })
            if (!res.token) this.$router.push('/login')
            await this.setToken(res)
            loading.close()
            window.open('/', '_self')
        }
        this.getMallProtocol()
        this.getShopDia()
        this.getHomeInfo()
        this.getIndexShopListM()
        await this.handleExpire()
        this.getTenderPage()
        if (this.showDevFunc && this.$route.query.bid == false) {
            this.getRemindMsg()
        }

    },
    methods: {
        removeHours,
        //下载文件
        async handleDownload (file) {
            this.fileLoading = true
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes], { type: 'application/pdf' })
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
            this.fileLoading = false
        },
        nextMsg () {
            this.currentIndex = this.currentIndex + 1
            this.handleViewMsg(this.currentIndex)
        },
        // eslint-disable-next-line no-unused-vars
        formDtlFileRemove (file, fileList) {
            this.uploadLoading = true
            createFileRecordDelete({ recordId: this.addFeeForm.files[0].fileFarId }).then(res => {
                if(res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.addFeeForm.files = []
                this.fileList2 = []
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        // eslint-disable-next-line no-unused-vars
        handleDownload2 (file) {
            this.uploadLoading = true
            let image = this.addFeeForm.files[0]
            previewFile({ recordId: image.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = url
                a.download = image.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        uploadLicenseBusiness (params) {
            let file = params.file
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 1)
            this.uploadLoading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.addFeeForm.files = []
                    this.fileList2 = []
                }else {
                    let resO = res[0]
                    this.addFeeForm.files.push({
                        name: resO.objectName,
                        relevanceType: 1,
                        url: resO.nonIpObjectPath,
                        fileFarId: resO.recordId,
                        fileType: 1
                    })
                }
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            }).finally(() =>{
                this.uploadLoading = false
            })
        },
        checkInputQty () {
            const regex = /^-?\d+$/
            if (!regex.test(this.addFeeForm.paymentDuration)) {
                this.$message.error('请输入有效的数字')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            if (this.addFeeForm.paymentDuration < 0 || this.addFeeForm.paymentDuration > 999) {
                this.$message.error('超过限制！')
                this.addFeeForm.payAmount = this.freeAmount
                return this.addFeeForm.paymentDuration = 1
            }
            this.addFeeForm.payAmount = this.addFeeForm.paymentDuration * this.freeAmount
        },
        save (num) {
            if (this.addFeeForm.files.length == 0) {
                return this.$message.error('请上传缴费证明！')
            }
            if( this.addFeeForm.recordType == 1 || this.addFeeForm.recordType == 2) {
                if(this.addFeeForm.paymentDuration <= 0) {
                    return this.$message.error('缴费时长需大于0！')
                }
            }
            if(this.addFeeForm.payAmount <= 0) {
                return this.$message.error('缴费金额需大于0！')
            }
            this.clientPop('info', '您确定要进行该操作吗？', async () => {
                this.addFeeForm.submitAud = num
                this.showAddFeeLoading = true
                createFeeAndDealFree(this.addFeeForm).then(res => {
                    if (res.code == null) {
                        this.$message.success('操作成功')
                        this.addFeeForm = {
                            state: 0,
                            payType: 1,
                            recordType: 2,
                            paymentDurationType: 4,
                            paymentDuration: 0,
                            files: [],
                            payAmount: 0,
                            remarks: null,
                        }
                        this.fileList2 = []
                        this.showAddFee = false
                        this.updateUserIsShowBid()
                    }
                }).finally(() => {
                    this.showAddFeeLoading = false
                })
            })
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleClose () {
            this.msgAlertVisible = false
        },
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if(!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        updateMessageState (stationMessageReceiveId) {
            updateState({ id: stationMessageReceiveId }).then(res => {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            })
        },
        async  handleViewMsg (index) {
            // 更新已读状态
            // 移除数组中已读元素
            this.currentIndex = index
            this.viewShow = true
            this.unreadData = this.msgInfo.list[index] || {}
            await  this.getFileInfos(this.unreadData.stationMessageId)
            this.updateMessageState(this.unreadData.stationMessageReceiveId)
        },
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: 7,
            }
            selectFileList(params).then(res=>{
                this.fileList = res.list
            })
        },
        getRemindMsg () {
            let token = localStorage.getItem('token') || this.userInfo.token
            if (!token) return
            getRemindMessage().then(res => {
                this.msgInfo.count = res.count
                if (res.count > 0) {
                    this.msgInfo.list = res.list || []
                    this.msgAlertVisible = true
                }else {
                    this.msgAlertVisible = false

                }
            })
        },
        stripHtmlTags,
        handleToChange () {
            this.$router.push({ path: '/user/changePass', name: 'changePass' })
        },
        async handleExpire () {
            //  当前密码是否超期修改，超时则弹出提示
            if (this.userInfo.isInterior === 0) {
                if (this.userInfo.isInterior === 0) {
                    let time =  await checkExpiring({ phone: this.userInfo.userMobile })
                    if (time.state == 1) {
                        this.alertPwdDialogVisible = true
                        this.pwdAlertType = 1
                        this.pwdData.time = time.endTime
                    }
                    if (time.state == 2) {
                        this.alertPwdDialogVisible = true
                        this.pwdAlertType = 2
                        this.pwdData.time = time.endTime
                    }
                }
            }
        },
        ...mapActions(['setToken']),
        async getMallProtocol () {
            if (localStorage.getItem('firstLogin') !== 'true') return
            let { content } = await getInfo({ programaKey: 'internalUserProtocol' })
            this.mallProtocol = content
            this.termsVisible = true
        },
        async getShopDia () {
            let diaLogMap = localStorage.getItem('diaLogMap')
            if (diaLogMap == null) return
            this.diaLogMap = JSON.parse(diaLogMap)
            // if(this.diaLogMap != null && this.diaLogMap.checkShopCode != null && this.userInfo && this.userInfo.userId) {
            //     this.shopDialog = true
            // }
            if(this.diaLogMap != null && this.diaLogMap.checkBidCode != null && this.userInfo && this.userInfo.userId) {
                this.shopDialog2 = true
            }
        },
        // 开通招标服务
        openBidFeeServer () {
            getPlatformFreeAccountAndAddress().then(res => {
                this.platformAccountObj = res
                this.thisEnName = res.enterpriseName
                this.addFeeForm.recordType = 2
                this.showAddFee = true
            }).finally(() =>{
            })
        },
        updateUserIsShowBid () {
            this.bidDialogLoading = true
            updateIsShowBid().then(res => {
                console.log(res)
                this.showBidDialog = false
            }).finally(() =>{
                this.bidDialogLoading = false
            })
        },
        outFee () {
            this.shopDialog = false
            localStorage.removeItem('diaLogMap')
            if(this.diaLogMap.checkShopCode == 700199) {
                return this.openWindowTab('/mFront/contractSigning')//店铺合同签约及缴费
            }
            this.$router.push({ path: '/supplierSys/fee/payRecordManage', name: 'supplierSysFeePayRecordManage', query: { show: true } })
        },
        //时间验证
        validateAddress (rule, value, callback) {
            callback()
        },
        outFee2 () {
            this.$router.push({ path: '/supplierSys/fee/payRecordManage', name: 'supplierSysFeePayRecordManage', query: { isbid: true } })
        },
        shopDialogAffirm () {
            this.shopDialog = false
            localStorage.removeItem('diaLogMap')
        },
        shopDialogAffirm2 () {
            this.shopDialog2 = false
            localStorage.removeItem('diaLogMap')
        },
        confirmTerms () {
            if (!this.readTerm) return this.$message.warning('请勾选已阅读并同意商城协议')
            this.termsVisible = false
            localStorage.removeItem('firstLogin')
        },
        handleViewNews (id) {
            this.openWindowTab({ path: '/mFront/newsDetail', query: { id } })
        },
        async isUserAdress () {
            let { isSubmitOrder, isInterior } = this.userInfo
            if(isInterior === 1) {
                if(isSubmitOrder == null || isSubmitOrder === 0) {
                    return
                }else {
                    isUserAdrees().then(res=>{
                        if (res.code != 200) {
                            this.clientPop('info', '用户还没有设置收货地址，请前往个人中心设置公司的收货地址', async () => {
                                this.$router.replace('/user/shippingAddr')
                            })
                        }
                    })
                }
            }
        },
        async getIndexShopListM () {
            this.companiesList = await getIndexShopList({ size: 12 })
        },
        async getHomeInfo () {
            let params = {
                maxAdImg: 4,
                maxLinks: 8,
                maxNews: 6,
                mallType: 0
            }
            let { data } = await getMaterialHomeInfo(params)
            let { adPictures, links, newses } = data
            // 广告图
            this.imgList = adPictures.map(item => item.pictureUrl)
            // 友情链接
            this.friendLinks = links
            // this.homePageBids = result.homePageBids
            // 最新动态
            this.newsList = newses
            this.newsListOfMore = newses.splice(3)
        },
        async getTenderPage () {
            let params = {
                limit: 4,
                page: 1,
                tenderForm: 0, //采购方式
                mallConfig: 0
            }
            let { list } = await getTenderLogOutPages(params)
            this.bidList = list
        },
        goDetail (row) {
            this.$router.push({
                path: '/front/bidding/detail',
                name: 'biddingDetail',
                params: { row }
            })
        },

    },
}
</script>
<style scoped lang="scss">
@import '../../assets/css/frontStage.scss';

/deep/ .el-carousel__indicators--horizontal {
    margin-bottom: 53px;
}

main {
    background-color: #F5F5F5;

    .banner {
        min-width: 1354px;
        height: auto;
        //aspect-ratio: 96/35;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .content {
        width: 1354px;
        margin-bottom: 50px;
        padding-top: 244px;
        position: relative;

        & > div {
            background-color: #fff;
        }
    }

    .entrance {
        width: 100%;
        position: absolute;
        top: -53px;
        left: 0;
        z-index: 100;
        padding: 50px;

        img {
            width: 603px;
        }
    }

    .bidding-announcement {
        padding: 31px 0 53px;

        .df {
            padding: 0 10px;
            flex-wrap: wrap;

            .bidding-item {
                width: 50%;
                height: 216px;
                padding: 16px 40px 0;
                border: 10px solid #fff;
                background: linear-gradient(360deg, #FFFFFF 0%, #CAE1FA 100%);
                position: relative;

                &:nth-child(-n+2) {
                    margin-bottom: 29px;
                }

                .bidding-company {
                    margin-bottom: 22px;
                    font-size: 16px;
                    color: #216EC6;
                }

                .bidding-title {
                    font-size: 20px;
                    color: #000000;
                }

                .end-time {
                    width: 306px;
                    height: 35px;
                    margin-left: -153px;
                    font-size: 14px;
                    text-align: center;
                    line-height: 35px;
                    color: #fff;
                    background-image: url(../../assets/images/end_time_bgc.png);
                    background-size: 100%;
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                }
            }
        }
    }

    .company-news {
        margin: 50px 0;
        padding: 31px 0 22px;

        .dfb {
            padding: 0 22px;

            .textOverflow2 {
                font-size: 14px;
                line-height: 19px;
            }

            & > div {
                height: 323px;
            }

            .release-time {
                font-size: 12px;

                img {
                    width: 12px;
                    height: 12px;
                    margin-right: 3px;
                }
            }

            .news-item {
                width: 310px;

                & > img {
                    width: 100%;
                    height: 237px;
                    object-fit: cover;
                }

                .release-time {
                    color: #484848;
                    margin: 11px 0 9px;
                }
            }

            .list-of-more {
                width: 287px;
                padding: 6px 17px 0;
                color: #484848;
                background-color: #F6FAFE;
                position: relative;

                .item {
                    padding: 16px 0 7px;
                    border-bottom: 1px dashed #C3C3C4;

                    .release-time {
                        margin-top: 8px;
                    }
                }

                .button {
                    width: 190px;
                    height: 29px;
                    margin-top: 14px;
                    font-size: 14px;
                    line-height: 29px;
                    text-align: center;
                    color: #fff;
                    background-color: #216EC6;
                    position: absolute;
                    bottom: 20px;
                    left: 49px;
                }
            }
        }
    }

    .shop-display {
        padding: 31px 0 80px 0;
        background-color: #fff;

        .companies-box {
            display: flex;
            flex-wrap: wrap;

            .company-item {
                width: 225px;
                //height: 250px;
                // border: 1px solid #d9d9d9;

                img {
                    width: 80px;
                    height: 80px;
                    margin: 50px auto 4px;
                    background-color: beige;
                    //border-radius: 50%;
                    display: block;
                }

                p {
                    width: 180px;
                    margin: 0 auto;
                    text-align: center;
                    line-height: 22px;
                    word-break: break-all;
                }
            }
        }
    }

    .content-box {
        height: inherit;

        & > div:first-child {
            margin-bottom: 30px;

            .tit-img {
                margin: 0 auto;
                display: block;
            }
        }
    }

    img.tit-img {
        height: 63px;
    }
}

.linkBox {
    background-color: #f5f5f5;
}

.friendshipBox {
    margin: 0 auto;
    width: 1350px;
    height: 180px;
    padding: 0 30px;

    span {
        font-size: 22px;
        color: #808080;
        font-weight: 600;
    }

    .item {
        background: #ffffff;
        border: 1px solid rgba(217, 217, 217, 1);
        padding: 20px;
        font-size: 22px;
        color: #333333;
        font-weight: 400;
        margin-right: 20px;

        i {
            color: #999;
        }
    }
}

.dialog-body {
    min-height: 670px;
    padding-left: 20px;
    padding-right: 20px;
}

/deep/ .el-dialog__footer {
    padding: 0 10px;

    .el-button {
        width: 120px;
        height: 40px;
        line-height: 40px;
        border-radius: 0;
    }
}
.theBtn {
    width: 120px;
    height: 40px;
    margin: 20px auto 20px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    background-color: #216EC6;
}
/deep/ .el-dialog {
    //width: 800px !important;
    //height: 450px !important;
    //margin-top: 161px !important;

    .dialog-body {
        //width: 630px;
        padding: 50px 0 10px;
        padding-top: 2px  !important;
        .pay-alert-main {
            margin-bottom: 40px;
            padding-left: 30px;
            font-size: 24px;
        }

        .pay-alert-warning {
            padding-left: 30px;
            font-size: 20px;
            color: orangered;
        }

        .company-info {
            height: 45px;
            margin-bottom: 30px;
            padding: 15px;
            background: #F7F7F7;

            div {
                font-size: 14px;

                span:first-child {
                    color: #999;
                }

                span:last-child {
                    color: #226FC7;
                }
            }
        }

        .row {
            margin-bottom: 30px;

            .col {
                width: 300px;
            }

            .el-input__inner {
                height: 35px;
                border-radius: 0;
            }

            .el-textarea__inner {
                    height: 70px !important;
                padding: 11px 10px;
                border-radius: 0;
                resize: none;
            }
        }

        .el-form-item {
            margin-bottom: 0;
        }

        .el-form-item__label {
            height: 24px;
            line-height: 14px;
            color: #999;
        }

        .btn {
            width: 80px;
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            text-align: center;
            color: #fff;
            background-color: #216EC6;
            cursor: pointer;
            user-select: none;
        }
    }
}
/deep/ .showImage {
    .el-dialog__header {
        padding-bottom: 30px;
        margin-bottom: 0px;
    }
    .el-dialog__body {
        height: 680px;
        margin: 10px;
    }
    .el-dialog__footer {
    }
}
/deep/ .shopDialog {
    height: 600px;
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}
</style>