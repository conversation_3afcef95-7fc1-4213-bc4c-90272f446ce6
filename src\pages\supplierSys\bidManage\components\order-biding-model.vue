<!-- 生成订单竞价 -->
<template>
    <div>
        <el-dialog v-loading="bidingFormLoading" v-dialogDrag id="bidingDialog" title="生成订单竞价"
            :visible.sync="showOrderBidingVisible" width="90%" style="margin-left: 10%" :close-on-click-modal="false">
            <el-divider content-position="left">竞价信息</el-divider>
            <el-form :model="bidingForm" :rules="bidingFormRules" label-width="200px" ref="bidingFormRef"
                :disabled="false" class="demo-ruleForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品类型：" prop="productType">
                            <el-radio-group v-has-permi="{ platform: 'oneselfShopManage', auth: 'created-lxBid', }"
                                v-model="bidingForm.productType">
                                <el-radio :label="0">零星采购</el-radio>
                                <el-radio :label="1">大宗临购</el-radio>
                                <el-radio :label="2">周转材料</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-show="bidingForm.productType === 1 || bidingForm.productType === 2">
                        <el-form-item label="价格类型：" prop="billType">
                            <el-radio-group v-has-permi="{ platform: 'oneselfShopManage', auth: 'created-lxBid', }"
                                v-model="bidingForm.billType">
                                <el-radio :label="1">浮动价格</el-radio>
                                <el-radio :label="2">固定价格</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="标题：" prop="title">
                            <el-input maxlength="200" placeholder="请输入标题" clearable
                                v-model="bidingForm.title"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="截止时间：" prop="endTime">
                            <el-date-picker style="width: 100%;" v-model="bidingForm.endTime" type="datetime"
                                placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系人：" prop="linkName">
                            <el-input maxlength="10" placeholder="请输入联系人" clearable
                                v-model="bidingForm.linkName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话：" prop="linkPhone">
                            <el-input type="number" clearable v-model="bidingForm.linkPhone" placeholder="请输入11位手机号码" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="竞价类型：" prop="type">
                            <el-radio-group v-model="bidingForm.type">
                                <el-radio :label="1">公开竞价</el-radio>
                                <span> <el-radio :label="2">邀请竞价</el-radio></span>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="bidingForm.type === 2">
                        <el-form-item label="选择供应商：" prop="">
                            <el-button class="btn10" type="primary" @click="showSupplierDialog">选择供应商</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="说明：" prop="biddingExplain">
                            <editor v-model="bidingForm.biddingExplain"></editor>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                    <el-col :span="24">
                        <el-form-item label="竞价函说明：" prop="biddingNotice">
                            <el-input type="textarea" :rows="6" v-model="bidingForm.biddingNotice"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-divider content-position="left">订单商品</el-divider>
            <div class="e-table" style="background-color: #fff" v-loading="biddingOrderItemLoading">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <div class="search_box" style="margin-left: 10px">
                            <el-button type="primary" class="btn10" @click="selectProductDia">选择订单商品</el-button>
                        </div>
                    </div>
                </div>
                <el-table ref="bidingOrderItemRef" border style="width: 100%" :data="bidingFormOrderItems" class="table"
                    :max-height="$store.state.tableHeight">
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" label-width="">
                        <template v-slot="scope">
                            <span class="action" @click="deleteRow(scope.row)">删除</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="state" label="订单明细状态" width="100">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state == 2" type="info">待分配</el-tag>
                            <el-tag v-if="scope.row.state == 3" type="info">待分配竞价</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单编号" width="230" prop="orderSn"></el-table-column>
                    <el-table-column prop="productImg" label="商品图片" width="130">
                        <template v-slot="scope">
                            <el-image style="width: 90px; height: 60px"
                                :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="productName" label="商品名称" width="、"></el-table-column>
                    <el-table-column prop="skuName" label="规格" width="200"></el-table-column>
                    <el-table-column prop="texture" label="材质" width="200"></el-table-column>
                    <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                    <el-table-column prop="buyCounts" label="数量" width="100"></el-table-column>
                    <el-table-column prop="maxPrice" label="最高单价" width="180">
                        <template v-slot="scope">
                            <el-input-number :controls="false" :precision="2"
                                v-model="scope.row.maxPrice"></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <el-button class="btn-blue btn10" @click="createBidingM">生成订单商品竞价</el-button>
                <el-button class="btn10" @click="showOrderBidingVisible = false">返回</el-button>
            </span>
        </el-dialog>

        <el-dialog v-dialogDrag custom-class="dlg" width="90%" title="订单商品选择" :visible.sync="showSelectOrderItem">
            <div class="box-left">
                <div class="e-table">
                    <div class="top">
                        <div style="width: 100%">
                            <el-input type="text" @blur="listBidingOrderListIdsM" placeholder="输入订单编号"
                                v-model="orderSn">
                                <img src="@/assets/search.png" slot="suffix" @click="listBidingOrderListIdsM" alt="" />
                            </el-input>
                        </div>
                    </div>
                    <el-table max-height="340px" @row-click="biddingOrderListClick" v-loading="biddingOrderListLoading"
                        class="table" height="450px" :data="bidingOrderList" border>
                        <el-table-column label="订单编号" width="250" prop="orderSn" />
                        <el-table-column label="创建时间" width="150" prop="gmtCreate">
                            <template v-slot="scope">
                                <span>{{ scope.row.gmtCreate | dateStr }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <myPagination :total="paginationOrderList.total" :current-page="paginationOrderList.currentPage"
                        :page-size.sync="paginationOrderList.pageSize" :page-sizes="[20, 40, 60, 80]"
                        @currentChange="listBidingOrderListIdsM" @sizeChange="listBidingOrderListIdsM" />
                </div>
            </div>
            <div class="box-right">
                <div class="e-table">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div style="width: 200px">
                            <el-input type="text" @blur="listBiddingOrderItemByOrderIdM" placeholder="输入搜索关键字"
                                v-model="selectOrderItemKeywords">
                                <img src="@/assets/search.png" slot="suffix" @click="listBiddingOrderItemByOrderIdM"
                                    alt="" />
                            </el-input>
                        </div>
                        <span style="color: #ff0000; margin-left: 20px">点击选择！</span>
                    </div>
                    <el-table max-height="340px" @row-click="handleCurrentInventoryOrderItemClick"
                        ref="eltableCurrentRow" v-loading="biddingOrderItemSelectLoading" class="table"
                        height="450px" :data="selectOrderItemTable" border>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="productName" label="商品名称" width="200" />
                        <el-table-column prop="spec" label="规格" width="100" />
                        <el-table-column prop="unit" label="单位" width="80" />
                        <el-table-column prop="buyCounts" label="数量" width="100" />
                        <el-table-column prop="classPathName" label="分类路径" width="200" />
                    </el-table>
                    <Pagination v-show="selectOrderItemTable && selectOrderItemTable.length > 0"
                        :total="selectOrderItemPage.total" :pageSize.sync="selectOrderItemPage.pageSize"
                        :currentPage.sync="selectOrderItemPage.currentPage"
                        @currentChange="listBiddingOrderItemByOrderIdM" @sizeChange="listBiddingOrderItemByOrderIdM" />
                </div>
            </div>
            <div class="box-right">
                <div class="e-table">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <span style="color: #ff0000">双击移除！</span>
                    </div>
                    <el-table max-height="340px" class="table" @row-dblclick="removeSelectOrderItemList"
                        height="450px" :data="yesSelectOrderItem" border>
                        <el-table-column label="序号" type="index" width="50"></el-table-column>
                        <el-table-column prop="orderSn" label="订单编号" width="100" />
                        <el-table-column prop="productName" label="商品名称" width="200" />
                        <el-table-column prop="spec" label="规格" width="" />
                        <el-table-column prop="unit" label="计量单位" width="100" />
                        <el-table-column prop="buyCounts" label="数量" width="100" />
                        <el-table-column prop="classPathName" label="分类路径" width="200" />
                    </el-table>
                </div>
            </div>
            <span slot="footer">
                <el-button type="primary" class="btn10" @click="selectOrderItemAffirm">选择物资</el-button>
                <el-button class="btn10" @click="showSelectOrderItem = false">取消</el-button>
            </span>
        </el-dialog>

        <!-- 供应商弹窗 -->
        <SupplierModel ref="supplierModelRef" @getSupplierList="getSupplierList" />
    </div>
</template>

<script>
import { mapState } from 'vuex'
import {
    listBidingOrderItemsList,
    // listMyCreateBiding,
    // submitBidingByIds,
    listBidingOrderListIds,
    // createInventoryBidding,
    // bidOpening,
    // deadlineTime,
    // deleteBidingByBiddingId,
} from '@/api/shopManage/biding/biding'
import Pagination from '@/components/pagination/pagination'
import myPagination from '@/components/pagination/myPagination'
import { createBidingByOrder, } from '@/api/platform/order/orders'
import Editor from '@/components/quillEditor'
import SupplierModel from '../components/supplier-model.vue'
export default {
    name: 'orderBidingModel',
    components: {
        SupplierModel,
        Editor,
        Pagination,
        myPagination
    },
    watch: {
        'bidingForm.endTime': {
            handler () {
                this.formatStr()
                this.$forceUpdate()
            },
        },
        'inventoryBidForm.endTime': {
            handler () {
                this.formatInventoryStr()
                this.$forceUpdate()
            },
        },
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            },
        },
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        // rightTableHeight () {
        //     return this.screenHeight - 292
        // },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return this.screenWidth - 300 + 'px'
        },
        // rightTableHeight2 () {
        //     return this.screenHeight - 210
        // },
    },

    data () {
        return {
            tableData: [], // 表格数据
            materialTableData: [], // 表格数据
            selectOrderItemTable: [], // 表格数据
            yesSelectOrderItem: [], // 表格数据
            showSelectOrderItem: false,
            orderId: null,
            orderSn: null,
            bidingOrderList: [],
            showOrderBidingVisible: false,
            bidingFormLoading: false,
            biddingOrderListLoading: false,
            bidingFormOrderItems: [],
            selectOrderItemKeywords: null,
            biddingOrderItemSelectLoading: false,
            biddingOrderItemLoading: false,
            bidingFormRules: {
                orderSn: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' },
                ],
                billType: [
                    { required: true, message: '请选择竞价类型', trigger: 'blur' },
                ],
                endTime: [
                    { required: true, message: '请选择截止时间', trigger: 'blur' },
                ],
                linkPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    {
                        pattern: /^1[3456789]\d{9}$/,
                        message: '请输入正确的手机号',
                        trigger: 'blur',
                    },
                ],
            },
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            paginationOrderList: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            selectOrderItemPage: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            bidingForm: {
                productType: 0,
                biddingSourceType: 1,
                billType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                orderItems: [],
                suppliers: [],
                biddingNotice: `说明：
   1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
    2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，'{year}'年'{month}'月'{day}'日'{minutes}'分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`,
            },
        }
    },
    methods: {

        formatStr () {
            let year = this.bidingForm.endTime.substring(0, 4)
            let month = this.bidingForm.endTime.substring(5, 7)
            let day = this.bidingForm.endTime.substring(8, 10)
            let minutes = this.bidingForm.endTime.substring(11, 16)
            this.bidingForm.biddingNotice = `说明：
   1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
    2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
        3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
        4、发票开具均采用一票制。
        5、结算与支付方式：先货后款，卖方全部货物到场后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），甲方在收到乙方提供的符合甲方财务要求的发票和对账单后5天内完成上账，60天内以银行转账的方式支付当期货款。
        6、竞价函报送要求：请将线上完成填报的竞价函导出打印，签字盖章后以PDF彩色扫描件上传至竞价专区，标题以“公司名称+**材料竞价”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，逾期送达的视为报价无效。
        7、竞价函比价：竞价发起单位将于报价截止时间后组织比价，最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
        8、其他说明：`
        },
        formatInventoryStr () {
            let year = this.inventoryBidForm.endTime.substring(0, 4)
            let month = this.inventoryBidForm.endTime.substring(5, 7)
            let day = this.inventoryBidForm.endTime.substring(8, 10)
            let minutes = this.inventoryBidForm.endTime.substring(11, 16)
            // 固定模板
            if (this.inventoryBidForm.billType === 2) {
                this.inventoryBidForm.biddingNotice = `说明：
备注：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
2、“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求；
4、发票开具均采用一票制。
5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，电子邮件逾期送达的视为报价无效。
7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
8、报价方申明（若有必要）：`
            } else {
                this.inventoryBidForm.biddingNotice = `说明：
备注：
1、本表中各材料数量、金额为暂估数量，结算以实际供应并经双方签认的数量和金额为准。
2、网价参照《我的钢铁网》“成都市场”2023年12月20日流体管对应规格型号公布的最低价格报价（如无该规格型号报价，则选取相应报价），结算以到货当日对应规格型号网价+固定费，若到货当日无网价，按照到货当日接近日期最低网价执行；“到场含税单价”为材料到达供货地点的含税单价，包含但不限于材料费、运杂费、装车、捆扎、利润、管理费、安全费、税费等一切费用（卸车由买方负责并承担相关费用）；
3、材料执行标准：满足国家、行业相关标准文件规定以及项目施工设计文件要求
4、发票开具均采用一票制。
5、结算与支付方式：先货后款，卖方全部货物到厂后需持双方收料签字确认的对账单与买方3日内完成对账（有特殊情况需双方协商），对账完成后乙方开具有效增值税专用发票，甲方收到增值税专用发票60个工作日内（有特殊情况需双方协商）向乙方付清已开票金额。
6、竞价函报送要求：请将完成填报的竞价函PDF彩色扫描件加密后（文档密码由报价方妥善保管）发送至四川路桥建设集团股份有限公司物资分公司企业邮箱**************，邮件标题以“公司名称+电话号码”命名，报价截止时间为${year}年${month}月${day}日${minutes}分，电子邮件逾期送达的视为报价无效。
7、竞价函启封比价：竞价发起单位将于报价截止时间后组织启封比价。届时，竞价发起单位将根据邮件标题预留电话，联系报价单位询问文件密码启封竞价函，请报价方保持电话畅通；最终根据比价情况，由竞价发起单位采购领导小组选择确定供应商。
8、报价方申明（若有必要）：`
            }
        },
        selectProductDia () {
            this.yesSelectOrderItem = []
            this.selectOrderItemTable = []
            this.showSelectOrderItem = true
            this.listBidingOrderListIdsM()
        },

        removeSelectOrderItemList (row) {
            this.yesSelectOrderItem = this.yesSelectOrderItem.filter(
                t => t.orderItemId != row.orderItemId
            )
        },

        handleCurrentInventoryOrderItemClick (row) {
            for (let i = 0; i < this.yesSelectOrderItem.length; i++) {
                let t = this.yesSelectOrderItem[i]
                if (t.orderItemId == row.orderItemId) {
                    return this.$message.warning('该订单商品已选择！')
                }
            }
            this.yesSelectOrderItem.push(row)
        },

        listBiddingOrderItemByOrderIdM () {
            let params = {
                page: this.selectOrderItemPage.currentPage,
                limit: this.selectOrderItemPage.pageSize,
                productType: this.orderProductType,
            }
            if (this.orderProductType == 13) {
                params.billType = this.bidingForm.billType
            }
            if (this.selectOrderItemKeywords != null) {
                params.keywords = this.selectOrderItemKeywords
            }
            if (this.orderId != null) {
                params.orderId = this.orderId
            }
            this.biddingOrderItemSelectLoading = true
            listBidingOrderItemsList(params)
                .then(res => {
                    this.selectOrderItemPage.total = res.totalCount
                    this.selectOrderItemPage.pageSize = res.pageSize
                    this.selectOrderItemPage.currentPage = res.currPage
                    this.selectOrderItemTable = res.list
                })
                .finally(() => {
                    this.biddingOrderItemSelectLoading = false
                })
        },

        selectOrderItemAffirm () {
            if (
                this.yesSelectOrderItem == null ||
                this.yesSelectOrderItem.length == 0
            ) {
                return this.$message.error('未选择订单商品！')
            }
            if (this.bidingFormOrderItems.length == 0) {
                this.bidingFormOrderItems = this.yesSelectOrderItem
                this.yesSelectOrderItem = []
                this.showSelectOrderItem = false
            } else {
                for (let i = 0; i < this.yesSelectOrderItem.length; i++) {
                    let t = this.yesSelectOrderItem[i]
                    let flag = false
                    for (let j = 0; j < this.bidingFormOrderItems.length; j++) {
                        let t2 = this.bidingFormOrderItems[j]
                        if (t.orderItemId == t2.orderItemId) {
                            flag = true
                        }
                    }
                    if (!flag) {
                        this.bidingFormOrderItems.push(t)
                    }
                }
                this.yesSelectOrderItem = []
                this.showSelectOrderItem = false
            }
        },
        biddingOrderListClick (row) {
            this.orderId = row.orderId
            this.listBiddingOrderItemByOrderIdM()
        },

        listBidingOrderListIdsM () {
            let params = {
                page: this.paginationOrderList.currentPage,
                limit: this.paginationOrderList.pageSize,
                productType: this.orderProductType,
            }
            if (this.orderSn != null) {
                params.orderSn = this.orderSn
            }
            if (this.orderProductType == 13) {
                params.billType = this.bidingForm.billType
            }
            this.biddingOrderListLoading = true
            listBidingOrderListIds(params)
                .then(res => {
                    this.paginationOrderList.total = res.totalCount
                    this.paginationOrderList.pageSize = res.pageSize
                    this.paginationOrderList.currentPage = res.currPage
                    this.bidingOrderList = res.list
                })
                .finally(() => {
                    this.biddingOrderListLoading = false
                })
        },

        openDialog () {
            this.showOrderBidingVisible = true
        },

        getSupplierList (data) {
            this.bidingForm.suppliers = data
        },
        showSupplierDialog () {
            this.$refs.supplierModelRef.openModel()
        },
        reset () {
            this.showOrderBidingVisible = false
            this.bidingForm = {
                productType: 0,
                biddingSourceType: 1,
                title: null,
                type: 1,
                endTime: null,
                linkName: null,
                linkPhone: null,
                biddingExplain: null,
                orderItems: [],
                suppliers: [],
            }
            this.selectedSupplierList = []
            this.bidingFormOrderItems = []
            this.orderProductType = 0
        },
        createBidingM () {
            this.$refs.bidingFormRef.validate(valid => {
                if (valid) {
                    if (this.bidingFormOrderItems.length == 0) {
                        return this.$message.error('未选择订单商品！')
                    }
                    this.bidingForm.orderItems = this.bidingFormOrderItems
                    // 邀请竞价必须选择供应商
                    if (this.bidingForm.type === 2) {
                        if (
                            this.bidingForm.suppliers == null ||
                            this.bidingForm.suppliers.length == 0
                        ) {
                            return this.$message.error('请选择供应商！')
                        }
                    }
                    // maxPrice 为0或者为空提示确认
                    let arr = this.bidingForm.orderItems.filter(item => {
                        return item.maxPrice == '' || Number(item.maxPrice) == 0
                    })
                    console.log(arr)
                    if (arr.length > 0) {
                        this.$confirm(
                            '商品：【' +
                            arr[0].productName +
                            '】未设置最高单价，将不会进行限价，是否确认提交？',
                            '提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        )
                            .then(() => {
                                this.clientPop('info', '您确定要生成竞价吗！', async () => {
                                    this.bidingFormLoading = true
                                    createBidingByOrder(this.bidingForm)
                                        .then(res => {
                                            if (res.code != null && res.code == 200) {
                                                this.$message.success('生成成功')
                                                this.reset ()
                                                this.$emit('getList')
                                            }
                                        })
                                        .finally(() => {
                                            this.bidingFormLoading = false
                                        })
                                })
                            })
                            .catch(() => {
                                this.$message({
                                    type: 'info',
                                    message: '已取消提交',
                                })
                            })
                    } else {
                        this.clientPop('info', '您确定要生成竞价吗！', async () => {
                            this.bidingFormLoading = true
                            createBidingByOrder(this.bidingForm)
                                .then(res => {
                                    if (res.code != null && res.code == 200) {
                                        this.$message.success('生成成功')
                                        this.reset ()
                                        this.$emit('getList')
                                    }
                                })
                                .finally(() => {
                                    this.bidingFormLoading = false
                                })
                        })
                    }
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error',
                    })
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
@import "../bidingList/index.scss";

.btn10 {
    margin: 2px;
    padding: 4px 12px !important;
    height: auto !important;
    line-height: normal !important;
}
</style>