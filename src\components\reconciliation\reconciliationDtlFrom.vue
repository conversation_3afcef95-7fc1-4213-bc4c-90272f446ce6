<template>
  <div  class="con">
    <div class="tabs-title" id="reconciliationDtl">对账单明细</div>
    <div class="e-table"  style="background-color: #fff" >
      <el-table
          ref="tableRef"
          border
          style="width: 100%"
          :data="tableData"
          :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
          :row-style="{ fontSize: '14px', height: '48px' }"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column prop="receivingDate" label="收料日期" width="130">
          <template v-slot="scope">
            {{ scope.row.receivingDate | dateStr }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="left"  v-if="reconciliationForm.reconciliationProductType == 12 &&[0, 4].includes(reconciliationForm.state) && reconciliationForm.createType != 3">
          <template slot-scope="scope">
            <span class="pointer" style="color: rgba(33, 110, 198, 1); margin-left: 20px" @click="dismantleM(scope.row)"  v-if="reconciliationForm.reconciliationProductType == 12">拆单</span>
            <span v-if="scope.row.reconciliationDtlId == null" class="pointer" style="color: rgb(176,5,5); margin-left: 20px" @click="deleteM(scope.row)">删除</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderSn" label="订单号" width="220"/>
        <!--                                  零星采购计划pcwp的材质是物资的商品名称  -->
        <el-table-column v-if="reconciliationForm.reconciliationProductType===10" prop="texture" label="商品名称" width="200"/>
        <el-table-column prop="materialName" label="物资名称" width="200"/>
        <el-table-column prop="spec" label="规格型号" width=""/>
        <el-table-column prop="unit" label="单位" width=""/>
        <el-table-column v-if="reconciliationForm.reconciliationProductType===12" prop="texture" label="材质" width=""/>
        <el-table-column prop="maxQuantity" label="可选数量" width="70"/>
        <el-table-column prop="quantity" label="已选数量" width="100" >
          <template v-slot="scope">
            <el-input
                :disabled="reconciliationForm.createType == 3 || readonly || (type === 2)"
                v-if="[0, 1, 4].includes(reconciliationForm.state) && !readonly && !(type === 2)"
                type="number"
                v-model="scope.row.quantity"
                @change="getChangedRow(scope.row)">
            </el-input>
            <span v-else>{{ scope.row.quantity }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="freightPrice" label="到货网价" width="100" v-if="type===1">
          <template v-slot="scope">
            <el-input
                :disabled="reconciliationForm.createType == 3"
                v-if="[0, 1, 4].includes(reconciliationForm.state)"
                type="number"
                v-model="scope.row.freightPrice"
                @change="priceChange(scope.row)"
            >
            </el-input>
            <span v-else>{{ scope.row.freightPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fixationPrice" label="固定费用" width="100" v-if="type===1">
          <template v-slot="scope">
            <el-input
                :disabled="reconciliationForm.createType == 3"
                v-if="[0, 1, 4].includes(reconciliationForm.state)"
                type="number"
                v-model="scope.row.fixationPrice"
                @change="priceChange(scope.row)"
            >
            </el-input>
            <span v-else>{{ scope.row.fixationPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="含税单价" width="100" >
          <template v-slot="scope">
            <el-input
                :disabled="reconciliationForm.createType == 3 || (readonly && type === 2) || (type === 2)"
                v-if="[0, 1, 4].includes(reconciliationForm.state) && !(readonly && type === 2) && !(type === 2)"
                type="number"
                v-model="scope.row.price"
                @change="priceChange(scope.row)">
            </el-input>
            <span v-else>{{ (Number(scope.row.price) || 0).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="noRatePrice" label="不含税单价" width=""/>
        <el-table-column prop="acceptanceAmount" label="含税金额" width=""/>
        <el-table-column prop="acceptanceNoRateAmount" label="不含税金额" width=""/>
        <el-table-column prop="taxAmount" label="税额" width="120px"/>
        <el-table-column prop="settledAmount" label="已结算金额" width=""/>
        <el-table-column prop="remarks" label="备注" width="" >
          <template v-slot="scope">
            <el-input
                :disabled="reconciliationForm.createType == 3"
                v-if="[0, 1, 4].includes(reconciliationForm.state)"
                v-model="scope.row.remarks"
                @change="getChangedRow(scope.row)">
            </el-input>
            <span v-else>{{scope.row.remarks}}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { calculateNotTarRateAmount, getUuid, toFixed } from '@/utils/common'
import {
    capitalsAndNum,
    capitalsItemNum,
    reconciliationFloatCountAmount
} from '@/utils/material_reconciliationUtils/compute'

export default {
    name: 'reconciliationDtlFrom',
    props: {
        tableData: [],
        reconciliationForm: {},
        type: null,
        readonly: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {

        }
    },
    methods: {
        priceChange (row) {
        // 一旦变化则是1
            row.updateType = 1
            // 处理固定费用
            this.disposeFixationPriceM(row)
            // 处理到店网价
            this.disposeFreightPriceM(row)
            // 计算金额
            // this.countAmountM()
            let params = reconciliationFloatCountAmount(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = params.tableData
            this.reconciliationForm.taxAmount = params.taxAmount
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount
        },
        disposeFixationPriceM (row) {
            if (row.fixationPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.fixationPrice = this.fixed2(0)
            } else {
                row.fixationPrice = this.fixed2(row.fixationPrice)
            }
        },
        // 处理到货网价
        disposeFreightPriceM (row) {
            if (row.freightPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.freightPrice = this.fixed2(0)
            } else {
                row.freightPrice = this.fixed2(row.freightPrice)
            }
        },
        deleteM (row) {
            this.tableData = this.tableData.filter(t => {
                if (t.uuid != row.uuid) {
                    return true
                } else {
                    return false
                }
            })
        },
        getChangedRow (row) {
            this.disposeQuantityM(row)
            // 计算金额
            let params = reconciliationFloatCountAmount(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = params.tableData
            this.reconciliationForm.taxAmount = params.taxAmount
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount
        },
        // 处理数量
        disposeQuantityM (row) {
            if(row.quantity <= 0) {
                return row.quantity = this.fixed4(0)
            }
            // 计算最大值
            let maxNum = this.fixed4(row.maxQuantity)
            let countNum = 0
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.materialName === row.materialName && t.orderSn === row.orderSn && t.spec === row.spec && t.unit == row.unit && t.uuid !== row.uuid && row.groupUuid === t.groupUuid ) {
                    countNum++
                    if (maxNum <= 0) {
                        maxNum = 0
                        continue
                    }
                    maxNum = maxNum - t.quantity
                }
            }
            // 如果一次没添加，则表示操作的第一个
            if(countNum == 0) {
                maxNum = row.maxQuantity
            }
            if (row.quantity >= maxNum) {
                row.quantity = this.fixed4(maxNum)
            } else {
                row.quantity = this.fixed4(row.quantity)
            }
        },
        countAmountM () {
            let taxAmount = 0
            let reconciliationAmount = 0
            let reconciliationNoRateAmount = 0
            // 最终计算
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                t.acceptanceAmount  = this.fixed2(Number(t.quantity) * Number(t.price))
                t.noRatePrice =  calculateNotTarRateAmount(t.price, this.reconciliationForm.taxRate)
                t.acceptanceNoRateAmount = capitalsItemNum(calculateNotTarRateAmount(t.acceptanceAmount, this.reconciliationForm.taxRate), t.noRatePrice, t.quantity)
                reconciliationAmount = this.fixed2(Number(reconciliationAmount) + Number(t.acceptanceAmount))
                reconciliationNoRateAmount = this.fixed2(Number(reconciliationNoRateAmount) + Number(t.acceptanceNoRateAmount))
            }
            this.reconciliationForm.reconciliationAmount = reconciliationAmount
            this.reconciliationForm.taxAmount = taxAmount
            // this.reconciliationForm.reconciliationNoRateAmount = reconciliationNoRateAmount
            this.reconciliationForm.reconciliationNoRateAmount = capitalsAndNum(reconciliationAmount, reconciliationNoRateAmount, this.reconciliationForm.taxRate)
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        dismantleM (row) {
        // 插入到当前点击的下一个节点
            let insertIndex = this.tableData.length
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if(t.uuid == row.uuid) {
                    insertIndex = i + 1
                }
            }
            let newRow = {
                ...row,
                reconciliationDtlId: null,
                acceptanceAmount: this.fixed2(0),
                quantity: 0,
                uuid: getUuid()
            }
            this.tableData.splice(insertIndex, 0, newRow)
        },
    }
}
</script>
