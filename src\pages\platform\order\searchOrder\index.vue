<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按订单提交时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="3">按发货时间排序</el-radio>
                        <el-input style="width: 300px" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch"/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table
                    class="table" :height="rightTableHeight" v-loading="tableLoading" :data="tableData" border
                    @selection-change="selectionChangeHandle"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="订单号" width="240" prop="orderSn">
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.orderSn }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="卖方" width="200" prop="shopName"></el-table-column>
                    <el-table-column label="商品名称" width="200" prop="untitled"></el-table-column>
                    <!--                    <el-table-column label="状态" width="" prop="state">-->
                    <!--                        <template slot-scope="scope">-->
                    <!--                            <el-tag v-if="showTableState(scope.row)">{{ tableStateTitle }}</el-tag>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column label="状态" width="" prop="state">
                        <template slot-scope="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <span v-if="scope.row.state == 1">已提交</span>
                            <span v-if="scope.row.state == 2">待确认</span>
                            <span v-if="scope.row.state == 3">已确认</span>
                            <span v-if="scope.row.state == 4">待签订合</span>
                            <span v-if="scope.row.state == 5">已签合同</span>
                            <span v-if="scope.row.state == 6">待发货</span>
                            <span v-if="scope.row.state == 7">已关闭</span>
                            <span v-if="scope.row.state == 8">发货中</span>
                            <span v-if="scope.row.state == 9">待收货</span>
                            <span v-if="scope.row.state == 10">已完成</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单类型" width="100" prop="orderClass">
                        <template slot-scope="scope">
                            <span v-if="scope.row.orderClass == 1">普通订单</span>
                            <span v-if="scope.row.orderClass == 2">多供方订单</span>
                            <span v-if="scope.row.orderClass == 3">已拆分订单</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品类型" width="100" prop="productType">
                        <template slot-scope="scope">
                            <span v-if="scope.row.productType == 0">低值易耗品</span>
                            <span v-if="scope.row.productType == 1">主要材料</span>
                            <span v-if="scope.row.productType == 2">周转材料</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="价格模式" width="100" prop="productType">
                        <template slot-scope="scope">
                            <!-- TODO 这里需要确认，暂时以是否有网价区分是否是参考价 -->
                            <span >{{scope.row.billType==1? '参考价': '一口价'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="收件人" width="" prop="receiverName"></el-table-column>
                    <el-table-column label="收件人手机号" width="150" prop="receiverMobile"/>
                    <el-table-column label="收件地址" width="200" prop="receiverAddress"/>
                    <el-table-column label="总金额（含税）" width="120" prop="actualAmount"/>
                    <!--                    <el-table-column label="总金额（不含税）" width="120" prop="noRateAmount" />-->
                    <!--                    <el-table-column label="税率（%）" width="120" prop="taxRate" />-->
                    <!--                    <el-table-column label="物流单号" width="200" prop="deliveryFlowId" >-->
                    <!--                    </el-table-column>-->
                    <!--                    <el-table-column label="物流公司" width="200" prop="logisticsCompany"/>-->
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                    <el-table-column label="买方" width="400" prop="enterpriseName"/>
<!--                    <el-table-column label="订单提交时间" width="160" prop="flishTime"/>-->
                    <el-table-column label="发货时间" width="160" prop="deliveryTime"/>
                    <el-table-column label="完成时间" width="160" prop="successDate"/>
                    <el-table-column label="订单备注" width="300" prop="orderRemark"></el-table-column>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单类型：">
                            <el-select v-model="filterData.orderClass" placeholder="请选择订单类型">
                                <el-option
                                    v-for="item in filterData.orderClasss"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品类型：">
                            <el-select v-model="filterData.productType" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.productTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单状态：">
                            <el-select v-model="filterData.selectSateValue" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="价格模式：">
                            <el-select v-model="filterData.priceType" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.priceTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单号：">
                            <el-input clearable maxlength="100" placeholder="请输入订单号" v-model="filterData.orderSn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称：">
                            <el-input clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.untitled"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="卖方：">
                            <el-input clearable maxlength="100" placeholder="请输入卖方" v-model="filterData.shopName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="买方：">
                            <el-input clearable maxlength="100" placeholder="请输入买方" v-model="filterData.enterpriseName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row>-->
<!--                    <el-col :span="12">-->
<!--                        <el-form-item label="订单提交时间：">-->
<!--                            <el-date-picker-->
<!--                                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                                v-model="filterData.okDate"-->
<!--                                type="datetimerange"-->
<!--                                range-separator="至"-->
<!--                                start-placeholder="开始日期"-->
<!--                                end-placeholder="结束日期"-->
<!--                            >-->
<!--                            </el-date-picker>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                </el-row>-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="发货时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.deliverGoodsDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="完成时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.successDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="金额（含税）：">
                            <el-input type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 190px"/>
                            <span style="width: 20px;text-align: center;display: inline-block;">~</span>
                            <el-input type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 190px"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce } from '@/utils/common'
import { mapState } from 'vuex'
import { orderList } from '@/api/platform/order/orders'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            tableLoading: false,
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            stateOptionTitle: '', // 选中的状态标题
            // 表格数据
            tableStateTitle: null, // 表格的状态
            dataListSelections: [], //选中的数据
            className: null,
            classId: null, // 分类id
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                untitled: null,
                orderSn: null,
                shopName: null,
                enterpriseName: null,
                belowPrice: null,
                abovePrice: null,
                priceType: null,
                dateValue: [], // 开始时间和结束时间
                okDate: [], // 订单提交时间
                deliverGoodsDate: [], // 发货时间
                successDate: [],
                selectStateTitle: null, // 选中的标题
                selectSateValue: null,  // 选中的值
                productType: null,
                orderClass: null,
                orderClasss: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 1,
                        label: '普通订单'
                    }
                    , {
                        value: 2,
                        label: '多供方订单'
                    }
                ],
                stateOptions: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 0,
                        label: '草稿'
                    }
                    , {
                        value: 6,
                        label: '待发货'
                    }
                    , {
                        value: 7,
                        label: '已关闭'
                    }, {
                        value: 8,
                        label: '已发货'
                    }, {
                        value: 9,
                        label: '待收货'
                    }, {
                        value: 10,
                        label: '已完成'
                    }],
                priceTypeOptions: [
                    {
                        value: null,
                        label: '全部'
                    },
                    {
                        value: 1,
                        label: '参考价'
                    },
                    {
                        value: 2,
                        label: '一口价'
                    }
                ],
                productTypeSelect: [
                    {
                        value: null,
                        label: '全部'
                    },
                    {
                        value: 0,
                        label: '低值易耗品'
                    },
                    {
                        value: 1,
                        label: '主要材料'
                    },
                    {
                        value: 2,
                        label: '周转材料'
                    },
                ],
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        // 显示状态
        showTableState (row) {
            let stateValue = row.state
            if (stateValue === 7) {
                return false
            }
            for (let i = 0; i < this.stateOptions.length; i++) {
                if (stateValue === this.stateOptions[i].value) {
                    this.tableStateTitle = this.stateOptions[i].label
                    return true
                }
            }
        },
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.selectOptionValue = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            if (row.orderClass == 2) {
                this.$router.push({
                    path: '/platform/order/towOrderDetail',
                    name: 'towOrderDetail',
                    query: {
                        orderSn: row.orderSn
                    }
                })
            }
            if (row.orderClass == 1) {
                this.$router.push({
                    //path后面跟跳转的路由地址
                    path: '/platform/order/orderDetail',
                    //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                    name: 'orderDetail',
                    query: {
                        orderSn: row.orderSn
                    }
                })
            }
        },
        resetSearchConditions () {
            this.filterData.belowPrice = ''
            this.filterData.abovePrice = ''
            this.filterData.untitled = ''
            this.filterData.orderSn = ''
            this.filterData.shopName = ''
            this.filterData.dateValue = []
            this.filterData.okDate = []
            this.filterData.deliverGoodsDate = []
            this.filterData.successDate = []
            this.filterData.selectSateValue = null // 选中的值
            this.filterData.productType = null
            this.filterData.orderClass = null
            this.filterData.enterpriseName = ''
            this.selectOptionValue = null
            this.stateOptionTitle = ''
            this.filterData.priceType = null
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 高级搜索状态选中
        stateOptionsClick (value) {
            this.filterData.selectSateValue = value
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 获取表格数据
        getTableData () {
            let params = {
                classId: this.classId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.orderClass != null) {
                params.orderClass = this.filterData.orderClass
            }
            if (this.selectOptionValue != null) {
                params.state = this.selectOptionValue
            }
            if (this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if (this.filterData.productType != null) {
                params.productType = this.filterData.productType
            }
            if (this.filterData.selectSateValue != null) {
                params.state = this.filterData.selectSateValue
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.filterData.okDate != null) {
                params.okStartDate = this.filterData.okDate[0]
                params.okEndDate = this.filterData.okDate[1]
            }
            if (this.filterData.deliverGoodsDate != null) {
                params.deliverGoodsStartDate = this.filterData.deliverGoodsDate[0]
                params.deliverGoodsEndDate = this.filterData.deliverGoodsDate[1]
            }
            if (this.filterData.successDate != null) {
                params.startSuccessDate = this.filterData.successDate[0]
                params.endSuccessDate = this.filterData.successDate[1]
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.untitled != null) {
                params.untitled = this.filterData.untitled
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.priceType != null) {
                params.priceType = this.filterData.priceType
            }
            orderList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            if (res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            } else {
                this.$message({
                    message: res.message,
                    type: 'error'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}
</style>
