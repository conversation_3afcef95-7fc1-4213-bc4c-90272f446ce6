<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top dfa" style="justify-content: flex-end">
                    <div class="search_box">
                        <el-radio v-model="orderBy" :label="1">按开始时间</el-radio>
                        <el-radio v-model="orderBy" :label="2">按截止时间</el-radio>
                        <el-input
                            style="width: 300px"
                            v-model="keywords"
                            clearable
                            placeholder="请输入关键字搜索"
                            @blur="handleInputSearch"
                            @keyup.enter.native="handleInputSearch"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>

            </div>
            <!-- 高级搜索弹出框开始 -->
            <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%" >
                <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                    <el-row>
                        <el-col :span="22">
                            <el-form-item label="标题：">
                                <el-input type="text" clearable placeholder="请输入标题搜索" v-model="filterData.title"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="22">
                            <el-form-item label="竞价类型：">
                                <el-select style="width: 100%;" v-model="filterData.type" placeholder="请选择竞价类型">
                                    <el-option
                                        v-for="item in filterData.typeSelect"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="22">
                            <el-form-item label="竞价状态：">
                                <el-select style="width: 100%;" v-model="filterData.biddingState" clearable placeholder="请选择竞价状态">
                                    <el-option v-for="item in biddingState" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="22">
                            <el-form-item label="商品类型：">
                                <el-select style="width: 100%;" v-model="filterData.productType" clearable placeholder="请选择竞商品类型">
                                    <el-option v-for="item in productType" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="22">
                            <el-form-item label="选择开始时间：">
                                <el-date-picker style="width: 100%;"
                                    v-model="filterData.timeRange"
                                    type="datetimerange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="截止日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <span slot="footer">
                    <el-button type="primary" @click="confirmSearch">查询</el-button>
                    <el-button @click="resetSearchConditions">清空</el-button>
                    <el-button @click="queryVisible = false">取消</el-button>
                </span>
            </el-dialog>
            <!-- 高级搜索弹出框 -->
            <!--表格开始-->
            <div class="e-table">
                <el-table
                    @row-click="clickTableRow"
                    class="table"
                    border
                    :height="rightTableHeight"
                    :data="tableData"
                    highlight-current-row
                    v-loading="tableLoading"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40"/>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="竞价标题" width="260" prop="title">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.title }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="竞价编号" width="300" prop="biddingSn">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.biddingSn }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column  label="竞价类型" width="100" prop="type">
                        <template v-slot="scope" >
                            <span v-if="scope.row.type == 1"><el-tag type="success">公开竞价</el-tag></span>
                            <span v-if="scope.row.type == 2" > <el-tag >邀请竞价</el-tag></span>
                        </template>
                    </el-table-column>
                    <el-table-column  label="商品类型" width="100" prop="productType">
                        <template v-slot="scope" >
                            <span v-if="scope.row.productType === 0"><el-tag type="success">低值易耗品</el-tag></span>
                            <span v-if="scope.row.productType === 1"> <el-tag >大宗临购订单</el-tag></span>
                            <span v-if="scope.row.productType === 2"> <el-tag >大宗临购清单</el-tag></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="竞价状态" width="100" prop="bidState">
                        <template v-slot="scope" >
                            <el-tag v-if="!scope.row.bidState" type="warning">
                                待提交
                            </el-tag>
                            <el-tag v-else-if="scope.row.bidState === 0" type="warning">待提交</el-tag>
                            <el-tag v-else-if="scope.row.bidState === 1" >已提交</el-tag>
                            <el-tag v-else-if="scope.row.bidState === 5" type="info">中标待审核</el-tag>
                            <el-tag v-else-if="scope.row.bidState === 6" type="success">已中标</el-tag>
                            <el-tag  v-else-if="scope.row.bidState === 7" type="danger">未中标</el-tag>
<!--                            <el-tag v->-->
<!--                                &lt;!&ndash;状态(0默认1已提交5中标待审核6中标审核通过7审核失败)&ndash;&gt;-->
<!--                                {{-->
<!--                                    ['待提交', '已提交', '', '', '', '中标待审核', '中标审核通过', '审核失败'][scope.row.bidState]-->
<!--                                }}-->
<!--                            </el-tag>-->
                        </template>
                    </el-table-column>
                    <el-table-column label="竞价发起单位" width="300" prop="createOrgName"/>
                    <el-table-column label="开始时间" width="230" prop="startTime"/>
                    <el-table-column label="结束时间" width="" prop="endTime"/>
                </el-table>
                <!--分页-->
                <Pagination
                    v-show="tableData || tableData.length !== 0"
                    :total="pages.totalCount"
                    :limit="50"
                    :pageSize.sync="pages.pageSize"
                    :currentPage.sync="pages.currPage"
                    @currentChange="currentChange"
                    @sizeChange="sizeChange"
                />

            </div>
        </div>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination.vue'
import { debounce } from '@/utils/common'
import { getMyBidList } from '@/api/supplierSys/bidManage/myBidding/myBidManage'
import { mapState } from 'vuex'

export default {
    components: {
        Pagination
    },
    data () {
        return {
            pages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 20,
            },
            tableLoading: false,
            keywords: null,
            queryVisible: false, // 是否显示高级搜索：true 显示，false不显示
            tableData: [],
            filterData: {
                title: null,
                timeRange: [],
                biddingType: null,
                productType: null,
                biddingState: null,
                publicState: null,
                type: null,
                typeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '公开竞价' },
                    { value: 2, label: '邀请竞价' },
                ],
            },
            orderBy: 1,
            screenHeight: 0,
            // 竞价类型
            biddingType: [
                { value: 1, label: '公开竞价' },
                { value: 2, label: '邀请竞价' },
            ],
            // 商品类型
            productType: [
                { value: null, label: '全部' },
                { value: 0, label: '低值易耗品' },
                { value: 1, label: '大宗临购' },
            ],
            // 状态(0默认1已提交5中标待审核6中标审核通过7审核失败)
            biddingState: [
                { value: null, label: '全部' },
                { value: 0, label: '待提交' },
                { value: 1, label: '已提交' },
                { value: 5, label: '中标待审核' },
                { value: 6, label: '中标审核通过' },
                { value: 7, label: '审核失败' },
            ],
            //   publicity_state 公示类型（0未发布1已发布）
            publicState: [
                { value: 0, label: '未发布' },
                { value: 1, label: '已发布' },
            ]

        }
    },
    watch: {
        'orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    created () {
        this.getTableData()
    },
    mounted () {
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    // activated () {
    //     this.getTableData()
    // },
    // mounted () {
    //     this.getTableData()
    // },
    // updated () {
    //     this.getTableData()
    // },
    methods: {
        getTableData () {
            let params = {
                page: this.pages.currPage,
                limit: this.pages.pageSize,
            }
            if (this.keywords != null || this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.publicState != null) {
                params.publicState = this.filterData.publicState
            }
            if (this.filterData.title != null) {
                params.title = this.filterData.title
            }
            if (this.filterData.biddingType != null) {
                params.biddingType = this.filterData.biddingType
            }
            if (this.filterData.biddingState != null) {
                params.state = this.filterData.biddingState
            }
            if (this.filterData.timeRange != null) {
                params.timeRange = this.filterData.timeRange
            }
            if (this.orderBy != null) {
                params.orderBy = this.orderBy
            }
            if (this.filterData.type != null) {
                params.type = this.filterData.type
            }
            if (this.filterData.productType != null) {
                params.productType = this.filterData.productType
            }
            console.log(this.userInfo)
            let {  mallRoles, shopName } = this.userInfo
            console.log(mallRoles, shopName)
            if (shopName === '四川路桥自营店' && this.showDevFunc) {
                let lg = mallRoles.some(item=>item.name === '大宗临购管理人员')
                if (lg) params.productType = 1
                let lx = mallRoles.some(item=>item.name === '零星采购管理人员')
                if (lx) params.productType = 0
                if (lg && lx) params.productType = null
            }
            this.tableLoading = true
            getMyBidList(params).then(res => {
                this.pages.totalCount = res.totalCount
                this.pages.pageSize = res.pageSize
                this.pages.currPage = res.currPage
                this.tableData = res.list
            }).finally(() => this.tableLoading = false)
        },
        handleView ({ row }) {
            if (row.productType === 1 || row.productType === 2) {
                this.$router.push({
                    path: '/supplierSys/bidManage/myLgBiddingDetail',
                    name: 'myLgBiddingDetail',
                    query: {
                        id: row.biddingSn
                    }
                })
            }else {
                this.$router.push({
                    path: '/supplierSys/bidManage/myBiddingDetail',
                    query: {
                        id: row.biddingSn
                    }
                })
            }
        },
        clickTableRow (/* row, column, event */) {
        },
        handleSelectionChange (/* selection */) {
        },
        currentChange () {
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        getScreenInfo () {
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        },
        // 关键词搜索
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
            //     let params = {
            //         keywords: this.keywords,
            //         limit: this.pages.pageSize,
            //         page: this.pages.currPage
            //
            //     }
            //     getMyBidList(params).then(res => {
            //         if (res) {
            //             this.tableData = res.list
            //             this.pages.total = res.totalCount
            //             this.pages.pageSize = res.pageSize
            //             this.pages.currentPage = res.currPage
            //         } else {
            //             this.clientPop('warn', res.message, () => {
            //             })
            //         }
            //     })
        },
        // 高级查询
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        resetSearchConditions () {
            this.filterData = {
                biddingType: null,
                biddingState: null,
                publicState: null,
                timeRange: null
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-form-item.uploader {
    height: 200px;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ .el-dialog__body {
    margin-top: 0;
}
/deep/ #bidingDialog {
    .el-dialog__body {
        height: 680px;
        margin-top: 0px;
    }
}
/deep/ input[type='‘number’'] {
    -moz-appearance: textfield !important;
}
</style>
