<template><!-- 店铺新增自己店铺的零星采购商品 -->
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs v-model="tabsName" tab-position="left" @tab-click="onChangeTab">
                <el-tab-pane :disabled="clickTabFlag" label="物资" name="baseInfo">
                </el-tab-pane>
                <div id="tabs-content">
                    <div id="baseInfCon" class="con">
                        <div id="baseInfo" class="tabs-title">物资</div>
                        <!--新增-->
                        <div v-if="showForm" class="form">
                            <el-form
                                ref="formEdit" :model="addForm.formData" :rules="formRules"
                                class="demo-ruleForm" label-width="200px"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="物资名称：" prop="productName">
                                            <el-input
                                                disabled
                                                v-model="addForm.formData.productName"
                                                placeholder="请选择物资"
                                            ></el-input>
                                            <el-button
                                                size="mini" type="primary"
                                                @click="importDeviceSelect"
                                                v-if="viewType == 'add'"
                                            >选择
                                            </el-button>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="分类：" prop="classPathName">
                                            <el-input
                                                disabled
                                                v-model="addForm.formData.classPathName"
                                            ></el-input>
                                            <!--                      <category-cascader-->
                                            <!--                          disabled-->
                                            <!--                          customStyle="width: 100%"-->
                                            <!--                          style="width: 100%"-->
                                            <!--                          :catelogPath="addForm.formData.classPath"-->
                                            <!--                          :classId.sync='addForm.formData.classId'-->
                                            <!--                          :classPath.sync="addForm.formData.classPath" :productType="0"-->
                                            <!--                          @change="resetRelevanceAndBrand" :is-lc="2"-->
                                            <!--                      ></category-cascader>-->
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="品牌：" prop="brandName">
                                            <el-input
                                                v-model="addForm.formData.brandName" disabled placeholder="请选择品牌"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="库房：" prop=" ">
                                            <el-radio v-model="addForm.formData.warehouseId" disabled :label="1">
                                                库房一
                                            </el-radio>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="规格：" prop="skuName">
                                            <el-input disabled
                                                      v-model="addForm.formData.skuName" clearable
                                                      placeholder="请输入规格"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="供货价" prop="costPrice">
                                            <el-input disabled
                                                      v-model="addForm.formData.costPrice" clearable
                                                      oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                      placeholder="请输入成本价"
                                                      type="number"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="库存：" prop="stock">
                                            <el-input :disabled="IsInventory"
                                                      v-model="addForm.formData.stock" clearable
                                                      oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                      placeholder="请输入库存"
                                                      type="number"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="计量单位" prop="unit">
                                            <el-select :disabled="IsUnitCount"
                                                       v-model="addForm.formData.unit" filterable
                                                       placeholder="请选择计量单位"
                                                       @change="numUnitChange"
                                            >
                                                <el-option
                                                    v-for="item in addForm.numUnitOptions" :key="item.value"
                                                    :label="item.label" :value="item.label"
                                                >
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <!-- 自营店需要采购进价字段 -->
                                    <el-col :span="12">
                                        <el-form-item label="采购进价：" prop="originalPrice">
                                            <el-input disabled
                                                      v-model="addForm.formData.originalPrice" clearable
                                                      oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                      placeholder="请输入原价"
                                                      type="number"
                                            ></el-input>
                                            <el-tooltip placement="top" effect="light">
                                                <div slot="content">
                                                    平台对外展示的价格
                                                </div>
                                                <el-icon class="el-icon-question ml10" style="color: #666;"></el-icon>
                                            </el-tooltip>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="销售价格" prop="sellPrice">
                                            <el-input disabled
                                                      v-model="addForm.formData.sellPrice" clearable
                                                      oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                      placeholder="请输入销售价格"
                                                      type="number"
                                            ></el-input>
                                            <el-tooltip placement="top" effect="light">
                                                <div slot="content">
                                                    用户登录后能看到的价格
                                                </div>
                                                <el-icon class="el-icon-question ml10" style="color: #666;"></el-icon>
                                            </el-tooltip>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="税率：" prop="taxRate">
                                            <el-input disabled v-model="addForm.formData.taxRate" style="width: 110px;"
                                                      :step="0.01" type="number" placeholder="填写税率"
                                                      @change="writeTaxRate"/>
                                            %
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            class="uploader" label="物资主图（推荐654x490）：" prop="adminFile"
                                        >
                                            <el-upload
                                                disabled
                                                ref="adminFileRef"
                                                action="fakeaction"
                                                :auto-upload="false"
                                                :show-file-list="false"
                                                :before-upload="checkFileSize"
                                                :on-change="file => setCropperImg(file, 0, [654, 490])"
                                            >
                                                <template v-if="mainImg">
                                                    <div class="cover dfc" @click.stop="()=>{}">
                                                        <i class="el-icon-zoom-in"
                                                           @click.stop="handleImgPreview('mainImg')"></i>
                                                        <i class="el-icon-delete" @click.stop="delImg('mainImg')"></i>
                                                    </div>
                                                    <img :src="mainImg" alt="">
                                                </template>
                                                <i v-else class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            class="uploader" label="物资图片（推荐654x490）：" prop="productFiles"
                                        >
                                            <el-upload
                                                class="display-only"
                                                ref="productFileRef"
                                                action="fakeaction"
                                                list-type="picture-card"
                                                :auto-upload="false"
                                                :multiple="true"
                                                :limit="uploadMax"
                                                :file-list="addForm.formData.productFiles"
                                                :before-upload="checkFileSize"
                                                :on-change="(file, fileList) => { fileList.pop(); setCropperImg(file, 2, [654, 490]) }"
                                                :on-exceed="handleExceed"
                                                :on-remove="productFileRemove"
                                                :on-preview="handlePictureCardPreview"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            class="uploader" label="物资小图 （推荐250x200）：" prop="minFile"
                                        >
                                            <el-upload
                                                disabled
                                                ref="minFileRef"
                                                :auto-upload="false"
                                                :before-upload="checkFileSize"
                                                :show-file-list="false"
                                                :on-change="(file) => setCropperImg(file, 1, [250, 200])"
                                                action="fakeaction"
                                            >
                                                <template v-if="smallImg">
                                                    <div class="cover dfc" @click.stop="()=>{}">
                                                        <i class="el-icon-zoom-in"
                                                           @click.stop="handleImgPreview('minImg')"></i>
                                                        <i class="el-icon-delete" @click.stop="delImg('minImg')"></i>
                                                    </div>
                                                    <img :src="smallImg" alt="">
                                                </template>
                                                <i v-else class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="物资描述：" prop="productDescribe">
                                            <editor :disabled="true"
                                                    @ready="onEditorReady"
                                                    v-model="addForm.formData.productDescribe"></editor>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
            <div class="buttons">
                <el-button type="success" @click="submit" v-if="viewType == 'add'">保存</el-button>
                <el-button @click="pageEdit" v-if="viewType != 'add'">编辑</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
        </div>
        <!--选择物资库-->
        <el-dialog
            v-dialogDrag :close-on-click-modal="false"
            :visible.sync="showDeviceDialog"
            style="margin-left: 20%;" title="选择物资库" width="70%"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            v-model="inventory.keyWord" clearable placeholder="输入搜索关键字" type="text"
                            @blur="getDeviceInventory"
                        >
                            <img slot="suffix" :src="require('@/assets/search.png')" @click="getDeviceInventory"/>
                        </el-input>
                    </div>
                </div>
                <el-table
                    ref="tableRef" :data="inventory.tableData" :max-height="$store.state.tableHeight" border
                    class="table"
                    v-loading="inventoryTableLoading"
                    highlight-current-row style="width: 100%" @row-click="handleCurrentInventoryClick"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="编号" prop="serialNum">
                        <template #default="scope">
                            <span class="serial-num-cell">{{ scope.row.serialNum }}</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="编号" prop="serialNum"></el-table-column>-->
                    <el-table-column label="名称" prop="productName"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="inventory.tableData && inventory.tableData.length > 0"
                    :currentPage.sync="inventory.paginationInfo.currentPage"
                    :pageSize.sync="inventory.paginationInfo.pageSize"
                    :total="inventory.paginationInfo.total" @currentChange="getDeviceInventory"
                    @sizeChange="getDeviceInventory"
                />
                <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog :visible.sync="imgPreviewDialog" title="图片预览">
            <img class="center mb20" style="display: block" :src="previewImg" alt="">
        </el-dialog>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <Cropper ref="cropperImage" @handleUploadSuccess="handleUploadSuccess"/>

    </div>

</template>

<script>
import { getCascaderOptions } from '@/api/platform/common/components'
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import editor from '@/components/quillEditor'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import Cropper from '@/components/cropper'
import { mapState } from 'vuex'
import $ from 'jquery'
import { addImgUrl, spliceImgUrl, throttle, toFixed } from '@/utils/common'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { getBrandPageList } from '@/api/shopManage/brand/brand'
import { uploadFile } from '@/api/platform/common/file'
import {
    getMaterialInfo,
} from '@/api/shopManage/product/materialManage'
import { updateMaterialAndState, updateProductState } from '@/api/platform/product/materialManage'
import { getEnterpriseInfoTaxRate } from '@/api/platform/shop/shopManager'
import { getList } from '@/api/platform/system/systemParam'
import {
    selfOriginStock,
    saveSelfStock,
    getSelfStock, deleteSelfStock
} from '@/api/supplierSys/stock/stockManage'

export default {
    data () {
        return {
            IsUnitCount: true,
            IsInventory: true,
            //编辑删除的recordId
            editRecordId: '',
            regionTableIndex: 1,
            addressData: regionData, // 地址数据
            economizeData: [], //只有省数据
            marketData: [], //省市数据
            zoneUpdateShow: false,
            zoneUpdateForm: {},
            zoneListLoading: false,
            zoneAbbr: {
                province: '',
                city: [],

            },
            zoneList: [], //去重
            cascaderOptions: {
                province: [],
                city: [],
                district: [],
            },
            imgPreviewDialog: false,
            previewImg: '',
            uploadMax: 10,
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            pages: {
                totalCount: 0,
                currPage: 1,
                pageSize: 20,
            },
            uploadType: null,
            mainImg: '', // 主图
            smallImg: '', // 小图
            dialogImageUrl: '', // 主图
            // 数据加载
            formLoading: false,
            dialogVisible: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            formRules: {
                productName: [
                    { required: true, message: '请选择物资', trigger: 'blur' },
                ],
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            // 商品库
            inventory: {
                tableData: [],
                Isloading: false,
                keyWord: null,
                classId: null,
                paginationInfo: { // 分页
                    total: 0,
                    pageSize: 20,
                    currentPage: 0,
                },
            },
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            uploadImgSize: 10, // 上传文件大小
            //表单数据
            formData: {},
            addForm: {
                formData: {
                    regionTableData: [
                        {
                            index: 1,
                            regionName: '全区域',
                            selectAddressOptionsAll: [],
                            taxInPrice: '',
                            price: '',
                            detailAddress: []
                        }
                    ],
                    zonePriceList: [],
                    zone: '',
                    zoneAddrList: [],
                    productType: 0,
                    taxRate: null,
                    productName: null,
                    productId: null,
                    productKeyword: null,
                    classId: null,
                    classPathName: null,
                    zonePath: [],
                    productMinPrice: null,
                    brandId: null,
                    brandName: null,
                    shopSort: null,
                    skuName: null,
                    settlePrice: null,
                    costPrice: null,
                    stock: 1,
                    unit: null,
                    originalPrice: null,
                    sellPrice: 0,
                    warehouseId: 1,
                    province: null,
                    city: null,
                    county: null,
                    detailedAddress: null,
                    productFiles: [],
                    minFile: [],
                    adminFile: [],
                    productDescribe: null,
                    accountPeriod: 2//二级供应商撮合模式默认账期是2
                },
                adminFileLength: 0,
                minFileLength: 0,
                // 地址
                // 计量单位
                numUnitOptions: [],
            },
            // 品牌数据
            brand: {
                classId: null,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableData: [],
            tableLoading: false,
        }
    },
    components: {
        Pagination,
        editor,
        Cropper
    },
    created () {
        this.editRecordId = '',
        this.addForm.numUnitOptions = this.materialUnit
        this.rowData = this.$route.params.row
        if (this.rowData.viewType == 'add') {

            this.getEnterpriseInfoTaxRateM()
            this.viewType = 'add'
            if (this.rowData.classPath != null) {
                this.addForm.formData.classPath = this.rowData.classPath
                this.addForm.formData.classId = this.rowData.classPath[this.rowData.classPath.length - 1]
            }
            this.showForm = true
        } else {
            this.viewType = 'view'
            this.getMaterialInfo()
        }
        this.getEconomizeAndMarketList()
        console.log(this.userInfo.isBusiness)//是否自营店铺：1：是  0：否
    },
    mounted () {
        $('#tabs-content').scrollTo(0, 0)
        this.$nextTick(() => {
            const editorElement = this.$el.querySelector('#baseInfCon .editor')
            if (editorElement) {
                const quill = editorElement.__vue__.$refs.quill.quill
                if (quill) {
                    this.disableEditorUploads(quill)
                }
            }
        })
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['materialUnit']),
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
        'addForm.formData.isZone': {
            handler (val) {
                this.addForm.formData.isZone = parseInt(this.addForm.formData.isZone)
                if (val == 2) {
                    if (this.addForm.formData.zonePriceList == null) {
                        this.addForm.formData.zonePriceList = []
                    }

                }
            }
        }
    },
    methods: {
        getFixed () {
            getList({ keywords: '固定加成率' }).then(res => {
                this.addForm.formData.markUpNum = res.list[0].keyValue2
                this.showForm = true
            })
        },
        taxInPriceChange (index) {
            let regionOption = this.addForm.formData.regionTableData.filter(item => item.index === index)[0]
            regionOption.taxInPrice = this.fixed2(regionOption.taxInPrice)
            if (this.addForm.formData.taxRate != null) {
                regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
            } else {
                this.$message.error('税率不能为空')
                regionOption.taxInPrice = ''
            }
        },
        getEconomizeAndMarketList () {
            let economizeList = []
            let marketList = []
            this.addressData.forEach((e, i) => {
                economizeList.push({ label: e.label, value: e.value })
                marketList.push({ label: e.label, value: e.value, children: [] })
                e.children.forEach(s => {
                    marketList[i].children.push({ label: s.label, value: s.value })
                })
            })
            this.economizeData = economizeList
            this.marketData = marketList
        },
        addRegion () {
            this.addForm.formData.regionTableData.push({
                index: this.regionTableIndex + 1,
                regionName: '区域' + this.regionTableIndex,
                selectAddressOptions: [],
                taxInPrice: '',
                price: '',
                detailAddress: []
            })
            this.regionTableIndex++
        },
        removeOneParamsData (row) {
            if (row.index === 1) {
                let region = this.addForm.formData.regionTableData[0]
                region.taxInPrice = ''
                region.price = ''
                region.selectAddressOptionsAll = []
                region.detailAddress = []
            } else {
                this.addForm.formData.regionTableData = this.addForm.formData.regionTableData.filter(obj => obj.index !== row.index)
            }
        },
        //获取当前企业的企业税率
        getEnterpriseInfoTaxRateM () {
            getEnterpriseInfoTaxRate().then(res => {
                this.addForm.formData.taxRate = res
            })
        },
        writeTaxRate () {
            if (this.addForm.formData.taxRate != null) {
                if (!(0 <= this.addForm.formData.taxRate && this.addForm.formData.taxRate <= 100)) {
                    this.$message.error('税率不能小于0或大于100')
                    this.addForm.formData.taxRate = 0
                } else {
                    this.addForm.formData.taxRate = this.fixed2(this.addForm.formData.taxRate)//税率保留2位小数
                    if (this.addForm.formData.regionTableData.length > 0) {
                        this.addForm.formData.regionTableData.forEach(r => {
                            r.price = this.fixed2(r.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
                        })
                    }
                }
            } else {
                this.$message.error('税率不能为空')
                this.addForm.formData.taxRate = 0
            }
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        limitZonePrice (row) {
            if (row.zonePrice <= 0 || row.zonePrice >= this.addForm.formData.originalPrice) {
                row.zonePrice = this.fixed2(0)
                this.$message.error('销售价格不能小于0或销售价格不能大于原价')
            } else {
                row.zonePrice = this.fixed2(row.zonePrice)
            }

        },
        async getSubDistrict (i) {
            let key, distCode
            if (i === 1) {
                this.zoneAbbr.city = ''
                key = 'city'
                this.cascaderOptions.province.forEach(item => {
                    if (item.districtName === this.zoneAbbr.province) distCode = item.districtCode
                })
            } else {
                key = 'district'
                this.cascaderOptions.city.forEach(item => {
                    if (item.districtName === this.zoneAbbr.city) distCode = item.districtCode
                })
            }
            let res = await getCascaderOptions({ distCode, })
            this.cascaderOptions[key] = res
        },
        handleImgPreview (name) {
            if (name === 'mainImg') {
                this.previewImg = this.mainImg
            } else if (name === 'minImg') {
                this.previewImg = this.smallImg
            }
            this.imgPreviewDialog = true
        },
        delImg (name) {
            if (name === 'mainImg') {
                this.mainImg = ''
                this.addForm.formData.adminFile.url = ''
            } else if (name === 'minImg') {
                this.smallImg = ''
                this.addForm.formData.minFile.url = ''
            }
        },
        // 裁剪完毕上传图片
        handleUploadSuccess (file) {
            if (this.uploadType === 0) {
                this.uploadAdminFile(file)
            } else if (this.uploadType === 1) {
                this.uploadMinFile(file)
            } else if (this.uploadType === 2) {
                this.uploadProductFile(file)
            }
        },
        resetRelevanceAndBrand () {
            let arr = ['relevanceName', 'brandName', 'brandId', 'relevanceId']
            arr.forEach(item => this.addForm.formData[item] = '')
        },
        handleExceed () {
            this.$message.warning('请最多上传 ' + this.uploadMax + ' 个文件。')
        },
        // 修改状态
        updateStateBatch (state, title) {
            let params = {
                productIds: [this.addForm.formData.productId],
                state: state,
                saveAndSubmit: 0
            }
            if (state === 3) {
                // 保存并上架
                this.$refs.formEdit.validate(valid => {
                    if (!valid) return this.$message.error('请检查非空输入框')
                    spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                    let newParams = {
                        productIds: [this.addForm.formData.productId],
                        state: state,
                        ...this.addForm.formData
                    }
                    newParams.state = state
                    params.saveAndSubmit = 1
                    this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                        updateMaterialAndState(newParams).then(res => {
                            this.handleClose()
                            this.message(res)
                        })
                    })
                })
            } else {
                // 下架
                this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                    updateProductState(params).then(res => {
                        this.handleClose()
                        this.message(res)
                    })
                })
            }

        },
        getSelfStockInfo (id) {
            this.formLoading = true
            getSelfStock(id).then(res => {
                this.addForm.formData = res
            }).finally(() => {
                this.formLoading = false
                this.showForm = true
            })
        },
        // 获取物资详情
        getMaterialInfo () {
            let params = {
                productId: this.rowData.productId,
            }
            this.formLoading = true
            getMaterialInfo(params).then(res => {
                this.addressFormatShow(res)
                addImgUrl(res, this.imgUrlPrefixAdd)
                this.addForm.minFileLength = res.minFile.length
                this.addForm.adminFileLength = res.adminFile.length
                this.addForm.formData = res
                this.addForm.formData.warehouseId = 1
                this.mainImg = res.adminFile.length > 0 ? res.adminFile[0].url : ''
                this.smallImg = res.minFile.length > 0 ? res.minFile[0].url : ''
                this.inventory.classId = this.addForm.formData.classId
                this.addForm.formData.regionTableData = res.regionPrice
                this.addForm.formData.regionTableData.forEach((e, i) => {
                    if (i === (this.addForm.formData.regionTableData.length - 1)) {
                        this.regionTableIndex = Number(e.regionName.replace('区域', '')) + 1
                    }
                })
                if (this.addForm.formData.markUp == null || this.addForm.formData.markUp == 1) {
                    this.addForm.formData.markUp = '1'
                    this.getFixed()
                } else {
                    this.showForm = true
                }
                console.log('addForm.formData.productFiles\n', this.addForm.formData.productFiles)
            }).finally(() => {
                this.formLoading = false
            })
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.formData.unit = value
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null) return
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.addForm.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 物资库点击
        handleCurrentInventoryClick (row) {
            let params = {
                productId: row.productId,
            }
            this.inventoryTableLoading = true
            getMaterialInfo(params).then(res => {
                this.addForm.formData = res
                this.addForm.formData.warehouseId = 1
            }).finally(() => {
                this.inventoryTableLoading = false
                this.showDeviceDialog = false
            })
            // this.addForm.formData.relevanceName = row.materialName
            // this.addForm.formData.relevanceId = row.billId
            // this.addForm.formData.relevanceNo = row.billNo
            // this.addForm.formData.skuName = row.spec
            // this.addForm.formData.unit = row.unit
            // this.addForm.formData.classId = row.classId
            // this.addForm.formData.classPath = row.classPath
            // this.addForm.formData.classNamePath = row.classNamePath
            // this.addForm.formData.className = row.className

        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                isActive: 1,
                page: this.inventory.paginationInfo.currentPage,
                limit: this.inventory.paginationInfo.pageSize,
            }
            if (this.inventory.classId != null) {
                params.classId = this.inventory.classId
            }
            if (this.inventory.keyWord != null) {
                params.keywords = this.inventory.keyWord
            }
            this.inventoryTableLoading = true
            selfOriginStock(params).then(res => {
                this.inventory.tableData = res.list
                this.inventory.paginationInfo.total = res.totalCount
            }).finally(() => {
                this.inventoryTableLoading = false
            })
        },
        // 选择物资名称
        importDeviceSelect () {
            this.showDeviceDialog = true
            this.getDeviceInventory()
        },
        // 提交
        submit () {
            this.$refs.formEdit.validate(valid => {
                if (!valid) return this.$message.error('请检查非空输入框')
                spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                if (this.viewType === 'add') {
                    this.formLoading = true
                    saveSelfStock(this.addForm.formData).then(res => {
                        if (res.code != null && res.code != 200) {
                            addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                            return this.formLoading = false
                        }
                        this.IsInventory = true
                        this.IsUnitCount = true
                        this.$router.go(-1)
                        this.message(res)
                        if (this.editRecordId) {
                            deleteSelfStock( this.editRecordId )
                        }
                    }).finally(() => {
                        this.formLoading = false
                    })
                } else {
                    this.formLoading = false
                }

            })
        },
        // 地区
        handleAddressChange (index) {
            let regionOption = this.addForm.formData.regionTableData.filter(item => item.index === index)[0]
            let addArr = regionOption.selectAddressOptions
            if (addArr != null && addArr.length > 0) {
                let addrStr = []
                addArr.forEach(e => {
                    if (e.length == 1) {
                        addrStr.push(CodeToText[e[0]])
                    } else if (e.length == 2) {
                        addrStr.push(CodeToText[e[0]] + CodeToText[e[1]])
                    }
                })
                regionOption.detailAddress = addrStr
            }
        },
        handleAddressChange1 (params) {
            if (params != null && params.length > 0) {
                let addrStr = []
                params.forEach(e => {
                    addrStr.push(CodeToText[e])
                })
                this.addForm.formData.regionTableData[0].detailAddress = addrStr
            }
        },
        // 物资主图上传
        setCropperImg (file, type, [w, h]) {
            this.uploadType = type
            this.$refs.cropperImage.handleOpen(file, { autoCropWidth: w, autoCropHeight: h }, {
                type: 'fixed',
                sizeWidth: w,
                sizeHeight: h
            })
        },
        // 物资图片上传
        productFileChange (file, fileList) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            this.uploadProductFile(file, fileList)
        },
        // 品牌单选
        handleCurrentClick (row) {
            this.addForm.formData.brandId = row.brandId
            this.addForm.formData.brandName = row.name
            this.showBrandDialog = false
        },
        // 分类点击
        classNodeClick (data) {
            this.brand.classId = data.classId
            this.getBrandTableData()
        },
        // 获取品牌表格
        getBrandTableData () {
            let params = {
                page: this.brand.paginationInfo.currentPage,
                limit: this.brand.paginationInfo.pageSize,
            }
            if (this.brand.keywords != null) {
                params.name = this.brand.keywords
            }
            if (this.brand.classId != null) {
                params.classId = this.brand.classId
            }
            this.brandTableLoading = true
            getBrandPageList(params).then(res => {
                this.brand.tableData = res.list
                this.brand.paginationInfo.total = res.totalCount
                this.brand.paginationInfo.pageSize = res.pageSize
                this.brand.paginationInfo.currentPage = res.currPage
                this.brandTableLoading = false
            }).catch(() => {
                this.brandTableLoading = false
            })
        },
        brandDialog () {
            if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            } else {
                this.brand.classId = this.addForm.formData.classId
            }
            this.showBrandDialog = true
            this.getBrandTableData()
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 上传主图
        async uploadAdminFile (file) {
            let currFile = this.addForm.formData.adminFile[0]
            if (currFile) await createFileRecordDelete({ recordId: currFile.fileFarId + '' }) // 删除主图
            let form = this.uploadFileInfo(file)
            this.addForm.adminFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 1
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 0
                this.mainImg = file.url
                this.addForm.formData.adminFile = [file]
                this.$message({ message: '上传成功', type: 'success' })
            }).finally(() => {
                this.addForm.adminFileLength = 0
                this.formLoading = false
            })
        },
        // 上传物资图片
        uploadProductFile (file) {
            let form = this.uploadFileInfo(file)
            this.formLoading = true
            uploadFile(form).then(res => {
                let productFile = {}
                productFile.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                productFile.name = res[0].objectName
                productFile.isMain = 0
                productFile.relevanceType = 1
                productFile.fileType = 1
                productFile.fileFarId = res[0].recordId
                productFile.imgType = 0
                this.addForm.formData.productFiles.push(productFile)
                this.$message({ message: '上传成功', type: 'success' })
            }).finally(() => {
                this.formLoading = false
            })
        },
        // 上传小图
        async uploadMinFile (file) {
            let currFile = this.addForm.formData.minFile[0]
            if (currFile) await createFileRecordDelete({ recordId: currFile.fileFarId + '' }) // 删除小图
            let form = this.uploadFileInfo(file)
            this.addForm.minFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 1
                this.smallImg = file.url
                this.addForm.formData.minFile = [file]
                this.$message({ message: '上传成功', type: 'success' })
            }).finally(() => {
                this.addForm.minFileLength = 0
                this.formLoading = false
            })
        },
        // 判断上传的图片大小
        checkFileSize (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },

        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // 物资图片删除
        productFileRemove (file) {
            let files = this.addForm.formData.productFiles
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.productFiles = newFiles
            })

        },
        //取消
        handleClose () {
            this.$router.replace('/supplierSys/analysis/inventoryManagement')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                productType: 0,
                relevanceName: null,
                productName: null,
                productKeyword: null,
                classId: null,
                classPath: [],
                productMinPrice: null,
                brandId: null,
                brandName: null,
                shopSort: null,
                skuName: null,
                settlePrice: null,
                costPrice: null,
                stock: 1,
                unit: null,
                originalPrice: null,
                sellPrice: null,
                province: null,
                city: null,
                county: null,
                detailedAddress: null,
                productFiles: [],
                adminFile: [],
                minFile: [],
                productDescribe: null,
            }
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        currentChange () {
        },
        sizeChange () {

        },
        handleCurrentChange () {

        },
        handleSelectionChange () {

        },
        onEditorReady (quill) {
            if (this.disabled === true) {
                quill.enable(false)
            }
            this.disableEditorUploads(quill)
        },
        disableEditorUploads (quill) {
            const toolbar = quill.getModule('toolbar')
            if (toolbar && toolbar.handlers && toolbar.handlers.image) {
                toolbar.handlers.image = () => {
                    this.$message.warning('图片上传功能已禁用')
                }
            }
            if (toolbar && toolbar.handlers && toolbar.handlers.video) {
                toolbar.handlers.video = () => {
                    this.$message.warning('视频上传功能已禁用')
                }
            }
        },
        //页面编辑
        pageEdit () {
            this.IsInventory = false
            this.IsUnitCount = false
            this.viewType = 'add'
            this.editRecordId = this.rowData.recordId
        }
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.e-table {
    min-height: auto;
    background: #fff;
}

#tabs-content {
    padding-bottom: 70px !important;
}

/deep/ .el-dialog .el-dialog__body {
    height: unset !important;
    margin: 0 20px;
}

/deep/ .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 148px;
    height: 148px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    .el-icon-delete {
        margin-left: 10px;
        line-height: unset;
        color: #e9513e;
    }

    .cover {
        width: 100%;
        height: 100%;
        position: relative;
        left: 0;
        top: 0;
        background-color: rgba(0, 0, 0, 0.7);
        display: none;
    }

    &:hover {
        border-color: #409EFF;

        .cover {
            display: block;
        }
    }

    img, & > i {
        width: 148px;
        height: 148px;
    }

    i {
        color: #8c939d;
        line-height: 148px;
        text-align: center;
    }

    img {
        object-fit: cover;
        display: block;
    }
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
    display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 300px;
        margin-top: 0;
    }
}

//隐藏富文本编辑器的文件时上传和视频上传按钮
//#baseInfCon {
//    //图片上传按钮
//    .editor ::v-deep .ql-image {
//        display: none !important;
//    }
//    //视频上传按钮
//    .editor ::v-deep .ql-video {
//        display: none !important;
//    }
//}
#baseInfCon {
    .editor ::v-deep {
        .ql-image,
        .ql-video {
            cursor: not-allowed;
            opacity: 0.5;
            pointer-events: none;

            &:hover {
                background-color: #eee;
            }
        }
    }
}
.serial-num-cell {
    color: #409EFF;
    cursor: pointer;
    &:hover {
        text-decoration: underline;
        font-weight: bold;
    }
}
/deep/ .display-only .el-upload {
    display: none;
}
</style>
