/** v-permission="\'order:add\'" v-if="hasPermission('order:add')"
 * 是否有权限 自定义指令
 */
import store from '@/store'
function getPermission (value) {
    const userInfo = store.state.userInfo
    let permissionArr = userInfo.permissions || []
    if (typeof value === 'string') {// 字符串
        return permissionArr.includes(value)
    } else if (Object.prototype.toString.call(value) === '[object Object]') {// 对象
        if(value.role) {
            permissionArr = userInfo.mallRoleList || []
            if (typeof value.role === 'string') {// 字符串
                return permissionArr.includes(value.role)
            } else if (Array.isArray(value.role)) {// 数组
                return value.role.some(p => permissionArr.some(itemI=>{return itemI.name === p}))
            }
        }
        // throw new Error('hasPermission 的值必须是字符串、字符串数组、或对象，例如 v-if="hasPermission('order:add')" 、 v-if="hasPermission(['order:add'])" v-if="hasPermission({role: '管理员'})" 或 v-if="hasPermission({role: ['管理员', '超级管理员']})")
        throw new Error('v-permission 的值必须是字符串、字符串数组、或对象，例如 v-permission="\'order:add\'" 、 v-permission="[\'order:add\',\'order:edit\']" v-permission="{role: \'管理员\'} 或 v-permission="{role: [\'管理员\',\'超级管理员\']}"')
    } else if (Array.isArray(value)) {// 数组
        return value.some(p => permissionArr.includes(p))
    } else {// 错误
        // throw new Error('hasPermission 的值必须是字符串、字符串数组、或对象，例如 v-if="hasPermission('order:add')" 、 v-if="hasPermission(['order:add'])" v-if="hasPermission({role: '管理员'})" 或 v-if="hasPermission({role: ['管理员', '超级管理员']})")
        throw new Error('v-permission 的值必须是字符串、字符串数组、或对象，例如 v-permission="\'order:add\'" 、 v-permission="[\'order:add\',\'order:edit\']" v-permission="{role: \'管理员\'} 或 v-permission="{role: [\'管理员\',\'超级管理员\']}"')
    }
}
export const hasPermi =  {
    inserted (el, binding, vnode) { // eslint-disable-line
        if (!getPermission(binding.value)) {
            if(el.parentNode) {
                el.__originalParent = el.parentNode // 缓存原始父节点
                const children = Array.from(el.__originalParent.children)
                const originalIndex = children.indexOf(el)
                const nextSibling = children[originalIndex + 1] // 获取原位置的下一个兄弟节
                el.__nextSibling = nextSibling
                el.parentNode.removeChild(el)  // 临时移除元素
            }
        }
    },
    update (el, binding) {
        if (!getPermission(binding.value)) {
            if(el.parentNode) {
                el.__originalParent = el.parentNode // 缓存原始父节点
                const children = Array.from(el.__originalParent.children)
                const originalIndex = children.indexOf(el)
                const nextSibling = children[originalIndex + 1] // 获取原位置的下一个兄弟节
                el.__nextSibling = nextSibling
                el.parentNode.removeChild(el)  // 临时移除元素
            }
        } else {
            // 响应式更新处理
            if (!document.body.contains(el)) {
                if(el.__originalParent) {
                    el.__originalParent.insertBefore(el, el.__nextSibling) // 插入到原位置
                }
            }
        }
    }
}
export function hasPermiFun (value) {
    return getPermission(value)
}
export default {
    hasPermi,
    hasPermiFun
}

// export default {
//     inserted (el, binding, vnode) {
//         const store = vnode.context.$store
//         const permissions = store.state.userInfo?.roles || []
//         const value = binding.value

//         let hasPermission = false

//         if (typeof value === 'string') {
//             hasPermission = permissions.includes(value)
//         } else if (Array.isArray(value)) {
//             hasPermission = value.some(p => permissions.includes(p))
//         } else {
//             throw new Error('v-permission 的值必须是字符串或字符串数组，例如 v-permission="\'order:add\'" 或 v-permission="[\'order:add\',\'order:edit\']"')
//         }
//         // 这里可以隐藏node，或者弹窗，或者修改样式等等，可以结合binging再优化设计
//         if (!hasPermission) {
//             el.parentNode && el.parentNode.removeChild(el)
//         }
//     }
// }