<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div class="tabs warningTabs" v-loading="formLoading">
            <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="订单信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="订单商品" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="拆分订单" name="twoOrderList" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">订单信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单号：">
                                            <span>{{ formData.orderSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="卖方：">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品名称：">
                                            <span>{{ formData.untitled }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="买方：">
                                            <span>{{ formData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收件人手机号：">
                                            <span>{{ formData.receiverMobile }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收件人：">
                                            <span>{{ formData.receiverName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="配送方式：">-->
                                    <!--                                            <span>{{ formData.deliveryType }}</span>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="物流单号：">-->
                                    <!--                                            <span>{{ formData.deliveryFlowId }}</span>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                    <el-col :span="12">
                                        <el-form-item label="订单状态：">
                                            <el-tag v-if="formData.state == 0">草稿</el-tag>
                                            <el-tag v-if="formData.state == 1">已提交</el-tag>
                                            <el-tag v-if="formData.state == 2">待确认</el-tag>
                                            <el-tag v-if="formData.state == 3">已确认</el-tag>
                                            <el-tag v-if="formData.state == 4">待签订合</el-tag>
                                            <el-tag v-if="formData.state == 5">已签合同</el-tag>
                                            <el-tag v-if="formData.state == 6">待发货</el-tag>
                                            <el-tag v-if="formData.state == 7">已关闭</el-tag>
                                            <el-tag v-if="formData.state == 8">发货中</el-tag>
                                            <el-tag v-if="formData.state == 9">待收货</el-tag>
                                            <el-tag v-if="formData.state == 10">已完成</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收件地址：">
                                            <span>{{ formData.receiverAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品类型：">
                                            <el-tag v-if="formData.productType == 0">低值易耗品</el-tag>
                                            <el-tag v-if="formData.productType == 1">主要材料</el-tag>
                                            <el-tag v-if="formData.productType == 2">周转材料</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="是否开发票：">
                                            <el-tag v-if="formData.orderBillState === 0">初始</el-tag>
                                            <el-tag v-if="formData.orderBillState === 1">已申请</el-tag>
                                            <el-tag v-if="formData.orderBillState === 2" type="success">已开票</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="总金额（含税）：">
                                            <span>{{ formData.actualAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="账期：">
                                            <!-- TODO 撮合模式默认账期两个月，撮合模式现在没实现 -->
                                            <span>{{ formData.paymentPeriod }}月</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!-- <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="支付类型：">
                                            <el-tag v-if="formData.payWay === 1">线上支付</el-tag>
                                            <el-tag v-if="formData.payWay === 2">内部结算</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row> -->
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单提交时间：">
                                            <span>{{ formData.flishTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" v-if="formData.productType!=0">
                                        <el-form-item label="价格模式：">
                                            <span>{{ formData.billType==1? '参考价': '一口价' }}</span>
                                        </el-form-item>
                                    </el-col>
<!--                                    <el-col :span="12">-->
<!--                                        <el-form-item label="税率：">-->
<!--                                            <span>{{ formData.taxRate }}%</span>-->
<!--                                        </el-form-item>-->
<!--                                    </el-col>-->
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="发货时间：">-->
                                    <!--                                            <span>{{ formData.deliveryTime }}</span>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="订单备注：">
                                            <span>{{ formData.orderRemark }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="productInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="contractList">订单商品</div>
                        <div class="e-table"  style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-input type="text" @blur="getTableData" placeholder="输入搜索关键字" v-model="keywords">
                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData" />
                                    </el-input>
                                    <div class="left-btn" style="margin-left: 20px">
                                        <!--                                        <el-button type="primary" class="btn-greenYellow" @click = "changePrice">批量改价</el-button>-->
                                    </div>
                                </div>
                            </div>
                            <el-table ref="tableRef"
                                      border
                                      style="width: 100%"
                                      :data="tableData"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"/>
                                <el-table-column prop="productImg" label="商品图片" width="130">
                                    <template slot-scope="scope">
                                        <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="productName" label="商品名称" width="150"/>
                                <el-table-column prop="skuName" label="规格" width="100"/>
                                <el-table-column prop="" label="单位"/>
                                <el-table-column prop="" label="材质"/>
                                <el-table-column prop="costPrice" label="成本价" width=""/>
                                <el-table-column prop="buyCounts" label="购买数量" width=""/>
                                <el-table-column prop="productPrice" label="单价（含税）" width="130"/>
                                <el-table-column prop="totalAmount" label="总金额（含税）" width="130"/>
                                <!-- <el-table-column prop="noRatePrice" label="单价（不含税）" width="130"/>
                                <el-table-column prop="noRateAmount" label="总金额（不含税）" width="130"/> -->
                                <el-table-column prop="isComment" label="评论状态" width="">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.isComment === 0">未评价</el-tag>
                                        <el-tag v-if="scope.row.isComment === 1" type="success">已评价</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="gmtCreate" label="创建时间" width="160"/>
                                <!-- <el-table-column prop="buyTime" label="购买时间" width="160"/>
                                <el-table-column prop="remarks" label="备注" width="200"/> -->
                            </el-table>
                        </div>
                        <!--            分页-->
                        <Pagination
                            v-show="tableData != null || tableData.length != 0"
                            :total="paginationInfo.total"
                            :pageSize.sync="paginationInfo.pageSize"
                            :currentPage.sync="paginationInfo.currentPage"
                            @currentChange="getTableData"
                            @sizeChange="getTableData"
                        />
                    </div>
                    <div id="twoOrderList" class="con" v-loading="tableLoading2">
                        <div class="tabs-title" id="contractList">拆分订单</div>
                        <div class="e-table"  style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-input type="text" @blur="getTableData2" placeholder="输入搜索关键字" v-model="keywords2">
                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData2" />
                                    </el-input>
                                </div>
                            </div>
                            <el-table ref="tableRef"
                                      border
                                      style="width: 100%"
                                      :data="tableData2"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column label="订单号" width="240" prop="orderSn">
                                    <template slot-scope="scope">
                                        <span class="action" @click="showTwoOrder(scope.row)">{{scope.row.orderSn}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="商品名称" width="200" prop="untitled"></el-table-column>
                                <el-table-column label="供方确认状态" width="150" prop="affirmState">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.affirmState == 0">待确认</el-tag>
                                        <el-tag type="success" v-if="scope.row.affirmState == 1">已确认</el-tag>
                                        <el-tag type="danger" v-if="scope.row.affirmState == 2">已拒绝</el-tag>
                                        <el-tag type="danger" v-if="scope.row.affirmState == 3">未选择供方</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="状态" width="" prop="state">
                                    <template slot-scope="scope">
                                        <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                                        <span v-if="scope.row.state == 1">已提交</span>
                                        <span v-if="scope.row.state == 2">待确认</span>
                                        <span v-if="scope.row.state == 3">已确认</span>
                                        <span v-if="scope.row.state == 4">待签订合</span>
                                        <span v-if="scope.row.state == 5">已签合同</span>
                                        <span v-if="scope.row.state == 6">待发货</span>
                                        <span v-if="scope.row.state == 7">已关闭</span>
                                        <span v-if="scope.row.state == 8">发货中</span>
                                        <span v-if="scope.row.state == 9">待收货</span>
                                        <span v-if="scope.row.state == 10">已完成</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="供应商名称" width="300" prop="supplierName" />
                                <!--                                <el-table-column label="订单类型" width="100" prop="orderClass">-->
                                <!--                                    <template slot-scope="scope">-->
                                <!--                                        <span v-if="scope.row.orderClass == 3">已拆分订单</span>-->
                                <!--                                    </template>-->
                                <!--                                </el-table-column>-->
                                <!--                                <el-table-column label="商品类型" width="100" prop="productType">-->
                                <!--                                    <template slot-scope="scope">-->
                                <!--                                        <span v-if="scope.row.productType == 0">物资</span>-->
                                <!--                                        <span v-if="scope.row.productType == 10">低值易耗品</span>-->
                                <!--                                    </template>-->
                                <!--                                </el-table-column>-->
                                <el-table-column label="收件人" width="" prop="receiverName"></el-table-column>
                                <el-table-column label="收件人手机号" width="150" prop="receiverMobile" />
                                <el-table-column label="收件地址" width="200" prop="receiverAddress" />
                                <el-table-column label="总金额（含税）" width="120" prop="actualAmount" />
<!--                                <el-table-column label="总金额（不含税）" width="120" prop="noRateAmount" />-->
<!--                                <el-table-column label="税率（%）" width="120" prop="taxRate" />-->
                                <el-table-column label="总金额" width="120" prop="actualAmount" />
                                <!--                    <el-table-column label="物流单号" width="200" prop="deliveryFlowId" >-->
                                <!--                        <template v-slot="scope">-->
                                <!--                            <el-input clearable v-model="scope.row.deliveryFlowId" @change="getChangedRow(scope.row)"></el-input>-->
                                <!--                        </template>-->
                                <!--                    </el-table-column>-->
                                <!--                    <el-table-column label="物流公司" width="200" prop="logisticsCompany" >-->
                                <!--                        <template v-slot="scope">-->
                                <!--                            <el-input clearable v-model="scope.row.logisticsCompany" @change="getChangedRow(scope.row)"></el-input>-->
                                <!--                        </template>-->
                                <!--                    </el-table-column>-->
                                <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                                <el-table-column label="订单提交时间" width="160" prop="flishTime" />
                                <el-table-column label="发货时间" width="160" prop="deliveryTime" />
                                <el-table-column label="完成时间" width="160" prop="successDate" />
                                <el-table-column label="订单备注" width="300" prop="orderRemark"></el-table-column>
                            </el-table>
                        </div>
                        <!--            分页-->
                        <Pagination
                            v-show="tableData2 != null || tableData2.length != 0"
                            :total="paginationInfo2.total"
                            :pageSize.sync="paginationInfo2.pageSize"
                            :currentPage.sync="paginationInfo2.currentPage"
                            @currentChange="getTableData2"
                            @sizeChange="getTableData2"
                        />
                    </div>
                </div>
            </el-tabs>
        </div>
        <el-dialog v-dialogDrag  title="订单详情" :visible.sync="showTwoOrderDialog"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div class="con">
                <div class="tabs-title" id="baseInfo">订单信息</div>
                <div style="width: 100%" class="form">
                    <el-form :model="formData2" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="订单号：">
                                    <span>{{ formData2.orderSn }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="卖方：">
                                    <span>{{ formData2.shopName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="供方名称：">
                                    <span>{{ formData2.supplierName }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="供方确认状态：">
                                    <el-tag v-if="formData2.affirmState == 0">待确认</el-tag>
                                    <el-tag type="success" v-if="formData2.affirmState == 1">已确认</el-tag>
                                    <el-tag type="danger" v-if="formData2.affirmState == 2">已拒绝</el-tag>
                                    <el-tag type="danger" v-if="formData2.affirmState == 3">未选择供方</el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="商品名称：">
                                    <span>{{ formData2.untitled }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="收件人：">
                                    <span>{{ formData2.receiverName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="收件人手机号：">
                                    <span>{{ formData2.receiverMobile }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="收件地址：">
                                    <span>{{ formData2.receiverAddress }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="总金额（含税）：">
                                    <span>{{ formData2.actualAmount }}</span>
                                </el-form-item>
                            </el-col>
<!--                            <el-col :span="12">-->
<!--                                <el-form-item label="总金额（不含税）：">-->
<!--                                    <span>{{ formData2.noRateAmount }}</span>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="是否开发票：">
                                    <el-tag v-if="formData2.orderBillState === 0">初始</el-tag>
                                    <el-tag v-if="formData2.orderBillState === 1">已申请</el-tag>
                                    <el-tag v-if="formData2.orderBillState === 2" type="success">已开票</el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="支付类型：">
                                    <el-tag v-if="formData2.payWay === 1">线上支付</el-tag>
                                    <el-tag v-if="formData2.payWay === 2">内部结算</el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <!--                                    <el-col :span="12">-->
                            <!--                                        <el-form-item label="配送方式：">-->
                            <!--                                            <span>{{ formData.deliveryType }}</span>-->
                            <!--                                        </el-form-item>-->
                            <!--                                    </el-col>-->
                            <!--                            <el-col :span="12">-->
                            <!--                                <el-form-item label="物流单号：">-->
                            <!--                                    <span>{{ formData.deliveryFlowId }}</span>-->
                            <!--                                </el-form-item>-->
                            <!--                            </el-col>-->
                            <el-col :span="12">
                                <el-form-item label="商品类型：">
                                    <el-tag v-if="formData2.productType == 0">物资</el-tag>
                                    <el-tag v-if="formData2.productType == 10">低值易耗品</el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="订单状态：">
                                    <el-tag v-if="formData2.state == 0">草稿</el-tag>
                                    <el-tag v-if="formData2.state == 1">已提交</el-tag>
                                    <el-tag v-if="formData2.state == 2">待确认</el-tag>
                                    <el-tag v-if="formData2.state == 3">已确认</el-tag>
                                    <el-tag v-if="formData2.state == 4">待签订合</el-tag>
                                    <el-tag v-if="formData2.state == 5">已签合同</el-tag>
                                    <el-tag v-if="formData2.state == 6">待发货</el-tag>
                                    <el-tag v-if="formData2.state == 7">已关闭</el-tag>
                                    <el-tag v-if="formData2.state == 8">发货中</el-tag>
                                    <el-tag v-if="formData2.state == 9">待收货</el-tag>
                                    <el-tag v-if="formData2.state == 10">已完成</el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="订单提交时间：">
                                    <span>{{ formData2.flishTime }}</span>
                                </el-form-item>
                            </el-col>
<!--                            <el-col :span="12">-->
<!--                                <el-form-item label="税率：">-->
<!--                                    <span>{{ formData2.taxRate }}%</span>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
                            <!--                            <el-col :span="12">-->
                            <!--                                <el-form-item label="发货时间：">-->
                            <!--                                    <span>{{ formData.deliveryTime }}</span>-->
                            <!--                                </el-form-item>-->
                            <!--                            </el-col>-->
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="订单备注：">
                                    <span>{{ formData2.orderRemark }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
            <div  class="con" v-loading="tableLoading3">
                <div class="tabs-title" id="contractList">订单商品</div>
                <div class="e-table"  style="background-color: #fff">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input clearable type="text" @blur="getTableData3" placeholder="输入搜索关键字" v-model="keywords3">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData3" />
                            </el-input>
                            <div class="left-btn" style="margin-left: 20px">
                                <!--                                        <el-button type="primary" class="btn-greenYellow" @click = "changePrice">批量改价</el-button>-->
                            </div>
                        </div>
                    </div>
                    <el-table ref="tableRef"
                              border
                              style="width: 100%"
                              :data="tableData3"
                              class="table"
                              :max-height="$store.state.tableHeight"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="productName" label="商品名称" width="200px"></el-table-column>
                        <el-table-column prop="skuName" label="规格" width="200px"></el-table-column>
                        <el-table-column prop="productImg" label="商品图片" width="130">
                            <template slot-scope="scope">
                                <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                            </template>
                        </el-table-column>
<!--                        <el-table-column prop="costPrice" label="成本价" width=""></el-table-column>-->
                        <el-table-column prop="productPrice" label="单价（含税）" width="130"/>
<!--                        <el-table-column prop="noRatePrice" label="单价（不含税）" width="130"/>-->
                        <el-table-column prop="buyCounts" label="购买数量" width=""></el-table-column>
                        <el-table-column prop="totalAmount" label="总金额（含税）" width="130"/>
<!--                        <el-table-column prop="noRateAmount" label="总金额（不含税）" width="130"/>-->
                        <el-table-column prop="isComment" label="评论状态" width="">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.isComment === 0">未评价</el-tag>
                                <el-tag v-if="scope.row.isComment === 1" type="success">已评价</el-tag>
                            </template>
                        </el-table-column>
                        <!--                                <el-table-column prop="buyTime" label="购买时间" width="160"></el-table-column>-->
                        <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                        <!--                                <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
                    </el-table>
                </div>
                <!--            分页-->
                <Pagination
                    v-show="tableData3 != null || tableData3.length != 0"
                    :total="paginationInfo3.total"
                    :pageSize.sync="paginationInfo3.pageSize"
                    :currentPage.sync="paginationInfo3.currentPage"
                    @currentChange="getTableData3"
                    @sizeChange="getTableData3"
                />
            </div>
            <el-button class="btn-blue" style="margin-top: 20px;margin-left: 50%" @click="showTwoOrderDialog = false">关闭</el-button>
        </el-dialog>
        <!--        选择供应商-->
        <el-dialog v-dialogDrag id="supplierDialog"  title="选择供应商" :visible.sync="showSupplierList"  width="80%" style="margin-left: 20%;" :close-on-click-modal="false">
            <div  class="con" v-loading="tableLoading4">
                <div class="e-table"  style="background-color: #fff">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input clearable type="text" @blur="getTableData4" placeholder="输入搜索关键字" v-model="keywords4">
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData4" />
                            </el-input>
                        </div>
                    </div>
                    <el-table ref="tableRef"
                              border
                              style="width: 100%"
                              :data="tableData4"
                              class="table"
                              :max-height="$store.state.tableHeight"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column label="操作" width="80">
                            <template slot-scope="scope">
                                <el-button style="padding:0px 8px 0px 8px;"
                                           size="mini"
                                           type="success"
                                           @click="supplierListClick(scope.row)"> 选择
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>
                        <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                    </el-table>
                </div>
                <!--            分页-->
                <Pagination
                    v-show="tableData4 != null || tableData4.length != 0"
                    :total="paginationInfo4.total"
                    :pageSize.sync="paginationInfo4.pageSize"
                    :currentPage.sync="paginationInfo4.currentPage"
                    @currentChange="getTableData4"
                    @sizeChange="getTableData4"
                />
            </div>
            <el-button class="btn-blue" style="margin-top: 20px;margin-left: 50%" @click="showSupplierList = false">关闭</el-button>
        </el-dialog>
        <div class="buttons">
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
import {
    orderItemList,
    shopManageOrderList,
    listSupplierByShopId,
    addOrderSupplier
} from '@/api/platform/order/orders'
import { findByOrderSn, updateOrderPrice } from '@/api/shopManage/order/order'
export default {

    data () {
        return {
            tableLoading4: false,
            showSupplierList: false,
            tableLoading3: false,
            formData2: {},
            showTwoOrderDialog: false,
            tableLoading2: false,
            formLoading: false,
            keywords: null,
            keywords2: null,
            keywords3: null,
            keywords4: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            tableData2: [],
            tableData3: [],
            tableData4: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo3: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo4: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedRow: [],
            tableLoading: false,
            thisCurrentOrderId: null,
        }
    },
    components: {
        Pagination
    },
    created () {
        this.findByOrderSnM()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        supplierListClick (row) {
            let params = {
                supplierId: row.supplierId,
                supplierName: row.supplierName,
                orderId: this.thisCurrentOrderId,
                affirmState: 0,
            }
            this.tableLoading4 = true
            addOrderSupplier(params).then(res => {
                if(res.code != null && res.code == 200) {
                    this.$message.success('操作成功')
                    this.getTableData2()
                    this.showSupplierList = false
                }
                this.tableLoading4 = false
            }).catch(() => {
                this.tableLoading4 = false
            })
        },
        getTableData4 () {
            let params = {
                page: this.paginationInfo4.currentPage,
                limit: this.paginationInfo4.pageSize,
            }
            if(this.keywords4 != null) {
                params.keywords = this.keywords4
            }
            this.tableLoading4 = true
            listSupplierByShopId(params).then(res => {
                this.tableData4 = res.list
                this.paginationInfo4.total = res.totalCount
                this.paginationInfo4.pageSize = res.pageSize
                this.paginationInfo4.currentPage = res.currPage
                this.tableLoading4 = false
            }).catch(() => {
                this.tableLoading4 = false
            })
        },
        // 点击二级订单
        showTwoOrder (row) {
            this.formData2 = row
            this.getTableData3()
            this.showTwoOrderDialog = true
        },
        findByOrderSnM () {
            this.formLoading = true
            findByOrderSn({ orderSn: this.$route.query.orderSn }).then(res => {
                this.formData = res
                this.getTableData()
                this.getTableData2()
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 批量修改排序
        changePrice () {
            if(this.changedRow.length == 0) {
                return this.$message('未修改数据！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateOrderPrice(this.changedRow).then(res => {
                    if(res.code == 200) {
                        this.$message.success('修改成功！')
                        this.findByOrderSnM()
                        this.changedRow = []
                    }
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if(this.changedRow.length == 0) {
                this.changedRow.push({ orderItemId: row.orderItemId, productPrice: row.productPrice })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if(t.orderItemId == row.orderItemId) {
                    t.productPrice = row.productPrice
                    flag = true
                }
            })
            if(!flag) {
                this.changedRow.push({ orderItemId: row.orderItemId, productPrice: row.productPrice })
            }
            flag = true
        },
        getTableData2 () {
            let params = {
                parentOrderId: this.formData.orderId,
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                isQueryTwoOrder: true
            }
            if(this.keywords2 != null) {
                params.keywords = this.keywords2
            }
            this.tableLoading2 = true
            shopManageOrderList(params).then(res => {
                this.tableData2 = res.list
                this.paginationInfo2.total = res.totalCount
                this.paginationInfo2.pageSize = res.pageSize
                this.paginationInfo2.currentPage = res.currPage
                this.tableLoading2 = false
            }).catch(() => {
                this.tableLoading2 = false
            })
        },
        // 获取表格数据
        getTableData3 () {
            let params = {
                orderId: this.formData2.orderId,
                page: this.paginationInfo3.currentPage,
                limit: this.paginationInfo3.pageSize,
            }
            if(this.keywords3 != null) {
                params.keywords = this.keywords3
            }
            this.tableLoading3 = true
            orderItemList(params).then(res => {
                this.tableData3 = res.list
                this.paginationInfo3.total = res.totalCount
                this.paginationInfo3.pageSize = res.pageSize
                this.paginationInfo3.currentPage = res.currPage
                this.tableLoading3 = false
            }).catch(() => {
                this.tableLoading3 = false
            })
        },
        // 获取表格数据
        getTableData () {
            let params = {
                orderId: this.formData.orderId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.keywords != null) {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            orderItemList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        addData () {},
        deleteData () {},
        //取消
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.warningTabs {
    padding-top: 70px;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 780px;
        margin-top: 0px;
    }
}

/deep/ #supplierDialog {
    .el-dialog__body {
        height: 580px;
        margin-top: 0px;
    }
}
</style>
