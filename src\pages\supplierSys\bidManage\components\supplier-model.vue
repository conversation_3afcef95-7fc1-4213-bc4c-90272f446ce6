<!-- 供应商选择 -->
<template>
    <div>
        <el-dialog v-dialogDrag title="选择供应商" id="supplierDialog2" v-loading="showTableLoading"
            :visible.sync="showSupplier" width="80%">
            <div style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        ">
                <div class="box-left" style="max-width: 50%">
                    <div class="e-table" style="background-color: #fff">
                        <div class="top"
                            style="height: 30px; display: flex; justify-content: center; align-items: center;">
                            <div class="search_box">
                                <el-input style="width: 400px" clearable type="text" @keyup.enter.native="getShopLists"
                                    placeholder="输入搜索关键字" v-model="shopData.keywords">
                                    <img src="@/assets/search.png" slot="suffix" @click="getShopLists" alt="搜索" />
                                </el-input>
                            </div>
                            <span style="margin-left: 10px; align-items: center; color: #f1083b">双击选择供应商！</span>
                        </div>
                        <el-table ref="tableShop1" highlight-current-row border :data="shopData.tableData" class="table"
                            height="450px" @row-dblclick="handleCurrentInventoryClickShop">
                            <el-table-column label="序号" type="index" width="60" />
                            <el-table-column prop="enterpriseName" width="" label="企业名称" />
                        </el-table>
                        <Pagination v-show="shopData.tableData || shopData.tableData.length > 0"
                            :total="shopData.paginationInfo.total" :pageSize.sync="shopData.paginationInfo.pageSize"
                            :currentPage.sync="shopData.paginationInfo.currentPage" @currentChange="currentChange"
                            @sizeChange="sizeChange" />
                    </div>
                </div>
                <div class="box-right" style="width: 50%">
                    <div class="e-table">
                        <div class="top" style="height: 30px">
                            <span style="margin-left: 10px; color: #ea083a">双击移除供应商！</span>
                        </div>

                        <el-table ref="tableShop2" highlight-current-row border :data="selectedSupplierList"
                            class="table" height="450px" @row-dblclick="handleRemoveCurrentClickShop">
                            <el-table-column label="序号" type="index" width="60" />
                            <el-table-column prop="enterpriseName" width="" label="企业名称" />
                        </el-table>
                    </div>
                </div>
            </div>
            <span slot="footer">
                <el-button type="primary" class="btn10" style="margin-top: 20px"
                    @click="confirmSupplierDialog">确定</el-button>
                <el-button class="btn10" style="margin-top: 20px" @click="closeSupplierDialog">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>

import { getSupplierShopsList } from '@/api/platform/supplier/supplierAudit'
import { debounce, } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
export default {
    name: 'supplierDialog',
    components: {
        Pagination,
    },
    data () {
        return {
            screenWidth: 0,
            screenHeight: 0,
            showSupplier: false,
            selectedSupplierList: [],
            rightTableHeight: 0,
            showTableLoading: false,
            shopData: {
                tableData: [],
                keywords: null,
                paginationInfo: {
                    // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            // 选中供应商
            selectedSupplierRow: [],
            // 选中供应商名称
            selectedSupplierRowName: [],
            suppliersList: [],
        }
    },
    comments: {
        rightTableHeight () {
            return this.screenHeight - 292
        },
    },
    mounted () {
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    methods: {

        openModel () {
            this.showSupplier = true
            this.getShopLists()
        },
        sizeChange () {
            this.getShopLists()
        },
        currentChange () {
            this.getShopLists()
        },
        closeSupplierDialog () {
            this.showSupplier = false
            this.selectedSupplierList = []
            this.resetDialog()
        },
        resetDialog () {
            this.selectedSupplierList = []
            this.shopData.keywords = null
            this.shopData.paginationInfo.currentPage = 1
        },
        getShopLists () {
            let params = {
                page: this.shopData.paginationInfo.currentPage,
                limit: this.shopData.paginationInfo.pageSize,
                // orderBy: this.filterData.orderBy,
            }
            if (this.shopData.keywords != null) {
                params.keywords = this.shopData.keywords
            }
            this.inventoryTableLoading = true
            getSupplierShopsList(params).then(res => {
                this.shopData.tableData = res.list || []
                this.shopData.paginationInfo.currentPage = res.currPage
                this.shopData.paginationInfo.pageSize = res.pageSize
                this.shopData.paginationInfo.total = res.totalCount
            })
            this.inventoryTableLoading = false
        },
        confirmSupplierDialog () {

            this.selectedSupplierRow = this.selectedSupplierList || []
            this.suppliersList = this.selectedSupplierRow.map(item => {
                return {
                    supplierId: item.enterpriseId,
                    supplierName: item.enterpriseName,
                    disabled: true,
                }
            })
            this.showSupplierList = false
            this.$emit('getSupplierList', this.suppliersList)
            this.closeSupplierDialog()
        },

        handleCurrentInventoryClickShop (row) {
            // 根据id排除已有的供应商
            for (let i = 0; i < this.selectedSupplierList.length; i++) {
                let t = this.selectedSupplierList[i]
                if (t.enterpriseId == row.enterpriseId) {
                    return this.$message.warning('该供应商已选择！')
                }
            }
            this.selectedSupplierList.push(row)
            this.$message.success('选择成功！')
        },
        handleRemoveCurrentClickShop (row) {
            this.selectedSupplierList = this.selectedSupplierList.filter(
                t => t.enterpriseId != row.enterpriseId
            )
        },
        getScreenInfo () {
            this.screenWidth =
                document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight =
                document.documentElement.clientHeight || document.body.clientHeight
        },
        beforeDestroy () {
            window.removeEventListener('resize', this.getScreenInfo)
        },
    }
}
</script>

<style lang="scss" scoped>
@import '../bidingList/index.scss';

.btn10 {
    margin: 2px;
    padding: 4px 12px !important;
    height: auto !important;
    line-height: normal !important;
}
</style>