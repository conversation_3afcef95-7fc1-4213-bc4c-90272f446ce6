<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div v-loading="formLoading" class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName"
                     @tab-click="onChangeTab">
                <el-tab-pane label="物资" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content" style="height: 762px;">
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">物资</div>
                        <!--新增-->
                        <div style="width: 100%" class="form" v-if="showForm">
                            <el-form
                                :model="addForm.formData" :rules="formRules" label-width="200px" ref="formEdit"
                                class="demo-ruleForm"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="物资名称：" prop="relevanceName">
                                            <el-input
                                                placeholder="请选择物资" clearable
                                                v-model="addForm.formData.relevanceName"
                                            ></el-input>
                                            <el-button size="mini" type="primary" @click="importDeviceSelect">选择
                                            </el-button>
                                        </el-form-item>
                                    </el-col>
                                    <!--                    <el-col :span="12">
                                                          <el-form-item label="商品名称：" prop="productName">
                                                            <el-input clearable v-model="addForm.formData.productName"></el-input>
                                                          </el-form-item>
                                                        </el-col>-->
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="关键字（,分隔）：">-->
                                    <!--                                            <el-input clearable  v-model="addForm.formData.productKeyword"></el-input>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                    <el-col :span="12">
                                        <el-form-item label="税率%" prop="taxRate">
                                            <el-input
                                                placeholder="请输入税率" clearable type="number"
                                                v-model="addForm.formData.taxRate"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="分类：" prop="classId">
                                            <category-cascader
                                                customStyle="width: 100%"
                                                style="width: 100%"
                                                :classPath.sync="addForm.formData.classPath"
                                                :classId.sync='addForm.formData.classId'
                                                :catelogPath="addForm.formData.classPath"
                                                :productType="0"
                                                @change="resetRelevanceAndBrand"
                                            ></category-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
<!--                                        <el-form-item label="成本价" prop="costPrice">-->
<!--                                            <el-input-->
<!--                                                placeholder="请输入成本价" clearable type="number"-->
<!--                                                oninput="if(value.length > 16)value = value.slice(0, 16)"-->
<!--                                                v-model="addForm.formData.costPrice"-->
<!--                                            />-->
<!--                                            <el-tooltip placement="top" effect="light">-->
<!--                                                <div slot="content">-->
<!--                                                    物资分公司采购价格-->
<!--                                                </div>-->
<!--                                                <el-icon class="el-icon-question ml10" style="color: #666;"></el-icon>-->
<!--                                            </el-tooltip>-->
<!--                                        </el-form-item>-->
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="品牌：" prop="brandName">
                                            <el-input
                                                placeholder="请选择品牌" clearable disabled
                                                v-model="addForm.formData.brandName"
                                            ></el-input>
                                            <el-button size="mini" type="primary" @click="brandDialog">选择</el-button>
                                        </el-form-item>
                                    </el-col>
                                    <!--                                    <el-col :span="12">-->
                                    <!--                                        <el-form-item label="结算价" prop="settlePrice">-->
                                    <!--                                            <el-input clearable type="number" oninput="if(value.length > 16)value = value.slice(0, 16)" v-model="addForm.formData.settlePrice"></el-input>-->
                                    <!--                                        </el-form-item>-->
                                    <!--                                    </el-col>-->
                                    <el-col :span="12">
                                        <el-form-item label="计量单位" prop="unit">
                                            <el-select
                                                @change="numUnitChange" v-model="addForm.formData.unit"
                                                placeholder="请选择计量单位"
                                            >
                                                <el-option
                                                    v-for="item in addForm.numUnitOptions" :key="item.value"
                                                    :label="item.label" :value="item.label"
                                                >
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="规格：" prop="skuName">
                                            <el-input placeholder="请输入规格" clearable
                                                      v-model="addForm.formData.skuName"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="店铺：" prop="shopId">
                                            <el-input
                                                placeholder="请选择店铺" clearable disabled
                                                v-model="addForm.formData.shopName"
                                            ></el-input>
                                            <el-button size="mini" type="primary" @click="shopDialog">选择</el-button>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!-- <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="原价：" prop="originalPrice">
                                            <el-input
                                                placeholder="请输入原价" clearable type="number"
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                v-model="addForm.formData.originalPrice"
                                            />
                                            <el-tooltip placement="top" effect="light">
                                                <div slot="content">
                                                    平台对外展示的价格
                                                </div>
                                                <el-icon class="el-icon-question ml10" style="color: #666;"></el-icon>
                                            </el-tooltip>
                                        </el-form-item>
                                    </el-col>
                                </el-row> -->
                                <!--                                               <el-row>
                                                                                   <el-col :span="12">
                                                                                       <el-form-item label="区域：" prop="province">
                                                                                           <el-cascader
                                                                                               size="large"
                                                                                               :options="addForm.addressData"
                                                                                               v-model="addForm.selectAddressOptions"
                                                                                               @change="handleAddressChange">
                                                                                           </el-cascader>
                                                                                       </el-form-item>
                                                                                   </el-col>
                                                                                   <el-col :span="12">
                                                                                    <el-form-item label="销售价格" prop="sellPrice">
                                                                                      <el-input
                                                                                        placeholder="请输入销售价格" clearable type="number"
                                                                                        oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                                                        v-model="addForm.formData.sellPrice"
                                                                                      ></el-input>
                                                                                    </el-form-item>
                                                                                  </el-col>
                                                                               </el-row>-->
                                <el-row style="margin-left: 8%">
                                    <el-button type="primary" @click="addRegion">新增区域</el-button>
                                </el-row>
                                <div style="max-height: 25vh;overflow-y: auto;margin: 10px 0;">
                                    <div>
                                        <div v-for="(item, index) in addForm.formData.regionTableData"
                                             :key="item.index">
                                            <el-row>
                                                <el-col :span="12">
                                                    <el-form-item :label="item.regionName">
                                                        <div v-if="item.regionName === '全区域'" style="width: 100%">
                                                            <el-select v-model="item.selectAddressOptionsAll" multiple
                                                                       placeholder="请选择"
                                                                       @change="handleAddressChange1">
                                                                <el-option
                                                                    v-for="item in economizeData"
                                                                    :key="item.value"
                                                                    :label="item.label"
                                                                    :value="item.value"
                                                                >
                                                                </el-option>
                                                            </el-select>
                                                        </div>
                                                        <div v-else style="width: 100%">
                                                            <el-cascader
                                                                style="width: 100%"
                                                                v-model="item.selectAddressOptions"
                                                                @change="handleAddressChange(item.index)"
                                                                :options="marketData"
                                                                :props="{ multiple: true, checkStrictly: true }"
                                                                clearable>
                                                            </el-cascader>
                                                        </div>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="10">
                                                    <el-form-item label="销售价格（含税）："
                                                                  :prop="'regionTableData.' + index + '.taxInPrice'"
                                                                  :rules="formRules.taxInPrice" label-width="200px">
                                                        <el-input
                                                            placeholder="请输入销售价格" clearable type="number"
                                                            oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                            v-model="item.taxInPrice"
                                                        ></el-input>
                                                    </el-form-item>
                                                </el-col>
                                                <el-col :span="1" v-if="index != 0">
                                                    <el-form-item>
                                                        <el-button icon="el-icon-delete" type="text" size="mini"
                                                                   @click="removeOneParamsData(item)"></el-button>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </div>
                                </div>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item required class="uploader" label="物资主图（推荐654*490）："
                                                      prop="adminFile">
                                            <el-upload
                                                :class="addForm.adminFileLength === 1 ? 'hide_box_admin' : ''"
                                                action="fakeaction" ref="adminFileRef"
                                                :file-list="addForm.formData.adminFile" list-type="picture-card"
                                                :before-upload="handleBeforeUpload" :auto-upload="false"
                                                :limit="1"
                                                :on-remove="adminFileRemove" :on-change="adminFileChange"
                                                :on-preview="handlePictureCardPreview"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item required class="uploader" label="物资图片（推荐654*490）："
                                                      prop="productFiles">
                                            <el-upload
                                                action="fakeaction" ref="productFileRef"
                                                :file-list="addForm.formData.productFiles"
                                                list-type="picture-card"
                                                :before-upload="handleBeforeUpload" :auto-upload="false"
                                                :multiple="true"
                                                :on-remove="productFileRemove" :on-change="productFileChange"
                                                :on-preview="handlePictureCardPreview"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            required class="uploader" label="物资小图（推荐250*200）：" prop="minFile"
                                        >
                                            <el-upload
                                                :class="addForm.minFileLength === 1 ? 'hide_box_min' : ''"
                                                action="fakeaction" ref="minFileRef"
                                                :file-list="addForm.formData.minFile" list-type="picture-card"
                                                :before-upload="handleBeforeUpload" :auto-upload="false"
                                                :on-remove="minFileRemove" :on-change="minFileChange"
                                                :on-preview="handlePictureCardPreview"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="物资描述：" prop="productDescribe">
                                            <editor
                                                v-model="addForm.formData.productDescribe" @change="onEditorChange"
                                            ></editor>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
            <div class="buttons">
                <el-button type="primary" @click="submit" class="btn-delete">保存</el-button>
                <el-button @click="handleClose">取消</el-button>
            </div>
        </div>
        <!--表格-->
        <el-dialog
            v-dialogDrag title="选择品牌" :visible.sync="showBrandDialog" width="70%" style="margin-left: 20%;"
            :close-on-click-modal="false"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            clearable type="text" @blur="getBrandTableData" placeholder="输入搜索品牌"
                            v-model="brand.keywords"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getBrandTableData" alt=""/>
                        </el-input>
                    </div>
                </div>
                <el-table v-loading="shopBrandLoading"
                          ref="tableRef" highlight-current-row border style="width: 100%" :data="brand.tableData"
                          class="table" @row-click="handleCurrentClick" :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" prop="index" width="60"/>
                    <el-table-column prop="name" label="品牌名" width="150"/>
                    <!--<el-table-column prop="logo" label="品牌logo" width="90"></el-table-column>-->
                    <el-table-column prop="descript" label="介绍"/>
                    <el-table-column prop="gmtCreate" label="创建时间" width="160"/>
                    <el-table-column prop="gmtModified" label="更新时间" width="160"/>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="brand.tableData != null || brand.tableData.length !== 0"
                    :total="brand.paginationInfo.total" :pageSize.sync="brand.paginationInfo.pageSize"
                    :currentPage.sync="brand.paginationInfo.currentPage" @currentChange="getBrandTableData"
                    @sizeChange="getBrandTableData"
                />
            </span>
            <div class="buttons">
                <el-button @click="showBrandDialog = false">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog
            v-dialogDrag title="选择店铺" :visible.sync="showShopDialog" width="70%"
            style="margin-left: 20%;" :close-on-click-modal="false"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            clearable type="text" @blur="getShopTableList" placeholder="输入搜索关键字"
                            v-model="shop.keywords"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getShopTableList"/>
                        </el-input>
                    </div>
                </div>
                <el-table v-loading="shopDialogLoading"
                          ref="tableRef" highlight-current-row border style="width: 100%" :data="shop.tableData"
                          class="table"
                          @row-click="shopTableRowClick" :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" prop="index" width="60"></el-table-column>
                    <el-table-column prop="shopName" label="店铺名称" width=""></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
            <Pagination
                v-show="shop.tableData != null || shop.tableData.length != 0" :total="shop.paginationInfo2.total"
                :pageSize.sync="shop.paginationInfo2.pageSize" :currentPage.sync="shop.paginationInfo2.currentPage"
                @currentChange="getShopTableList" @sizeChange="getShopTableList"
            />
            <el-button style="margin-top: 20px" @click="showShopDialog = false">取消</el-button>
        </span>

        </el-dialog>
        <!--选择物资库-->
        <el-dialog
            v-dialogDrag :close-on-click-modal="false"
            :visible.sync="showDeviceDialog"
            style="margin-left: 20%;" title="选择物资库" width="70%"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            v-model="inventory.keyWord" clearable placeholder="输入搜索名称" type="text"
                            @blur="getDeviceInventory"
                        >
                            <img slot="suffix" :src="require('@/assets/search.png')" @click="getDeviceInventory"/>
                        </el-input>
                    </div>
                </div>
                <el-table v-loading="inventoryTableLoading"
                          ref="tableRef" :data="inventory.tableData" :max-height="$store.state.tableHeight" border
                          class="table"
                          highlight-current-row style="width: 100%" @row-click="handleCurrentInventoryClick"
                >
                    <el-table-column label="序号" prop="index" width="60"/>
                    <el-table-column label="编号" prop="billNo" width="200"></el-table-column>
                    <el-table-column label="名称" prop="materialName" width="200"></el-table-column>
                    <el-table-column label="规格型号" prop="spec"></el-table-column>
                    <el-table-column label="类别名称" prop="className"></el-table-column>
                    <el-table-column label="计量单位" prop="unit"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
            <Pagination
                v-show="inventory.tableData && inventory.tableData.length > 0"
                :currentPage.sync="inventory.paginationInfo.currentPage"
                :pageSize.sync="inventory.paginationInfo.pageSize"
                :total="inventory.paginationInfo.total" @currentChange="getDeviceInventory"
                @sizeChange="getDeviceInventory"
            />
            <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
        </span>
        </el-dialog>
        <el-dialog :visible.sync="imgPreviewDialog" title="图片预览">
            <img class="center mb20" style="display: block" :src="previewImg" alt="">
        </el-dialog>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>

<script>
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import CategoryCascader from '@/components/category-cascader'
import editor from '@/components/quillEditor'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import $ from 'jquery'
// eslint-disable-next-line no-unused-vars
import { addImgUrl, spliceImgUrl, throttle } from '@/utils/common'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { getBrandPageList } from '@/api/shopManage/brand/brand'
import { uploadFile } from '@/api/platform/common/file'
// import { createShopDevice, getDeviceInfo, updateShopDevice } from '@/api/shopManage/product/repairManage'
// import { getDeviceInventoryPageList } from '@/api/shopManage/product/inventory'
import { createMaterialSupplier, updateMaterialSupplier, listShopList } from '@/api/shopManage/product/materialManage'
import { getMaterialInfo, updateProductState } from '@/api/platform/product/materialManage'
import { queryPageMaterialDtl } from '@/api/shopManage/product/prodcutInventory'

export default {
    data () {
        return {
            zoneListLoading: false,
            zoneList: [],
            imgPreviewDialog: false,
            dialogVisible: false,
            previewImg: '',
            shopDialogLoading: false,
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            mainImg: '',
            smallImg: '',
            dialogImageUrl: '',
            formLoading: false,
            inventoryTableLoading: false,
            formRules: {
                relevanceName: [
                    { required: true, message: '请选择名称', trigger: 'blur' }
                ],
                // brandName: [
                //     { required: true, message: '请选择品牌', trigger: 'blur' },
                // ],
                productName: [
                    { required: true, message: '请输入物资名称', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' }
                ],
                // productMinPrice: [
                //     { required: true, message: '请输入最低价格', trigger: 'blur' },
                //     { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                // ],
                settlePrice: [
                    { required: true, message: '请输入结算价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                skuName: [
                    { required: true, message: '请输入规格', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' },
                    { validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' }
                ],
                stock: [
                    { required: true, message: '请输入库存', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,3})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                costPrice: [
                    { required: true, message: '请输入成本价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                unit: [
                    { required: true, message: '请选择计量单位', trigger: 'blur' }
                ],
                originalPrice: [
                    // { required: true, message: '请输入原价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                shopId: [
                    { required: true, message: '请选择店铺', trigger: 'blur' }
                ],
                taxInPrice: [
                    { required: true, message: '请输入销售价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                // province: [
                //     { required: true, message: '请选择地址', trigger: 'blur' },
                // ],
                // detailedAddress: [
                //     { required: true, message: '请输入详细地址', trigger: 'blur' },
                //     { min: 1, max: 250, message: '超出范围', trigger: 'blur' }
                // ],
                adminFile: [
                    { required: true, message: '请上传物资主图', trigger: 'blur' }
                ],
                productFiles: [
                    { required: true, message: '请上传物资图片', trigger: 'blur' }
                ],
                minFile: [
                    { required: true, message: '请上传物资小图', trigger: 'blur' }
                ],
                productDescribe: [
                    { required: true, message: '请输入描述', trigger: 'blur' },
                    { validator: this.publicFunc.formFunc.verifyFwbSpaces, trigger: 'blur' }
                ]
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            // 商品库
            inventory: {
                tableData: [],
                keywords: null,
                classId: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1
                }
            },
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            showShopDialog: false, // 店铺
            uploadImgSize: 20, // 上传文件大小
            //表单数据
            formData: {},
            regionTableIndex: 1,
            economizeData: [], //只有省数据
            marketData: [], //省市数据
            addForm: {
                formData: {
                    regionTableData: [
                        {
                            index: 1,
                            regionName: '全区域',
                            selectAddressOptionsAll: [],
                            taxInPrice: '',
                            detailAddress: []
                        }
                    ],
                    zone: '',
                    zoneId: '',
                    zonePath: '',
                    productType: 0,
                    isOpenImport: 1,
                    relevanceName: null,
                    productName: null,
                    productKeyword: null,
                    classId: null,
                    classPath: [],
                    productMinPrice: null,
                    brandId: null,
                    brandName: null,
                    shopSort: null,
                    skuName: null,
                    settlePrice: null,
                    costPrice: null,
                    stock: 1,
                    unit: null,
                    shopId: null,
                    originalPrice: null,
                    sellPrice: null,
                    province: null,
                    city: null,
                    county: null,
                    detailedAddress: null,
                    productFiles: [],
                    minFile: [],
                    adminFile: [],
                    productDescribe: null
                },
                adminFileLength: 0,
                minFileLength: 0,
                // 地址
                addressData: regionData, // 地址数据
                selectAddressOptions: [], // 地址选择
                // 计量单位
                numUnitOptions: []
            },
            // 品牌数据
            brand: {
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1
                }
            },
            shopBrandLoading: false,
            shop: {
                keywords: null,
                tableData: [],
                paginationInfo2: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120
        }
    },
    components: {
        Pagination,
        CategoryCascader,
        editor
    },
    created () {
        this.addForm.numUnitOptions = this.materialUnit
        this.rowData = this.$route.params.row
        if (this.rowData && this.rowData.viewType === 'add') {
            this.viewType = 'add'
            if (this.rowData.classPath != null) {
                this.addForm.formData.classPath = this.rowData.classPath
                this.addForm.formData.classId = this.rowData.classPath[this.rowData.classPath.length - 1]
            }
            this.showForm = true
        } else {
            this.getMaterialInfo()
        }
        this.getEconomizeAndMarketList()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['materialUnit']),
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        }
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        }
    },
    methods: {
        // 重新校验form表单某个字段
        checkFormItem (formId) {
            let _this = this
            setTimeout(() => {
                _this.$refs.formEdit.validateField(formId, errorMsg => {
                    if (errorMsg) {
                        // console.error( '校验失败:', errorMsg)
                    } else {
                        // 校验通过逻辑
                    }
                })
            }, 20)
        },
        // 富文本编辑器内容改变
        onEditorChange () {
            let text = this.addForm.formData.productDescribe.replace(/(<([^>]+)>)/ig, '')
            if (this.addForm.formData.productDescribe && text.trim()) {
                this.$refs.formEdit.clearValidate(['productDescribe'])
            } else {
                this.checkFormItem('productDescribe')
            }
        },
        resetRelevanceAndBrand () {
            // let arr = ['brandName', 'brandId', 'relevanceName', 'relevanceId']
            // arr.forEach(item => this.addForm.formData[item] = '')
            // this.$refs.formEdit.clearValidate(['classId'])
            this.checkFormItem('classId')
        },
        addRegion () {
            this.addForm.formData.regionTableData.push({
                index: this.regionTableIndex + 1,
                regionName: this.regionTableIndex == 0 ? '全区域' : '区域' + this.regionTableIndex,
                selectAddressOptions: [],
                taxInPrice: '',
                detailAddress: []
            })
            this.regionTableIndex++
        },
        removeOneParamsData (row) {
            if (row.index === 1) {
                let region = this.addForm.formData.regionTableData[0]
                region.taxInPrice = ''
                region.selectAddressOptionsAll = []
                region.detailAddress = []
            } else {
                this.addForm.formData.regionTableData = this.addForm.formData.regionTableData.filter(obj => obj.index !== row.index)
            }
            // 给区域名称重新赋值
            let indexD = 0
            for(let itemI of this.addForm.formData.regionTableData) {
                if(indexD == 0) {
                    itemI.regionName = '全区域'
                }else {
                    itemI.regionName = '区域' + indexD
                }
                indexD++
            }
            this.regionTableIndex = indexD
        },
        // 下架
        updateStateBatch (state) {
            let params = {
                productIds: [this.addForm.formData.productId],
                state: state
            }
            this.clientPop('info', '您确定要下架这个物资吗！', async () => {
                updateProductState(params).then(res => {
                    this.handleClose()
                    this.message(res)
                })
            })
        },
        // 获取物资详情
        getMaterialInfo () {
            let params = {
                productId: this.rowData.productId,
                productType: 0
            }
            this.formLoading = true
            getMaterialInfo(params).then(res => {
                this.addressFormatShow(res)
                this.addForm.minFileLength = res.minFile.length
                this.addForm.adminFileLength = res.adminFile.length
                // 后端未返回该字段或改字段不为数组则返回空数组
                if(!res.regionTableData || Array.isArray(res.regionTableData)) {
                    res.regionTableData = []
                }
                this.regionTableIndex = res.regionTableData.length
                this.addForm.formData = res
                addImgUrl(res, this.imgUrlPrefixAdd)
                this.inventory.classId = this.addForm.formData.classId
                this.showForm = true
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.formData.unit = value
            this.$refs.formEdit.clearValidate(['unit'])
            this.checkFormItem('unit')
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null) return
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.addForm.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 物资库点击
        handleCurrentInventoryClick (row) {
            this.addForm.formData.relevanceName = row.materialName
            this.addForm.formData.relevanceId = row.billId
            this.addForm.formData.relevanceNo = row.billNo
            this.addForm.formData.classId = row.classId
            this.addForm.formData.classPath = row.classIdPath.split('/')
            this.$refs.formEdit.clearValidate(['relevanceName', 'classId'])
            this.showDeviceDialog = false
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                isActive: 1,
                pageIndex: this.inventory.paginationInfo.currentPage,
                pageSize: this.inventory.paginationInfo.pageSize,
            }
            if (this.inventory.classId != null) {
                params.classId = this.inventory.classId
            }
            if (this.inventory.keyWord != null) {
                params.keyWord = this.inventory.keyWord
            }
            this.inventoryTableLoading = true
            queryPageMaterialDtl(params).then(res => {
                this.inventory.tableData = this.publicFunc.tableFunc.transformTableIndex(res.list, res)
                this.inventory.paginationInfo.total = res.totalCount
                this.inventory.paginationInfo.pageSize = res.pageSize
                this.inventory.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.inventoryTableLoading = false
            }).catch(() => {
                this.inventoryTableLoading = false
            })
        },
        // 获取店铺表格
        getShopTableList () {
            let params = {
                page: this.shop.paginationInfo2.currentPage,
                limit: this.shop.paginationInfo2.pageSize,
            }
            if (this.shop.keywords != null) {
                params.shopName = this.shop.keywords
            }
            this.shopDialogLoading = true
            listShopList(params).then(res => {
                this.shop.tableData = this.publicFunc.tableFunc.transformTableIndex(res.list, res)
                this.shop.paginationInfo2.total = res.totalCount
                this.shop.paginationInfo2.pageSize = res.pageSize
                this.shop.paginationInfo2.currentPage = res.currPage
                this.shopDialogLoading = false
            }).catch(() => {
                this.shopDialogLoading = false
            })
        },
        // 选择物资名称
        importDeviceSelect () {
            /*if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            } else {
                this.inventory.classId = this.addForm.formData.classId
            }*/
            this.showDeviceDialog = true
            this.inventory.paginationInfo.currentPage = 1
            this.getDeviceInventory()
        },

        // 提交
        submit () {
            this.addForm.formData.regionPrice = this.addForm.formData.regionTableData
            this.$refs.formEdit.validate(valid => {
                if (!valid) return false
                spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                if (this.viewType === 'add') {
                    this.formLoading = true
                    createMaterialSupplier(this.addForm.formData).then(res => {
                        if (res.code != null && res.code != 200) {
                            addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                            return this.formLoading = false
                        }
                        let classInfo = {
                            classPath: this.addForm.formData.classPath,
                            classId: this.addForm.formData.classId
                        }
                        // 重置
                        this.resetFormData()
                        // 恢复分类
                        this.addForm.formData.classPath = classInfo.classPath
                        this.addForm.formData.classId = classInfo.classId
                        this.$refs.adminFileRef.clearFiles()
                        this.$refs.productFileRef.clearFiles()
                        this.$refs.minFileRef.clearFiles()
                        this.addForm.minFileLength = 0
                        this.addForm.adminFileLength = 0
                        this.formLoading = false
                        this.message(res)
                    }).finally(() => {
                        this.formLoading = false
                    })
                } else {
                    this.formLoading = true
                    updateMaterialSupplier(this.addForm.formData).then(res => {
                        this.getMaterialInfo()
                        this.message(res)
                    }).catch(() => {
                        this.getMaterialInfo()
                    }).finally(() => this.formLoading = false)
                }
            })
        },
        // 地区
        handleAddressChange2 () {
            let addArr = this.addForm.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.addForm.formData.province = province
            this.addForm.formData.city = city
            this.addForm.formData.county = county
            this.addForm.formData.detailedAddress = province + city + county
        },
        handleAddressChange1 (params) {
            if (params != null && params.length > 0) {
                let addrStr = []
                params.forEach(e => {
                    addrStr.push(CodeToText[e])
                })
                this.addForm.formData.regionTableData[0].detailAddress = addrStr
            }
        },
        handleAddressChange (index) {
            let regionOption = this.addForm.formData.regionTableData.filter(item => item.index === index)[0]
            let addArr = regionOption.selectAddressOptions
            if (addArr != null && addArr.length > 0) {
                let addrStr = []
                addArr.forEach(e => {
                    if (e.length == 1) {
                        addrStr.push(CodeToText[e[0]])
                    } else if (e.length == 2) {
                        addrStr.push(CodeToText[e[0]] + CodeToText[e[1]])
                    }
                })
                regionOption.detailAddress = addrStr
            }
        },
        getEconomizeAndMarketList () {
            let economizeList = []
            let marketList = []
            this.addForm.addressData.forEach((e, i) => {
                economizeList.push({ label: e.label, value: e.value })
                marketList.push({ label: e.label, value: e.value, children: [] })
                e.children.forEach(s => {
                    marketList[i].children.push({ label: s.label, value: s.value })
                })
            })
            this.economizeData = economizeList
            this.marketData = marketList
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // 小图上传
        minFileChange (file, fileList) {
            fileList.pop()
            this.uploadMinFile(file, fileList)
        },
        // 物资主图上传
        adminFileChange (file, fileList) {
            fileList.pop()
            this.uploadAdminFile(file, fileList)
        },
        // 物资图片上传
        productFileChange (file, fileList) {
            fileList.pop()
            this.uploadProductFile(file, fileList)
        },
        // 品牌单选
        handleCurrentClick (row) {
            this.addForm.formData.brandId = row.brandId
            this.addForm.formData.brandName = row.name
            this.showBrandDialog = false
        },
        // 店铺点击
        shopTableRowClick (row) {
            this.addForm.formData.shopId = row.shopId
            this.addForm.formData.shopName = row.shopName
            this.$refs.formEdit.clearValidate(['shopId'])
            this.showShopDialog = false
        },
        // 获取品牌表格
        getBrandTableData () {
            let params = {
                page: this.brand.paginationInfo.currentPage,
                limit: this.brand.paginationInfo.pageSize
            }
            if (this.brand.keywords != null) {
                params.name = this.brand.keywords
            }
            this.shopBrandLoading = true
            getBrandPageList(params).then(res => {
                this.brand.tableData = this.publicFunc.tableFunc.transformTableIndex(res.list, res)
                this.brand.paginationInfo.total = res.totalCount
                this.brand.paginationInfo.pageSize = res.pageSize
                this.brand.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.shopBrandLoading = false
            }).catch(() => {
                this.shopBrandLoading = false
            })
        },
        shopDialog () {
            this.showShopDialog = true
            this.getShopTableList()
        },
        brandDialog () {
            this.showBrandDialog = true
            this.getBrandTableData()
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params.raw)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 上传主图
        uploadAdminFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.adminFileLength = 1
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath
                file.name = res[0].objectName
                file.isMain = 1
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 0
                this.addForm.formData.adminFile.push(file)
                fileList.push(params)
                this.$refs.formEdit.clearValidate(['adminFile'])
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 上传物资图片
        uploadProductFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            uploadFile(form).then(res => {
                let productFile = {}
                productFile.url = res[0].objectPath
                productFile.name = res[0].objectName
                productFile.isMain = 0
                productFile.relevanceType = 1
                productFile.fileType = 1
                productFile.fileFarId = res[0].recordId
                productFile.imgType = 0
                this.addForm.formData.productFiles.push(productFile)
                fileList.push(params)
                this.$refs.formEdit.clearValidate(['productFiles'])
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 上传小图
        uploadMinFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.minFileLength = 1
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 1
                this.addForm.formData.minFile.push(file)
                fileList.push(params)
                this.$refs.formEdit.clearValidate(['minFile'])
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 主图删除
        adminFileRemove (file, fileList) {
            this.addForm.adminFileLength = fileList.length
            let recordId = this.addForm.formData.adminFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.adminFile = []
                this.checkFormItem('adminFile')
            })
        },
        // 物资图片删除
        productFileRemove (file) {
            let files = this.addForm.formData.productFiles
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.productFiles = newFiles
                if (!this.addForm.formData.productFiles.length) {
                    this.checkFormItem('productFiles')
                }
            })

        },
        // 小图删除
        minFileRemove (file, fileList) {
            this.addForm.minFileLength = fileList.length
            let recordId = this.addForm.formData.minFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.minFile = []
                this.checkFormItem('minFile')
            })
        },
        //取消
        handleClose () {
            this.$router.replace('/platform/product/productDatabase')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                productType: 0,
                relevanceName: null,
                productName: null,
                productKeyword: null,
                classId: null,
                classPath: [],
                productMinPrice: null,
                brandId: null,
                brandName: null,
                shopSort: null,
                skuName: null,
                settlePrice: null,
                costPrice: null,
                stock: 1,
                unit: null,
                originalPrice: null,
                sellPrice: null,
                province: null,
                city: null,
                county: null,
                detailedAddress: null,
                productFiles: [],
                adminFile: [],
                minFile: [],
                productDescribe: null
            }
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        }
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
    display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 550px;
        margin-top: 0;
    }
}
</style>
