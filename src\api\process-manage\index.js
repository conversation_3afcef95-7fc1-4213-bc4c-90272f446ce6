// 流程管理相关接口
import service from '@/utils/request'

// eslint-disable-next-line
const { httpGet } = service

/**
 * 获取当前账号是否有审核的流程
 * @param {*} businessKey 业务ID
 * @returns 用户ID
 */
const getAuditUser = (processId, businessKey) => {
    return httpGet({
        url: `/materialMall/platform/processConfig/getAuditUser?processId=${processId}&businessKey=${businessKey}`,
    })
}

/**
 * 获取当前账号是否有提交的流程
 * @param {*} processId 流程ID
 * @returns 用户ID
 */
const getSubmitUser = processId => {
    return httpGet({
        url: `/materialMall/platform/processConfig/getSubmitUser?processId=${processId}`,
    })
}

/**
 * 获取当前账号是否有撤回的流程
 * @param {*} processId 流程ID
 * @param {*} businessKey 业务ID
 * @returns 用户ID
 */
const getWithdrawUser = (processId, businessKey) => {
    return httpGet({
        url: `/materialMall/platform/processConfig/getWithdrawUser?processId=${processId}&businessKey=${businessKey}`,
    })
}

/**
 * 获取流程日志
 * @param {*} businessKey 业务ID
 * @returns 用户ID
 */
const getAuditLog = businessKey => {
    return httpGet({
        url: `/materialMall/platform/processConfig/getAuditLog?businessKey=${businessKey}`,
    })
}

export default {
    getAuditUser,
    getSubmitUser,
    getWithdrawUser,
    getAuditLog
}