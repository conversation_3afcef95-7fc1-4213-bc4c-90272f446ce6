<template>
    <div class="root" v-loading="cartLoading">
        <div class="box center p20">
<!--          共计xx件-->
            <div class="addr-bar dfb">
                <div>共计 {{totalSkuCount}} 件</div>
                <div class="addrPDiv">
                    <div class="addrDiv">收货地址：<div>{{addrObj.addr}}</div></div>
                    <div class="addrToggleDiv" @click="checkedAddressM">切换收货地址</div>
                </div>
            </div>
<!--          表头-->
            <div class="title dfa">
                <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>
                <div>商品</div>
                <div>单价</div>
                <div>税率</div>
                <div>账期</div>
                <div>数量</div>
                <div>小计</div>
                <div>操作</div>
            </div>
<!--          表格内容-->
            <div class="product">
                <div class="shop" v-for="(item, i) in cartList" :key="i">
<!--            店铺栏-->
                    <div class="shop-name dfa">
                        <el-checkbox v-model="item.checked" :label="item.shopName" :indeterminate="false" @change="selectAllInShop(i)"></el-checkbox>
                    </div>
<!--            一条商品详情-->
                    <div class="product-item dfa" style="height: 200px; margin-bottom: 70px" v-for="(product, index) in item.productInfo" :key="index">
<!--            配送区域选择器-->
                      <div class="delivery-area-row">
                        <div class="delivery-area-selector">
                          <span class="delivery-area-label">配送区域：</span>
                          <el-select
                              v-model="product.zoneId"
                              size="mini"
                              placeholder="请选择配送区域"
                              @change="deliveryAreaChange(product)">
                            <el-option
                                v-for="option in getDeliveryAreaOptions(product)"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                          </el-select>
                        </div>
                      </div>
<!--            该商品的选择框-->
                        <div class="checkbox"><el-checkbox v-model="product.checked" @change="(checked) => checkChange({ checked, id: product.cartId })"></el-checkbox></div>
<!--            该商品的图片-->
                      <img class="goodsImg" @click="$router.push({ path: '/mFront/productDetail', query: { productId: product.productId } })" :src="product.productMinImg ? imgUrlPrefixAdd + product.productMinImg : require('@/assets/images/img/queshen3.png')" alt="">
<!--            该商品详情-->
                      <div @click="$router.push({ path: '/mFront/productDetail', query: { productId: product.productId } })"  class="title-box">
                            <el-tooltip class="item" effect="light" :content="product.productName" placement="top-start">
                                <div class="product-name">{{product.productName}}</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="light" :content="product.skuName" placement="top-start">
                                <div class="sku-name">规格型号：{{ product.skuName}}</div>
                            </el-tooltip>
                            <div style="margin-top: 4px">剩余库存：{{ product.stock}}</div>
                            <div style="margin-top: 4px" v-if="(product.productType != 1 || (product.productType == 1 && (product.secondUnit == null || product.secondUnit == '')))">计量单位：{{ product.unit}}</div>
                            <div style="margin-top: 4px" v-if="product.productType == 1 && product.secondUnit != null && product.secondUnit != ''">
                                计量单位：{{product.unit}}
                                共计
                                {{fixed4(product.cartNum * product.secondUnitNum)}}
                                {{product.secondUnit}}
                            </div>
                            <div style="margin-top: 4px">商品类型：
                                <span v-if="product.productType === 0">零星采购商品</span>
                                <span v-if="product.productType === 1">大宗临购商品</span>
                                <span v-if="product.productType === 2">周转材料商品</span>
                            </div>
                        </div>
<!--            该商品单价-->
                        <div class="price-container" style="width: 160px; text-align: left; display: flex; flex-direction: column; gap: 2px;">
                            <div class="price-label">不含税</div>
                            <div class="price-value">￥{{fixed2(product.noTaxPrice || (product.sellPrice / (1 + (product.taxRate/100))))}}</div>
                            <div class="price-label">含税</div>
                            <div class="price-value">￥{{fixed2(product.sellPrice)}}</div>
                        </div>
<!--            该商品税率-->
                        <div class="price">{{product.taxRate}}%</div>
<!--            该商品账期-->
                        <div class="payment-period-select">
                            <span v-if="product.priceType == 1">-</span>
                            <el-select class="zqSelect" v-else
                                v-model="product.paymentPeriod"
                                size="mini"
                                placeholder="请选择账期"
                                @change="paymentPeriodChange(product)">
                                <el-option
                                    v-for="option in getPaymentPeriodOptions(product)"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value">
                                </el-option>
                            </el-select>
                        </div>
<!--            该商品数量-->
                        <div class="num-box">
                            <div class="numCalc df">
                                <el-input-number v-model="product.cartNum" :min="0.0001"
                                    @change="(val) => handleMinNum(val, product)"
                                    placeholder="请输入数量"></el-input-number>
                                <!-- <div @click="changeNum('minus', product)" class="num-box-btn">-</div>
                                <input
                                    @change="cartNumChange(product)"
                                    @input="handleNumInput(product, $event)"
                                    v-model="product.cartNum"
                                    type="text"
                                    class="num-box-input"
                                    placeholder="请输入数量">
                                <div @click="changeNum('plus', product)" class="num-box-btn">+</div> -->
                            </div>
                        </div>
<!--            该商品小计-->
                    <div class="price-group" style="width: 160px;margin: 0;text-align: left; display: flex; flex-direction: column; gap: 2px;">
                        <div class="no-tax-label" style="color: rgba(212, 48, 48, 1);margin-bottom: 10px;">不含税：</div>
                        <div class="no-tax-price" style="color: rgba(212, 48, 48, 1);margin-bottom: 10px;font-weight: bold;font-size: 16px;">￥{{product.numTotalNoRatePrice}}</div>
                        <div class="tax-label" style="color: rgba(212, 48, 48, 1);margin-bottom: 10px;">含税：</div>
                        <div class="tax-price" style="color: rgba(212, 48, 48, 1);margin-bottom: 10px;font-weight: bold;font-size: 16px;">￥{{product.numTotalPrice}}
                            <span v-if="product.priceType === 1" style="font-size: 12px; margin-left: 4px; color: rgba(212, 48, 48, 1);">(参考价)</span>                    </div>
                        </div>
<!--            该商品操作-->
                    <div class="operate">
                        <div  class="operate-btn" @click="deleteProduct(product.cartId)">删除</div>
                        <div class="operate-btn" v-if="!product.collect" @click="addToCollection(product,1)">加入关注</div>
                        <div class="operate-btn" v-else @click="addToCollection(product,0)">取消关注</div>
                    </div>
                    </div>
                </div>
            </div>
<!--          表格底部选项-->
            <div class="bottom-bar dfb">
<!--        全选\删除选中的商品\添加关注\清空购物车-->
                <div class="bar-left dfa">
                    <el-checkbox v-model="selectAllProduct" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>
                    <span @click="deleteBatch">删除选中的商品</span>
                    <span @click="addBatchCollect">添加关注</span>
                    <span @click="clearCart">清空购物车</span>
                </div>
<!--        总价、按钮-->
                <div class="allPrice">
                  <div style="width: 300px;height: 60px;">
                    <div style="color: rgb(127,127,126);margin-top: 5px;">已选择 {{totalSelected}} 件商品<i class="el-icon-arrow-up"></i>&nbsp;&nbsp;不含税总价：<span>￥{{NoTaxTotalPrice}}</span><img src="../../../../assets/images/userCenter/提示.png" alt=""></div>
                    <div style="color: rgb(127,127,126);margin-top: 5px" class="bar-right-tax dfa">税额：<span>￥{{totalTax}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>含税总价：<span>￥{{totalPrice}}</span></div>
                  </div>
                    <div>
                        <!-- 非PCWP用户只能立即下单，pcwp用户才能推送计划或清单-->
                        <button v-if="cartList.flatMap(c=>c.productInfo).filter(p=>p.checked).length == 0" class="allPriceBtn2">请勾选商品</button>
                        <template v-else>
                            <!-- TODO 暂时没加菜单权限 -->
                            <template v-if="hasPermiFun('material-procurement-platform-mall:shoppingcart:submit')">
                                <button @click="submitOrder" class="allPriceBtn1">立即下单</button>
                            </template>
                            <template v-if="hasPermiFun('material-procurement-platform-mall:shoppingcart:order')">
                                <button @click="beforeSubmitPlan" v-if="commitType==0" class="allPriceBtn2">推送零星采购计划</button>
                                <button @click="beforeSubmitPlan" v-else-if="commitType==1" class="allPriceBtn2">推送大宗临购计划</button>
                                <button @click="createSynthesize" v-else-if="commitType==2" class="allPriceBtn2">生成大宗临购清单</button>
                                <button @click="beforeSubmitPlan" v-else-if="commitType==3" class="allPriceBtn2">推送周转材料计划</button>
                                <button @click="createSynthesize" v-else-if="commitType==4" class="allPriceBtn2">生成周转材料清单</button>
                            </template>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <el-dialog v-loading="synthesizeLoading" v-dialogDrag :visible.sync="showSynthesizeMonad"  width="80%" :close-on-click-modal="false">
            <div class="list-title dfa mb20" style="margin-top: -20px">
                {{titleStr}}清单
            </div>
            <el-form :inline="true" ref="synthesizeRef" :model="synthesizeFormData" style="margin-left: 20px" :rules="synthesizeRoles">
                <el-row>
                    <el-col :span="12" >
                        <el-form-item label="供应商单位：" prop="supplierOrgName">
                            {{synthesizeFormData.supplierOrgName}}
<!--                            <el-input  style="width: 300px" disabled v-model="synthesizeFormData.supplierOrgName"></el-input>-->
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="采购单位：" prop="orgName">
                            {{synthesizeFormData.orgName}}
<!--                            <el-input  style="width: 300px" disabled v-model="synthesizeFormData.orgName"></el-input>-->
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" >
                        <el-form-item label="清单类型：" prop="billType">
                            <el-radio v-model="synthesizeFormData.billType" :label="1">浮动价格</el-radio>
                            <el-radio v-model="synthesizeFormData.billType" :label="2">固定价格</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" >
                        <el-form-item label="货款支付周期（月）：" prop="paymentWeek">
                            <el-select v-model="synthesizeFormData.paymentWeek" clearable placeholder="请选择货款支付周期">
                                <el-option v-for="item in payWeekList" :key="item.value" :label="item.label"
                                           :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" >
                        <el-form-item label="项目收货地址选择：" prop="province">
                            <el-cascader
                                clearable
                                style="width: 300px"
                                size="large"
                                :options="addressData"
                                v-model="selectAddressOptions"
                                @change="handleAddressChange">
                            </el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item  label="项目收货详细地址：" prop="receiverAddress">
                            <el-input clearable v-model="synthesizeFormData.receiverAddress" style="width: 300px" placeholder="请输入项目收货地址"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="单据状态：">
                            <el-tag type="info" v-if="synthesizeFormData.state == 0">草稿</el-tag>
                            <el-tag v-if="synthesizeFormData.state == 1">已提交</el-tag>
                            <el-tag type="success" v-if="synthesizeFormData.state == 2">供应商已确认</el-tag>
                            <el-tag type="success" v-if="synthesizeFormData.state == 3">已推送{{titleStr}}计划</el-tag>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="参考总金额（元）：">
                            <span style="color: red">{{synthesizeFormData.referenceSumAmount}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks" >
                            <el-input style="width: 600px;" type="textarea" :auto-resize="false" v-model="synthesizeFormData.remarks"
                                      placeholder="请填写详细需求描述（材质、每单位长度、长度、宽度、高度/厚度、直径、是否镀锌等）" maxlength="1000" show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="list-title dfa mb20">清单明细</div>
            <el-table
                max-height="372px"
                border
                :data="synthesizeFormData.dtls"
                style="min-height: 372px;margin-left: 20px"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="productSn" label="物资编号" width=""/>
                <el-table-column prop="productName" label="商品名称" width=""/>
                <el-table-column prop="materialName" label="物资名称" width=""/>
                <el-table-column prop="spec" label="规格型号" width=""/>
                <el-table-column prop="classNamePath" label="分类路径" width=""/>
                <el-table-column prop="texture" label="材质" width=""/>
                <el-table-column prop="brandName" label="品牌名称" width=""/>
                <el-table-column prop="qty" label="数量" width="">
                    <template v-slot="scope">
                        <el-input
                            type="number"
                            v-model="scope.row.qty"
                            @change="getChangedRow(scope.row)">
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="unit" label="计量单位" width=""/>
                <el-table-column prop="zonePath" label="配送区域" width=""/>
                <el-table-column prop="referencePrice" label="参考单价" width=""/>
                <el-table-column prop="referenceAmount" label="参考金额" width=""/>
            </el-table>
            <div class="list-title dfa mb20">附件信息</div>
            <el-form style="margin-left: 20px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="" prop="" >
                            <el-upload
                                accept=".pdf,.jpeg,.jpg,.png"
                                action="fakeaction"
                                v-loading="uploading"
                                :file-list="[]"
                                :before-upload="handleBeforeUpload"
                                :auto-upload="true"
                                :http-request="uploadStFile"
                                list-type="picture-card">
                                <el-button size="small" type="primary">点击上传</el-button>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                max-height="372px"
                border
                :data="files"
                style="min-height: 372px;margin-left: 20px"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="操作" >
                    <template slot-scope="scope">
                        <i class="el-icon-download" @click="()=>onDownloadFile(scope.row)" />
                        <i class="el-icon-delete" @click="()=>onDeleteFile(scope.row.fileFarId)" />
                        <!-- <i class="el-icon-view" @click="()=>onDeleteFile(scope.row.fileFarId)" /> -->
                    </template>
                </el-table-column>
                <el-table-column prop="" label="缩略图" width="">
                     <template slot-scope="scope">
                        <el-image :src="scope.row.thumbnail">
                        <div slot="placeholder" class="image-slot">
                            加载中<span class="dot">...</span>
                        </div>
                    </el-image>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="文件名称" width=""/>
                <el-table-column prop="fileSize" label="文件大小" width=""/>
                <el-table-column prop="uploadDate" label="上传时间" width=""/>
                <el-table-column prop="founderName" label="上传人" width=""/>
            </el-table>
            <span slot="footer">
                <el-button type="primary" class="btn-greenYellow" @click="saveSynthesizeTemporaryM">保存</el-button>
                <el-button type="primary" class="btn-greenYellow" @click="saveSynthesizeTemporaryM(1)">保存并提交</el-button>
                <el-button @click="clickCloneM">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog
            title="提示" :close-on-click-modal="false"
            :visible.sync="planSubmitVisible"
            width="25%"
            center>
            <el-dialog
                width="80%"
                title="附件列表" :close-on-click-modal="false"
                :visible.sync="attachVisible"
                append-to-body>
                <el-form style="margin-left: 20px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="" prop="" class="uploadDiv">
                                <el-upload
                                    action="fakeaction"
                                    v-loading="uploading"
                                    :file-list="[]"
                                    :before-upload="handleBeforeUploadAttach"
                                    :auto-upload="true"
                                    :http-request="uploadAttachFiles"
                                    list-type="picture-card">
                                    <el-button size="small" type="primary">点击上传</el-button>
                                </el-upload>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-table
                    max-height="372px"
                    border
                    :data="planAttachs"
                    style="min-height: 372px;margin-left: 20px;width: calc(100% - 20px)"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="操作" >
                        <template slot-scope="scope">
                            <i class="el-icon-download" @click="()=>onDownloadFile(scope.row)" />
                            <i class="el-icon-delete" @click="()=>onDeleteAttachFile(scope.row.fileFarId)" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="文件名称" width=""/>
                    <el-table-column prop="fileSize" label="文件大小" width=""/>
                    <el-table-column prop="uploadDate" label="上传时间" width=""/>
                    <el-table-column prop="founderName" label="上传人" width=""/>
                </el-table>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="planAttachs = [];attachVisible = false">取消</el-button>
                    <el-button type="primary" @click="attachVisible = false">确认</el-button>
                </span>
            </el-dialog>
            <div style="padding: 40px 0;text-align: center">
                <span v-if="planAttachs.length==0" style="font-size: 30px">是否需要上传附件？</span>
                <span v-else :title="planfileNames.fullTitle">当前附件：{{ planfileNames.displayText }}</span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="planSubmitVisible = false" style="padding: 0;text-align: center">取消</el-button>
                <el-button type="primary" @click="uploadAttachs" style="padding: 0;text-align: center">上传附件</el-button>
                <el-button type="primary" @click="submitPlan" style="padding: 0;text-align: center">{{planAttachs.length==0?'直接':''}}提交</el-button>
            </span>
        </el-dialog>
        <el-dialog class="front" title="" :visible.sync="addrDialogVisible">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>{{ dialogTitle }}</div>
                    </div>
                    <div class="dialog-close" @click="addrDialogVisible = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <el-table :data="addrList" @row-click="handleCurrentInventoryClick">
                <el-table-column label="收货地址" label-width="560" prop="addr"></el-table-column>
                <el-table-column label="联系人" label-width="152" prop="name"></el-table-column>
                <el-table-column label="联系电话" label-width="110" prop="tel"></el-table-column>
                <el-table-column label="操作" label-width="">
                    <template v-slot="scope">
                        <span @click="handleEditAddr(scope.row)" class="edit-btn">编辑</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="add pointer" @click="createAddress">+ 新增</div>
        </el-dialog>
        <el-dialog class="front" :visible.sync="addDetailDialog" top="8vh">
            <div class="dialog-header">
                <div class="dialog-header-top search_bar">
                    <div class="dialog-title search_bar">
                        <div></div>
                        <div>{{ dialogTitle }}</div>
                    </div>
                    <div class="dialog-close" @click="addDetailDialog = false"><img src="@/assets/images/close.png" alt="" /></div>
                </div>
                <div></div>
            </div>
            <!-- 弹框内容 -->
            <div class="dialog-body center">
                <el-form :model="userAddressForm" ref="addAddressRef" :rules="userAddressFormRules" label-width="80px" :inline="false" label-position="top">
                    <el-form-item label="收货人：" prop="receiverName">
                        <el-input v-model="userAddressForm.receiverName" placeholder="请输入收货人姓名"></el-input>
                    </el-form-item>
                    <el-form-item class="tel" label="手机号码：" prop="receiverMobile">
                        <span>+86</span><el-input v-model="userAddressForm.receiverMobile" placeholder="请输入手机号码"></el-input>
                    </el-form-item>
                    <el-form-item label="选择地址：" prop="detailAddress">
                        <el-cascader
                            size="large"
                            :options="addressData2"
                            v-model="selectAddressOptions2"
                            @change="handleAddressChange2">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item class="address" label="详细地址：" prop="detailAddress">
                        <el-input v-model="userAddressForm.detailAddress" placeholder="请输入详细收货地址"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer">
                <button class="butSub" @click="createAddressM">保存</button>
            </span>
            </div>
        </el-dialog>
        <BillOfMaterials/>
    </div>
</template>
<script>
import BigNumber from 'bignumber.js'
import BillOfMaterials from '@/pages/frontStage/mall/revolving-materials/bill-of-materials'
import { addShopCollect, addBatch } from '@/api/frontStage/productCollect'
import { create, getDefaultAddress, getList } from '@/api/frontStage/shippingAddr'
import {
    getShoppingCartList,
    changeCartNum,
    deleteSku,
    emptyCart,
    createSynthesizeTemporary,
    isSynthesizeTemporary,
    createRatail,
    createReval,
    createBulkRatail,
    updateCartInfo
} from '@/api/frontStage/userCenter'
import { previewFile, uploadFile, thumbnailImage } from '@/api/platform/common/file'
import { createMaterialOrder } from '@/api/frontStage/order'
import { debounce, removeExtraDigit } from '@/utils/common'
import { mapState } from 'vuex'
import { updateChecked } from '@/api/frontStage/userCenter'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
const regionData_sc = regionData.filter(item => item.value == '510000')
import moment from 'moment'
import { hasPermiFun } from '@/utils/instruct/permission'

export default {
    components: {
        BillOfMaterials
    },
    data () {
        return {
            addrObj: {
                addr: '', name: '', tel: ''
            },
            dialogTitle: '选择收货地址',
            addDetailDialog: false,
            userAddressForm: { // 新增编辑地址表单
                detailAddress: null,
            },
            userAddressFormRules: {
                receiverName: [
                    { required: true, message: '请输入收件人', trigger: 'blur' },
                    { min: 1, max: 10, message: '超过限制', trigger: 'blur' }
                ],
                receiverMobile: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' }
                ],
            },
            selectAddressOptions2: [], // 地址选择
            addressData2: regionData_sc, // 地址数据
            addrDialogVisible: false,
            addrList: [
                // { id: 0, addr: '四川成都市成华区保和街道' },
            ],
            // 地址
            addressData: regionData_sc, // 地址数据
            showSynthesizeMonad: false,
            titleStr: '大宗零购',
            synthesizeLoading: false,
            synthesizeFormData: {
                receiverAddress: null,
            },

            synthesizeRoles: {
                receiverAddress: [
                    { required: true, message: '请输入地址', trigger: 'blur' },
                ],
                billType: [
                    { required: true, message: '请选择大宗临购清单类型', trigger: 'blur' },
                ],
                paymentWeek: [
                    { required: true, message: '请选择货款支付周期', trigger: 'blur' },
                ],
                province: [
                    { required: true, message: '请选择地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
            },
            cartLoading: false,
            disableBtn: false,
            selectedAddr: null,
            totalPrice: '',
            noTaxTotalPrice: '',
            payWeekList: [
                { label: '0月', value: 0 },
                { label: '1月', value: 1 },
                { label: '2月', value: 2 },
                { label: '3月', value: 3 },
                { label: '4月', value: 4 },
                { label: '5月', value: 5 },
                { label: '6月', value: 6 },
            ],
            selectAddressOptions: [],
            selectAllProduct: false,
            // 购物车列表
            cartList: [],
            changedSku: null,
            uploading: false,
            files: [],
            planSubmitVisible: false,
            planAttachs: [],
            attachVisible: false,
        }
    },
    watch: {
        cartList: {
            handler (newVal) {
                let checkAll = true
                newVal.forEach(newItem => {
                    // 判断店铺全选
                    newItem.checked = newItem.productInfo.every(subItem => subItem.checked)
                    // 如果有店铺未全选则取消购物车全选状态
                    if(!newItem.checked) checkAll = false
                    newItem.productInfo.forEach(subItem => {
                        // 计算价格
                        // if(isNaN(Number(subItem.cartNum))) subItem.cartNum = 1
                        // subItem.numTotalPrice = this.fixed2((Number(subItem.sellPrice) * subItem.cartNum))

                        if (subItem.paymentPeriod === undefined) {
                            this.$set(subItem, 'paymentPeriod', 1)
                        }
                        if(isNaN(Number(subItem.cartNum))) subItem.cartNum = 1
                        const total = Number(subItem.sellPrice) * subItem.cartNum
                        subItem.numTotalPrice = this.fixed2(total)

                        // 计算不含税总价 - 优先使用后台返回的不含税单价
                        if (subItem.noTaxPrice) {
                            // 使用后台返回的不含税单价计算不含税总价
                            console.log('使用后台不含税单价:', subItem.noTaxPrice, '数量:', subItem.cartNum, '计算结果:', Number(subItem.noTaxPrice) * subItem.cartNum)
                            subItem.numTotalNoRatePrice = this.fixed2(Number(subItem.noTaxPrice) * subItem.cartNum)
                        } else {
                            // 如果后台没有返回不含税单价，则前端计算
                            const taxRate = subItem.taxRate / 100 || 0 // 处理未设置税率的情况
                            if (taxRate > 0) {
                                // 先计算不含税单价（保留2位小数）
                                const noTaxUnitPrice = this.fixed2(Number(subItem.sellPrice) / (1 + taxRate))
                                // 再用不含税单价乘以数量
                                subItem.numTotalNoRatePrice = this.fixed2(noTaxUnitPrice * subItem.cartNum)
                            } else {
                                subItem.numTotalNoRatePrice = this.fixed2(total)
                            }
                        }
                        // 设置配送区域的中文名称显示
                        if (subItem.zoneId) {
                            subItem.zonePath = this.getDeliveryAreaName(subItem, subItem.zoneId)
                        }
                    })
                })
                this.selectAllProduct = checkAll
                this.calcTotalPrice()
            },
            deep: true
        },
        // 监听用户修改sku的数量
        totalProductCount () {
            if(JSON.stringify(this.changedSku == '{}')) return
            this.handleNumChange(this.changedSku)
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 商品总数
        totalProductCount () {
            let num = 0
            this.cartList.forEach(item => {
                item.productInfo.forEach(subItem => num += subItem.cartNum)
            })
            return num
        },
        // 商品sku总数量
        totalSkuCount () {
            let len = 0
            this.cartList.forEach(item => len += item.productInfo.length)
            return len
        },
        // 全选的商品数量
        totalSelected () {
            let count = 0
            this.cartList.forEach(item => {
                if(item.checked) { // 店铺是否全选
                    return count += item.productInfo.length
                }
                item.productInfo.forEach(subItem => subItem.checked ? count += 1 : null)
            })
            return count
        },
        // 总税额计算
        totalTax () {
            if (!this.cartList || this.cartList.length === 0) {
                return '0.00'
            }
            let tax = new BigNumber(0)
            // 深度遍历所有商品
            this.cartList.forEach(item => {
                item.productInfo.forEach(product => {
                    if (product.checked) {
                        // 直接使用含税和不含税价格计算税额
                        const withTax = new BigNumber(product.numTotalPrice || 0)
                        const withoutTax = new BigNumber(product.numTotalNoRatePrice || 0)
                        tax = tax.plus(withTax.minus(withoutTax))
                    }
                })
            })
            return this.fixed2(tax)
        },
        allChecked () {
            return this.cartList.flatMap(c=>c.productInfo).filter(p=>p.checked)
        },
        commitType () {
            const allChecked = this.allChecked
            console.log('allChecked', allChecked)
            // 零星
            if (allChecked.every(p=>p.productType == 0)) {
                return 0
            }
            // 大宗计划
            if (allChecked.every(p=>p.productType == 1 && p.priceType == 0)) {
                return 1
            }
            // 大宗清单
            if (allChecked.every(p=>p.productType == 1 && p.priceType == 1)) {
                return 2
            }
            // 周材计划
            if (allChecked.every(p=>p.productType == 2 && p.priceType == 0)) {
                return 3
            }
            // 周材清单
            if (allChecked.every(p=>p.productType == 2 && p.priceType == 1)) {
                return 4
            }
            return -1
        },
        planfileNames () {

            const names = this.planAttachs.map(file => file.name || '')
            const fullTitle = names.join(',')
            const displayNames = names.slice(0, 3).map(name => {
                return name.length > 10 ? name.slice(0, 10) + '...' : name
            })

            const displayText = displayNames.join('，') + (names.length > 3 ? ' 等' : '')

            return {
                displayText,
                fullTitle
            }
        }
    },
    methods: {
        hasPermiFun,
        clickCloneM () {
            this.synthesizeFormData = { receiverAddress: null }
            this.selectAddressOptions = []
            this.showSynthesizeMonad = false
        },

        deliveryAreaChange (product) {
            // 查找当前选中的配送区域对象（从商品特定的选项中查找）
            const deliveryOptions = this.getDeliveryAreaOptions(product)
            const area = deliveryOptions.find(opt => opt.value === product.zoneId)
            if (!area) {
                this.$message.error('请选择有效的配送区域')
                return
            }

            // 从regionPriceList中找到对应的区域价格信息
            const regionPrice = this.findRegionPriceByAreaCode(product, product.zoneId)
            if (regionPrice) {
                // 立即更新本地价格显示
                if (regionPrice.markUpNum) {
                    product.taxInPrice = regionPrice.bonusTaxInPrice // 含税单价
                    product.price = regionPrice.price // 不含税单价
                    product.noTaxPrice = regionPrice.price // 不含税单价
                    product.sellPrice = regionPrice.bonusTaxInPrice // 销售价格使用含税价格
                } else {
                    product.taxInPrice = regionPrice.taxInPrice // 含税单价
                    product.price = regionPrice.price // 不含税单价
                    product.noTaxPrice = regionPrice.price // 不含税单价
                    product.sellPrice = regionPrice.taxInPrice // 销售价格使用含税价格
                }

                // 赋值价格区域id
                product.regionPriceId = regionPrice.regionPriceId

                // 更新配送区域的中文名称显示
                product.zonePath = area.label

                // 使用防抖处理，与数量改变逻辑一致
                this.handleAreaChange(product, area)
            }
        },
        saveSynthesizeTemporaryM (num) {
            for (let i = 0; i < this.synthesizeFormData.dtls.length; i++) {
                let t = this.synthesizeFormData.dtls[i]
                if(t.qty == null || Number(t.qty) == 0) {
                    return this.$message.error('商品为【' + t.productName + '】数量不能为0！')
                }
            }
            this.$refs.synthesizeRef.validate(valid=>{
                if(valid) {
                    let str = ''
                    if(num != null && num == 1) {
                        str = '您确定要保存并提交吗？'
                    }else {
                        str = '您确定要保存吗？'
                    }
                    this.clientPop('info', str, async () => {
                        this.synthesizeLoading = true
                        if(num != null && num == 1) {
                            this.synthesizeFormData.isSubmit = 1
                        }
                        // 这里区分大宗和周材
                        let stType
                        if (this.commitType == 2) {
                            stType = 0  // 大宗清单
                        }
                        if (this.commitType == 4) {
                            stType = 1  // 周材清单
                        }
                        createSynthesizeTemporary({ ...this.synthesizeFormData, attachments: this.files, stType }).then(res => {
                            if(res.code == 200) {
                                this.$message.success('操作成功！')
                                this.delCardM()
                                this.synthesizeFormData = {
                                    receiverAddress: null,
                                }
                                this.selectAddressOptions = []
                                this.showSynthesizeMonad = false
                            }
                        }).finally(() => {
                            this.synthesizeLoading = false
                        })
                    })
                }else {
                    return this.$message.error('请检查非空项！')
                }
            })
        },
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.synthesizeFormData.province = province
            this.synthesizeFormData.city = city
            this.synthesizeFormData.county = county
            if(province == null && city == null && county == null) {
                this.synthesizeFormData.receiverAddress = null
            }else {
                this.synthesizeFormData.receiverAddress = province + city + county
            }
        },
        // 辅助方法：根据ID查找商品
        findProductById (cartId) {
            for (const shop of this.cartList) {
                for (const product of shop.productInfo) {
                    if (product.cartId === cartId) {
                        return product
                    }
                }
            }
            return null
        },
        // 修改单个商品选择的方法
        async checkChange ({ id, checked }) {
            if (checked) {
                // 如果要选中这个商品，需要校验
                const currentProduct = this.findProductById(id)
                if (!currentProduct) return
                // 获取当前已选中的其他商品（来自所有店铺）
                const otherSelectedProducts = this.getSelectedProduct().filter(p => p.cartId !== id)
                // 如果已经有选中的商品，检查商品类型是否相同
                if (otherSelectedProducts.length > 0) {
                    const existingProductType = otherSelectedProducts[0].productType
                    const existingZoneId = otherSelectedProducts[0].zoneId
                    const existingTaxRate = otherSelectedProducts[0].taxRate
                    const existingPaymentPeriod = otherSelectedProducts[0].paymentPeriod
                    const existingPriceType = otherSelectedProducts[0].priceType
                    // 检查商品类型
                    if (currentProduct.productType !== existingProductType) {
                        this.$message.error('购物车只能勾选同一种类型的商品')
                        // 重置商品的选中状态为false，阻止勾选
                        this.$nextTick(() => {
                            currentProduct.checked = false
                        })
                        return // 不执行选中操作
                    }
                    // 商品价格类型（一口价和参考价必须相同）
                    if (currentProduct.priceType !== existingPriceType) {
                        this.$message.error('购物车只能勾选同一种价格类型的商品')
                        // 重置商品的选中状态为false，阻止勾选
                        this.$nextTick(() => {
                            currentProduct.checked = false
                        })
                        return // 不执行选中操作
                    }
                    // 检查税率
                    if (currentProduct.taxRate !== existingTaxRate) {
                        this.$message.error('购物车只能勾选同一种税率的商品')
                        // 重置商品的选中状态为false，阻止勾选
                        this.$nextTick(() => {
                            currentProduct.checked = false
                        })
                        return // 不执行选中操作
                    }
                    // 检查账期
                    if (currentProduct.paymentPeriod !== existingPaymentPeriod) {
                        this.$message.error('购物车只能勾选同一种账期的商品')
                        // 重置商品的选中状态为false，阻止勾选
                        this.$nextTick(() => {
                            currentProduct.checked = false
                        })
                        return // 不执行选中操作
                    }
                    // 检查配送区域
                    if (currentProduct.zoneId !== existingZoneId) {
                        this.$message.error('购物车只能勾选同一配送区域')
                        // 重置商品的选中状态为false，阻止勾选
                        this.$nextTick(() => {
                            currentProduct.checked = false
                        })
                        return // 不执行选中操作
                    }
                }
            }
            this.cartLoading = true
            // 执行原有的选中逻辑
            await updateChecked({ id, checked: checked ? 1 : 0 })
            await this.getShoppingCartList()
            this.cartLoading = false
        },
        async updateCheckedState () {
            let products = this.cartList.map(item => item.productInfo).flat(1)
            if(products.length === 0) return
            let requests = []
            products.forEach(item => {
                let { cartId, checked } = item
                let req = new Promise(resolve => {
                    updateChecked({ id: cartId, checked: checked ? 1 : 0 }).then(res => {
                        resolve(res)
                    })
                })
                requests.push(req)
            })
            await Promise.all(requests)
            await this.getShoppingCartList()
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 获取修改后的行
        getChangedRow (row) {
            // if(row.isTwoUnit === 1) {
            //     if(row.twoUnitNum <= 0) {
            //         row.twoUnitNum = 0
            //     }
            //     if(row.twoUnitNum >= 999999) {
            //         row.twoUnitNum = 999999
            //     }
            //     row.twoUnitNum = this.fixed4(row.twoUnitNum)
            //     row.qty = this.fixed4(row.twoUnitNum / row.secondUnitNum)
            // }
            // if(row.isTwoUnit === 0) {
            if(row.qty < 0) {
                row.qty = 0
            }
            if(row.qty > 999999) {
                row.qty = 999999
            }
            row.qty = this.fixed4(row.qty)
            if(row.isTwoUnit === 1) {
                row.twoUnitNum = this.fixed4(row.qty * row.secondUnitNum)
            }
            // }
            let sumAmount = 0
            for (let i = 0; i < this.synthesizeFormData.dtls.length; i++) {
                let t = this.synthesizeFormData.dtls[i]
                let amount = this.fixed2(Number(t.qty) * Number(t.referencePrice))
                t.referenceAmount = amount
                t.synthesizeAmount = amount
                sumAmount = this.fixed2(Number(sumAmount) + (Number(amount)))
            }
            this.synthesizeFormData.referenceSumAmount = sumAmount
            this.synthesizeFormData.synthesizeSumAmount = sumAmount
        },
        uploadAttachs () {
            this.attachVisible = true
        },
        beforeSubmitPlan () {
            this.planAttachs = []
            this.planSubmitVisible = true
        },
        createBillOfMaterialsSynthesize () {

        },
        // 推送计划到后端
        submitPlan () {
            this.$confirm('您确定要推送所选商品到计划吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let list = this.getSelectedProduct()
                if(list == null || list.length === 0) {
                    this.$message({ message: '未选中数据', type: 'info' })
                    return
                }
                for (const item of list) {
                    if (item.shopState === 0) {
                        this.$message.error({ message: '商品所属店铺无接单权限，无法推送计划' })
                        return
                    }
                }
                // 区域分组判断
                const areaSet = new Set()
                list.forEach(item => {
                    // 兼容 zoneId 为空的情况
                    areaSet.add(item.zoneId || '')
                })
                if (areaSet.size > 1) {
                    return this.$message.error('不同配送区域的商品不能推送到同一个计划')
                }

                let cardIds = list.map(item => item.cartId)
                this.cartLoading = true
                // TODO 附件
                const payload = {
                    cardIds,
                    attachments: this.planAttachs
                }
                let pushFunc, planName
                if(this.commitType == 0) {
                    pushFunc = createRatail
                    planName = '零星采购计划'
                }else if (this.commitType == 1) {
                    pushFunc = createBulkRatail
                    planName = '大宗临购计划'
                }else if (this.commitType == 3) {
                    pushFunc = createReval
                    planName = '周转材料计划'
                }
                pushFunc(payload).then(res => {
                    if(res.code !== 200) return
                    this.getShoppingCartList()
                    this.$bus.$emit('refreshCart')
                    this.$alert(`推送${planName}成功,请及时去PCWP审核计划`, '提示', {
                        confirmButtonText: '确定',
                        type: 'success'
                    }).then(() => this.delCardM())
                }).finally(() => {
                    this.cartLoading = false
                    this.planSubmitVisible = false
                })
            })
        },
        delCardM () {
            let list = this.getSelectedProduct()
            if(list == null || list.length === 0) {
                this.$message({ message: '未选中数据', type: 'info' })
                return
            }
            let arr = list.map(item => item.cartId)
            deleteSku(arr).then(res => {
                if(res.code !== 200) return
                // this.$message({
                //     message: '推送数据已清除',
                //     type: 'success'
                // })
                this.$bus.$emit('refreshCart')
                this.getShoppingCartList()
            })
        },
        cartNumChange (item) {
            // 保存用户输入前的原始值，用于错误时恢复
            if (!item.originalCartNum) {
                item.originalCartNum = item.cartNum
            }

            // 验证输入值：只能输入大于0的整数
            const inputValue = item.cartNum

            // 检查是否为空或无效值
            if (!inputValue || inputValue === '' || inputValue === null || inputValue === undefined) {
                item.cartNum = item.originalCartNum
                this.$message.warning('数量不能为空')
                return
            }

            // 转换为数字并检查
            const numValue = Number(inputValue)

            // 检查是否为有效数字
            if (isNaN(numValue)) {
                item.cartNum = item.originalCartNum
                this.$message.warning('请输入有效的数字')
                return
            }

            // 检查是否为正数
            if (numValue <= 0) {
                item.cartNum = item.originalCartNum
                this.$message.warning('数量必须大于0')
                return
            }

            // 检查是否为整数
            if (!Number.isInteger(numValue)) {
                item.cartNum = item.originalCartNum
                this.$message.warning('数量只能输入整数')
                return
            }

            // 更新为整数值
            item.cartNum = numValue

            // 传递给后端
            this.handleNumChange(item)
        },
        // 实时限制用户输入，只允许输入数字
        handleNumInput (product, event) {
            const value = event.target.value
            // 只允许输入数字，移除非数字字符
            const numericValue = value.replace(/[^0-9]/g, '')

            // 如果输入值发生了变化，更新输入框
            if (numericValue !== value) {
                product.cartNum = numericValue
                // 手动更新输入框的值
                this.$nextTick(() => {
                    event.target.value = numericValue
                })
            }
        },
        // 处理账期变更
        paymentPeriodChange (item) {
            this.disableBtn = true

            // 保存原始数据用于回滚
            const originalPaymentPeriod = item.originalPaymentPeriod || item.paymentPeriod
            const oldZoneId = item.zoneId

            // 直接调用更新方法，传递正确的参数
            this.updatePaymentPeriodDirect(item, originalPaymentPeriod, oldZoneId)
        },
        // 直接更新账期
        async updatePaymentPeriodDirect (item, originalPaymentPeriod, oldZoneId) {
            try {
                // 获取当前账期对应的价格信息
                const regionPrice = this.findRegionPriceByAreaCode(item, item.zoneId)

                // 构建更新参数，按照后端接口要求
                const updateParams = {
                    cartId: item.cartId,
                    paymentPeriod: item.paymentPeriod, // Integer类型的账期
                    zoneId: item.zoneId, // 区域id
                    zoneAddr: item.zonePath // 区域地址
                }

                // 如果找到了对应的价格信息，添加商品价格
                if (regionPrice) {
                    updateParams.productPrice = regionPrice.taxInPrice // 使用含税价格作为商品价格
                    updateParams.noTaxPrice = regionPrice.price // 不含税价格
                    updateParams.regionPriceId = regionPrice.regionPriceId // 价格区域id
                }

                await updateCartInfo(updateParams)

                // 重新获取购物车数据，获取最新的价格信息
                await this.getShoppingCartList()

                // 验证更新结果
                const updatedItem = this.findCartItemById(item.cartId)
                if (!updatedItem) {
                    throw new Error('未找到更新后的商品数据')
                }

                // 验证账期是否真正更新
                if (updatedItem.paymentPeriod !== item.paymentPeriod) {
                    // 恢复前端显示
                    item.paymentPeriod = updatedItem.paymentPeriod
                    this.$message.warning('账期更新失败，请重试')
                    this.disableBtn = false
                    return
                }

                // 处理配送区域和价格更新
                this.handleDeliveryAreaAfterPaymentPeriodChange(updatedItem, oldZoneId)

                this.disableBtn = false

                // 账期变更后检查是否与其他已选商品账期一致
                this.checkPaymentPeriodConsistency(updatedItem)

            } catch (error) {
                // 恢复原始账期
                item.paymentPeriod = originalPaymentPeriod
                this.$message.error('账期更新失败：' + (error.message || '未知错误'))
                this.disableBtn = false
            }
        },
        // 处理账期变更后的配送区域和价格更新
        handleDeliveryAreaAfterPaymentPeriodChange (updatedItem, oldZoneId) {
            // 获取新账期对应的配送区域选项
            const newDeliveryOptions = this.getDeliveryAreaOptions(updatedItem)

            // 重置配送区域选择
            let needUpdateDeliveryArea = false

            // 智能选择配送区域
            if (oldZoneId && newDeliveryOptions.some(opt => opt.value === oldZoneId)) {
                // 如果新账期下还有相同的配送区域，保持选中状态
                updatedItem.zoneId = oldZoneId
                const selectedOption = newDeliveryOptions.find(opt => opt.value === oldZoneId)
                if (selectedOption) {
                    updatedItem.zonePath = selectedOption.label
                }
            } else if (newDeliveryOptions.length > 0) {
                // 否则选择第一个可用的配送区域
                updatedItem.zoneId = newDeliveryOptions[0].value
                updatedItem.zonePath = newDeliveryOptions[0].label
                needUpdateDeliveryArea = true
            } else {
                // 没有可用的配送区域
                updatedItem.zoneId = null
                updatedItem.zonePath = null
            }

            // 更新价格信息
            if (updatedItem.zoneId) {
                const regionPrice = this.findRegionPriceByAreaCode(updatedItem, updatedItem.zoneId)
                if (regionPrice) {
                    updatedItem.taxInPrice = regionPrice.taxInPrice
                    updatedItem.price = regionPrice.price
                    updatedItem.noTaxPrice = regionPrice.price // 不含税单价
                    updatedItem.sellPrice = regionPrice.taxInPrice
                    updatedItem.regionPriceId = regionPrice.regionPriceId // 赋值价格区域id
                }

                // 如果配送区域发生了变化，需要再次更新到后端
                if (needUpdateDeliveryArea) {
                    updateCartInfo({
                        cartId: updatedItem.cartId,
                        zoneId: updatedItem.zoneId,
                        zoneAddr: updatedItem.zonePath,
                        paymentPeriod: updatedItem.paymentPeriod,
                        productPrice: updatedItem.sellPrice, // 含税价格
                        noTaxPrice: updatedItem.noTaxPrice, // 不含税价格
                        regionPriceId: updatedItem.regionPriceId // 价格区域id
                    }).catch(() => {
                        // 静默处理错误
                    })
                }
            }
        },
        // 检查账期一致性
        checkPaymentPeriodConsistency (changedItem) {
            // 如果当前商品未被选中，不需要检查
            if (!changedItem.checked) {
                return
            }
            // 获取所有已选中的其他商品
            const otherSelectedProducts = this.getSelectedProduct().filter(p => p.cartId !== changedItem.cartId)
            // 如果没有其他选中商品，不需要检查
            if (otherSelectedProducts.length === 0) {
                return
            }
            // 检查账期是否一致
            const existingPaymentPeriod = otherSelectedProducts[0].paymentPeriod
            if (changedItem.paymentPeriod !== existingPaymentPeriod) {
                // 账期不一致，取消该商品的勾选
                this.$message.warning('账期不一致，已自动取消该商品的选中状态')
                this.$nextTick(async () => {
                    changedItem.checked = false
                    // 更新选中状态到服务器
                    await updateChecked({ id: changedItem.cartId, checked: 0 })
                    await this.getShoppingCartList()
                })
            }
        },
        // 批量收藏
        addBatchCollect () {
            let list = this.getSelectedProduct()
            if(list == null || list.length === 0) {
                this.$message({ message: '未选中数据', type: 'info' })
                return
            }
            let arr = list.map(item => item.productId)
            addBatch(arr).then(res =>{
                if(res.code !== 200) return
                this.$message({ message: '添加关注成功！', type: 'success' })
                this.getShoppingCartList()
            })

        },
        // 获取购物车列表
        async getShoppingCartList () {
            this.cartList = await getShoppingCartList()
            // 初始化每个商品的原始数量值和账期
            this.initOriginalCartNum()
        },
        // 初始化商品的原始数量值和账期
        initOriginalCartNum () {
            this.cartList.forEach(shop => {
                shop.productInfo.forEach(product => {
                    // 设置原始数量值，用于错误时恢复
                    product.originalCartNum = product.cartNum

                    // 保存原始账期值，用于错误时恢复
                    if (product.originalPaymentPeriod === undefined) {
                        product.originalPaymentPeriod = product.paymentPeriod
                    }

                    // 初始化账期：如果没有设置账期，使用第一个可用的账期
                    if (product.paymentPeriod === undefined || product.paymentPeriod === null) {
                        const paymentOptions = this.getPaymentPeriodOptions(product)
                        if (paymentOptions.length > 0) {
                            product.paymentPeriod = paymentOptions[0].value
                            product.originalPaymentPeriod = paymentOptions[0].value
                        }
                    }

                    // 设置商品的zoneId和regionPriceId，确保下拉框能正确显示
                    this.setProductZoneId(product)
                })
            })
        },
        // 设置商品的zoneId，确保下拉框能正确显示
        setProductZoneId (product) {
            if (!product.zoneId && product.regionPriceList && Array.isArray(product.regionPriceList)) {
                // 过滤账期匹配的regionPrice
                const filteredRegionPrices = product.regionPriceList.filter(regionPrice => {
                    return regionPrice.accountPeriod === product.paymentPeriod
                })

                if (filteredRegionPrices.length > 0) {
                    const firstRegionPrice = filteredRegionPrices[0]
                    if (firstRegionPrice && firstRegionPrice.areaCode) {
                        let areaCodes = []
                        if (Array.isArray(firstRegionPrice.areaCode)) {
                            areaCodes = firstRegionPrice.areaCode
                        } else if (typeof firstRegionPrice.areaCode === 'string') {
                            // 直接按逗号分割字符串
                            areaCodes = firstRegionPrice.areaCode.split(',').map(code => code.trim())
                        } else {
                            areaCodes = [firstRegionPrice.areaCode]
                        }
                        product.zoneId = areaCodes[0] || firstRegionPrice.areaCode
                        product.regionPriceId = firstRegionPrice.regionPriceId // 设置价格区域id
                    }
                }
            }
        },
        // 根据areaCode查找对应的区域价格信息
        findRegionPriceByAreaCode (product, areaCode) {
            if (!product.regionPriceList || !Array.isArray(product.regionPriceList)) {
                return null
            }
            let filteredRegionPrices = []
            // 先过滤账期匹配的regionPrice
            if(product.priceType != 1) {
                filteredRegionPrices = product.regionPriceList.filter(regionPrice => {
                    return regionPrice.accountPeriod === product.paymentPeriod
                })
            }else {
                filteredRegionPrices = product.regionPriceList
            }

            // 遍历过滤后的regionPriceList查找匹配的区域价格
            for (const regionPrice of filteredRegionPrices) {
                if (regionPrice.areaCode) {
                    // 解析areaCode（可能是数组或逗号分隔的字符串）
                    let areaCodes = []
                    if (Array.isArray(regionPrice.areaCode)) {
                        areaCodes = regionPrice.areaCode
                    } else if (typeof regionPrice.areaCode === 'string') {
                        // 直接按逗号分割字符串
                        areaCodes = regionPrice.areaCode.split(',').map(code => code.trim())
                    } else {
                        areaCodes = [regionPrice.areaCode]
                    }

                    // 检查当前areaCode是否在areaCodes中
                    if (areaCodes.includes(areaCode)) {
                        return regionPrice
                    }
                }
            }

            return null
        },
        // 获取商品的账期选项
        getPaymentPeriodOptions (product) {
            if (!product.regionPriceList || !Array.isArray(product.regionPriceList)) {
                return []
            }

            // 从regionPriceList中提取所有的accountPeriod
            const accountPeriods = new Set()
            product.regionPriceList.forEach(regionPrice => {
                if (regionPrice.accountPeriod !== undefined && regionPrice.accountPeriod !== null) {
                    accountPeriods.add(regionPrice.accountPeriod)
                }
            })

            // 转换为选项格式并排序
            const options = Array.from(accountPeriods).map(period => ({
                label: `${period}个月账期`,
                value: period
            })).sort((a, b) => a.value - b.value)

            return options
        },
        // 获取商品当前账期对应的配送区域选项
        getDeliveryAreaOptions (product) {
            if (!product.regionPriceList || !Array.isArray(product.regionPriceList)) {
                return []
            }
            let filteredRegionPrices = []
            // 过滤当前账期对应的regionPrice
            if(product.priceType != 1) {
                filteredRegionPrices = product.regionPriceList.filter(regionPrice => {
                    return regionPrice.accountPeriod === product.paymentPeriod
                })
            }else {
                filteredRegionPrices = product.regionPriceList
            }

            // 提取配送区域选项
            const areaMap = new Map()

            filteredRegionPrices.forEach(regionPrice => {
                if (regionPrice.area && regionPrice.areaCode) {
                    // 拆分area字符串（如"攀枝花市,泸州市,德阳市"）
                    const areaNames = regionPrice.area.split(',').map(name => name.trim())
                    // 拆分areaCode字符串（如"510300,510400,510500"）
                    let areaCodes = []
                    if (Array.isArray(regionPrice.areaCode)) {
                        areaCodes = regionPrice.areaCode
                    } else if (typeof regionPrice.areaCode === 'string') {
                        // 直接按逗号分割字符串
                        areaCodes = regionPrice.areaCode.split(',').map(code => code.trim())
                    } else {
                        areaCodes = [regionPrice.areaCode]
                    }

                    // 将area和areaCode一一对应展示
                    areaNames.forEach((areaName, index) => {
                        const areaCode = areaCodes[index] || areaCodes[0] // 如果数量不匹配，使用第一个code
                        if (areaName && areaCode && !areaMap.has(areaCode)) {
                            areaMap.set(areaCode, {
                                label: areaName, // 单个区域名称
                                value: areaCode // 单个区域代码
                            })
                        }
                    })
                }
            })

            // 转换为数组并按照areaCode排序
            return Array.from(areaMap.values()).sort((a, b) => a.value.localeCompare(b.value))
        },
        // 根据zoneId获取配送区域的中文名称
        getDeliveryAreaName (product, zoneId) {
            if (!zoneId) return ''

            const deliveryOptions = this.getDeliveryAreaOptions(product)
            const option = deliveryOptions.find(opt => opt.value === zoneId)
            return option ? option.label : zoneId
        },
        // 根据cartId查找购物车中的商品
        findCartItemById (cartId) {
            for (const shop of this.cartList) {
                for (const product of shop.productInfo) {
                    if (product.cartId === cartId) {
                        return product
                    }
                }
            }
            return null
        },
        // 校验选中商品的类型和税率是否一致
        validateSelectedProducts (products) {
            if (!products || products.length === 0) {
                return { valid: true, message: '' }
            }
            // 获取第一个商品的类型、配送区域和税率作为基准
            const firstProduct = products[0]
            const baseProductType = firstProduct.productType
            const baseZoneId = firstProduct.zoneId
            const baseTaxRate = firstProduct.taxRate
            const existingPaymentPeriod = firstProduct.paymentPeriod
            // 检查所有商品是否具有相同的类型和税率
            for (let i = 1; i < products.length; i++) {
                const product = products[i]
                // 检查商品类型
                if (product.productType !== baseProductType) {
                    return {
                        valid: false,
                        message: '购物车只能勾选同一种类型的商品'
                    }
                }
                // 检查税率
                if (product.taxRate !== baseTaxRate) {
                    return {
                        valid: false,
                        message: '购物车只能勾选同一种税率的商品'
                    }
                }
                // 检查账期
                if (product.paymentPeriod !== existingPaymentPeriod) {
                    return {
                        valid: false,
                        message: '购物车只能勾选同一账期的商品'
                    }
                }
                // 检查配送区域
                if (product.zoneId !== baseZoneId) {
                    return {
                        valid: false,
                        message: '购物车只能勾选同一配送区域'
                    }
                }
            }
            return { valid: true, message: '' }
        },
        // 验证收货地址与配送区域是否匹配
        validateDeliveryAddress (selectedProducts) {
            if (!this.addrObj || !this.addrObj.addr) {
                return {
                    valid: false,
                    message: '请选择收货地址'
                }
            }

            const receiverAddress = this.addrObj.addr

            // 由于所有商品的配送区域都相同，只需检查第一个商品的配送区域
            const firstProduct = selectedProducts[0]
            if (!firstProduct.zoneAddr) {
                return {
                    valid: false,
                    message: '商品配送区域信息缺失，请重新选择配送区域'
                }
            }

            // 将配送区域字符串按逗号分割，检查收货地址是否包含在配送区域中
            const deliveryAreas = firstProduct.zoneAddr.split(',').map(area => area.trim())
            const isAddressInDeliveryArea = deliveryAreas.some(area => {
                // 检查收货地址是否包含配送区域中的任一区域
                return receiverAddress.includes(area)
            })

            if (!isAddressInDeliveryArea) {
                return {
                    valid: false,
                    message: `收货地址"${receiverAddress}"不在配送区域"${firstProduct.zoneAddr}"范围内，请更换收货地址或重新选择配送区域`
                }
            }

            return { valid: true, message: '' }
        },
        // 全选所有商品
        toggleSelectAll () {
            if (this.selectAllProduct) {
                // 如果要全选，先获取所有商品进行校验
                let allProducts = []
                this.cartList.forEach(shop => {
                    shop.productInfo.forEach(product => {
                        allProducts.push(product)
                    })
                })
                const validation = this.validateSelectedProducts(allProducts)
                if (!validation.valid) {
                    this.$message.error(validation.message)
                    this.selectAllProduct = false // 重置全选状态
                    return
                }
            }
            // 执行全选或取消全选
            this.cartList.forEach(item => {
                item.productInfo.forEach(subItem => {
                    subItem.checked = this.selectAllProduct
                })
            })
            this.updateCheckedState()
        },
        // 修改店铺全选的商品
        selectAllInShop (shopIndex) {
            const shop = this.cartList[shopIndex]
            if (shop.checked) {
                // 如果要全选该店铺，需要校验
                // 1. 先获取当前已选中的其他店铺商品
                let otherSelectedProducts = []
                this.cartList.forEach((otherShop, index) => {
                    if (index !== shopIndex) {
                        otherShop.productInfo.forEach(product => {
                            if (product.checked) {
                                otherSelectedProducts.push(product)
                            }
                        })
                    }
                })
                // 2. 将当前店铺的所有商品加入待校验列表
                const allSelectedProducts = [...otherSelectedProducts, ...shop.productInfo]
                // 3. 校验所有将被选中的商品
                const validation = this.validateSelectedProducts(allSelectedProducts)
                if (!validation.valid) {
                    this.$message.error(validation.message)
                    shop.checked = false // 重置店铺选择状态
                    return
                }
            }
            // 执行店铺全选或取消全选
            shop.productInfo.forEach(product => {
                product.checked = shop.checked
            })
            this.updateCheckedState()
        },
        // 获取选中的所有商品
        getSelectedProduct () {
            let arr = []
            this.cartList.forEach(item => {
                item.productInfo.forEach(product => {
                    product.shopState = item.shopState
                })
                if(item.checked) { // 店铺是否全选
                    return arr = arr.concat(item.productInfo)
                }
                item.productInfo.forEach(subItem => subItem.checked ? arr.push(subItem) : null)
            })
            return arr
        },
        // 更改sku对应的商品数量
        changeNum (action, item) {
            this.changedSku = item
            if(action === 'minus') {
                if(item.cartNum === 1) return
                item.cartNum--
                if (item.cartNum <= 0) {
                    item.cartNum = 1
                }
            } else {
                item.cartNum > item.stock ? item.cartNum = item.stock : item.cartNum++
            }
            this.handleNumChange(item)
        },
        handleMinNum (val, item) {
            this.changedSku = item
            if (val === 0) {
                item.cartNum = 1
            } else {
                item.cartNum = Number(this.fixed4(val))
            }
            this.handleNumChange(item)
        },
        // 删除商品
        deleteProduct (id) {
            this.clientPop('info', '您确定删除此商品吗？', async () => {
                let res = await deleteSku([id])
                if(res.code !== 200) return
                this.$message({ message: '删除成功', type: 'success' })
                this.$bus.$emit('refreshCart')
                await this.getShoppingCartList()
            })
        },
        // 删除选中的商品
        deleteBatch () {
            let list = this.getSelectedProduct()
            if(list == null || list.length === 0) {
                this.$message({ message: '未选中数据', type: 'info' })
                return
            }
            this.clientPop('info', '您确定要删除选中的商品吗？', async () => {
                let arr = list.map(item => item.cartId)
                deleteSku(arr).then(res => {
                    if(res.code !== 200) return
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    })
                    this.$bus.$emit('refreshCart')
                    this.getShoppingCartList()
                })
            })
        },
        // 添加到收藏
        addToCollection (product, state) {
            let { productId, productType } = product
            let params = { productId, collectType: 1 }
            let message = state === 1 ? '关注成功' : '取消关注成功'
            if(state === 1) {
                params = { ...params, productType, state: 1 }
            }
            addShopCollect(params).then(res => {
                if(res.code !== 200) return
                this.$message({ message, type: 'success' })
                this.getShoppingCartList()
            })
        },
        // 生成大宗临购
        createSynthesize () {
            if(this.disableBtn) return this.$message.warning('数据加载中...')
            let list = this.getSelectedProduct()
            if(list == null || list.length === 0) {
                this.$message({ message: '未选中数据', type: 'info' })
                return
            }
            for (let i = 0; i < list.length; i++) {
                let it = list[i]
                if(it.productType === 1) {
                    this.titleStr = '大宗临购'
                }else if(it.productType === 2) {
                    this.titleStr = '周转材料'
                }else {
                    this.$message.error('商品：【' + it.productName + '】不属于大宗临购或周转材料商品不能生成！' )
                    return
                }
            }

            this.cartLoading = true
            let cardIds = list.map(item => item.cartId)
            isSynthesizeTemporary(cardIds).then(res => {
                if(res != null && res.code == null) {
                    this.synthesizeFormData = res
                    this.selectAddressOptions = []
                    this.showSynthesizeMonad = true
                }
            }).finally(() => {
                this.cartLoading = false
            })
        },
        // 清空购物车
        clearCart () {
            this.clientPop('info', '您确定要清空购物车吗？', async () => {
                emptyCart().then(res => {
                    if(res.message === '操作成功') {
                        this.$message({ message: '清空成功', type: 'success' })
                    }
                    this.$bus.$emit('refreshCart')
                    this.getShoppingCartList()
                })
            })
        },
        // 计算订单总价
        calcTotalPrice () {
            let price = new BigNumber(0)
            let noTaxPrice = new BigNumber(0)
            this.cartList.forEach(item => {
                item.productInfo.forEach(item1 => {
                    if (item1.checked) {
                        // 累加含税总价
                        price = price.plus(new BigNumber(item1.numTotalPrice))
                        // 累加不含税总价
                        noTaxPrice = noTaxPrice.plus(new BigNumber(item1.numTotalNoRatePrice))
                    }
                })
            })
            this.totalPrice = this.fixed2(price)
            this.NoTaxTotalPrice = this.fixed2(noTaxPrice)
        },
        // 立即下单
        submitOrder () {
            // 权限和商品验证
            // const { isSubmitOrder, isInterior } = this.userInfo
            const selectedProducts = this.getSelectedProduct()
            if (selectedProducts.length === 0) {
                this.$message.error('未选择商品')
                return
            }
            // if (isInterior === 1) {
            //     if (!isSubmitOrder) {
            //         this.$message.error('没有下单权限，请联系管理员！')
            //         return
            //     }
            // }
            // 验证收货地址与配送区域是否匹配
            const addressValidation = this.validateDeliveryAddress(selectedProducts)
            if (!addressValidation.valid) {
                this.$message.error(addressValidation.message)
                return
            }

            // 直接下单，不再区分内部和外部用户
            const cartIds = selectedProducts.map(item => item.cartId)
            const orderObj = {
                cartIds,
                receiverName: this.addrObj.name,
                receiverMobile: this.addrObj.tel,
                receiverAddress: this.addrObj.addr,
                payPrice: this.totalPrice,
                payWay: 2,
                orderRemark: '',
                auditorId: null,
                auditorName: null
            }
            localStorage.setItem('materialCartIdSubmit', JSON.stringify(cartIds))
            createMaterialOrder(orderObj).then(res => {
                // 判断返回结果，可能是直接的data，也可能是包含code的对象
                if (res && res.code && res.code !== 200) {
                    this.$message.error(res.message || '订单提交失败')
                    return
                }
                this.$message.success('订单提交成功')
                this.$bus.$emit('refreshCart')
                this.getShoppingCartList()
                // this.$router.push({ name: 'materialOrderList' })
            }).catch(err => {
                console.error('订单提交失败:', err)
                this.$message.error('订单提交失败')
            })
        },
        // 发送修改sku对应商品数量的请求
        updateCartNum (item) {
            if(!item.cartNum) return

            // 保存当前输入的值和原始值
            const inputValue = item.cartNum
            const originalValue = item.originalCartNum || item.cartNum

            this.disableBtn = true
            // 使用removeExtraDigit处理用户输入的值
            changeCartNum({ changeNum: removeExtraDigit(inputValue), id: item.cartId }).then(() => {
                // 成功后更新原始值为当前值
                item.originalCartNum = inputValue
                this.getShoppingCartList()
                this.disableBtn = false
            }).catch(error => {
                // 如果后端返回错误（如库存不足），恢复原始值
                item.cartNum = originalValue
                this.disableBtn = false

                // 显示错误信息
                if (error && error.message) {
                    this.$message.error(error.message)
                } else {
                    this.$message.error('修改数量失败，已恢复原始值')
                }
            })
        },
        // 发送修改配送区域的请求
        updateDeliveryArea (product, area) {
            if (!product.cartId || !area) return

            // 查找对应的区域价格信息
            const regionPrice = this.findRegionPriceByAreaCode(product, area.value)

            this.disableBtn = true
            const params = {
                cartId: product.cartId,
                zoneAddr: area.label, // 区域地址
                zoneId: area.value, // 区域id
                paymentPeriod: product.paymentPeriod // 账期
            }

            // 如果找到了区域价格信息，添加商品价格参数
            if (regionPrice && regionPrice.markUpNum) {
                params.productPrice = regionPrice.bonusTaxInPrice // 添加购物车时商品价格（使用含税价格）
                params.noTaxPrice = regionPrice.price // 不含税价格
                params.regionPriceId = regionPrice.regionPriceId // 价格区域id
            }else {
                params.productPrice = regionPrice.taxInPrice // 添加购物车时商品价格（使用含税价格）
                params.noTaxPrice = regionPrice.price // 不含税价格
                params.regionPriceId = regionPrice.regionPriceId // 价格区域id
            }

            updateCartInfo(params).then(() => {
                this.getShoppingCartList()
                this.disableBtn = false
            }).catch(() => {
                this.disableBtn = false
            })
        },
        uploadAttachFiles ({ file }) {
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', 3)
            form.append('isResetName', 1)
            this.uploading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    // this.$message.error(res.msg)
                }else {
                    let resO = res[0]
                    const fileData = {
                        name: resO.objectName,
                        thumbnail: undefined,
                        fileSize: file.size,
                        fileFarId: resO.recordId,
                        founderName: this.userInfo.userName,
                        founderId: this.userInfo.userId,
                        uploadDate: moment().format('YYYY-MM-DD')
                    }
                    this.planAttachs.push(fileData)
                }
            }).finally(() =>{
                this.uploading = false
            })
        },
        uploadStFile ({ file }) {
            const fileType = file.type == 'application/pdf' ? 3 : 1
            const form = new FormData()
            form.append('files', file)
            form.append('bucketName', 'mall-private') //存储桶名称
            form.append('directory', 'material') // 商城类型
            form.append('fileType', fileType)
            form.append('isResetName', 1)
            this.uploading = true
            uploadFile(form).then(res => {
                if(res.code != null && res.code != 200) {
                    this.$message.error(res.msg)
                }else {
                    let resO = res[0]
                    const url = URL.createObjectURL(file)
                    const fileData = {
                        name: resO.objectName,
                        url,
                        thumbnail: undefined,
                        fileSize: file.size,
                        fileFarId: resO.recordId,
                        fileType,
                        founderName: this.userInfo.userName,
                        founderId: this.userInfo.userId,
                        uploadDate: moment().format('YYYY-MM-DD')
                    }
                    thumbnailImage({ recordId: resO.recordId }).then(res => {
                        const blob = new Blob([res])
                        const thumbnail = window.URL.createObjectURL(blob)
                        fileData.thumbnail = thumbnail
                    })
                    this.files.push(fileData)
                }
            }).finally(() =>{
                this.uploading = false
            })
        },
        handleBeforeUpload (file) {
            return this.handleBeforeUploadFunc(['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'], 200)(file)
        },
        handleBeforeUploadAttach (file) {
            return this.handleBeforeUploadFunc([], 200)(file)
        },
        handleBeforeUploadFunc (allowedTypes, maxSize) {
            return file => {
                if(file.size > maxSize * 1024 * 1024) {
                    this.$message.error(`上传的图片大小不能超过 ${maxSize}MB!`)
                    return false
                }
                if ((allowedTypes.length > 0) && !allowedTypes.includes(file.type)) {
                    this.$message.error('仅支持上传图片（.jpg, .jpeg, .png）和PDF格式')
                    return false
                }
                return true
            }
        },
        onDeleteFile (fileFarId) {
            this.files = this.files.filter(file => file.fileFarId !== fileFarId)
        },
        onDeleteAttachFile (fileFarId) {
            this.planAttachs = this.planAttachs.filter(file => file.fileFarId !== fileFarId)
        },
        onDownloadFile ({ fileFarId, name }) {
            this.uploading = true
            previewFile({ recordId: fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const link = document.createElement('a')
                link.href = url
                link.download = name
                document.body.appendChild(link)
                link.click()

                // 清理
                document.body.removeChild(link)
                URL.revokeObjectURL(url)
            }).finally(() =>{
                this.uploading = false
            })
        },
        // 获取默认地址
        getDefaultAddressM () {
            getDefaultAddress().then(res => {
                if(res) {
                    this.addrObj.name = res.receiverName
                    this.addrObj.tel = res.receiverMobile
                    this.addrObj.addr = res.detailAddress
                    this.addrObj.city = res.city
                }
            })
        },
        // 地址表格点击
        handleCurrentInventoryClick (row) {
            this.addrObj = row
            this.addrDialogVisible = false
        },
        // 创建编辑地址统一接口
        createAddressM () {
            this.$refs.addAddressRef.validate(valid => {
                if (valid) {
                    create(this.userAddressForm).then(res => {
                        if(res.code == 200) {
                            this.$message({
                                message: res.message,
                                type: 'success'
                            })
                            this.getAddRess()
                            this.addDetailDialog = false
                        }
                    })
                }
            })
        },
        // 编辑地址
        handleEditAddr (row) {
            let obj = {
                addressId: row.addressId,
                detailAddress: row.addr,
                receiverName: row.name,
                receiverMobile: row.tel,
            }
            this.userAddressForm = obj
            //地址选择器回显
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions2 = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
            this.addDetailDialog = true
        },
        // 地址选择
        handleAddressChange2 () {
            let addArr = this.selectAddressOptions2
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.userAddressForm.province = province
            this.userAddressForm.city = city
            this.userAddressForm.county = county
            this.userAddressForm.detailAddress = province + city + county
        },
        // 创建
        createAddress () {
            this.userAddressForm = {
                detailAddress: null,
            },
            this.selectAddressOptions2 = []
            this.addDetailDialog = true
        },
        // 切换地址
        checkedAddressM () {
            this.getAddRess()
            this.addrDialogVisible = true
        },
        // 获取地址
        getAddRess () {
            // 获取收货地址
            getList({ page: 1, limit: 30 }).then(res => {
                if(!res.list[0]) return
                let address = []
                // 显示默认地址
                res.list.forEach(item => {
                    let obj = {
                        addressId: item.addressId,
                        checked: false,
                        addr: item.detailAddress,
                        name: item.receiverName,
                        tel: item.receiverMobile,
                        province: item.province,
                        city: item.city,
                        county: item.county,
                    }
                    address.push(obj)
                })
                this.addrList = address
            })
        },
    },
    async created () {
        this.getDefaultAddressM()
        this.cartLoading = true
        this.handleNumChange = debounce(this.updateCartNum)
        this.handleAreaChange = debounce(this.updateDeliveryArea)
        // 获取购物车列表
        await this.getShoppingCartList()
        this.calcTotalPrice()
        this.cartLoading = false
    },
}
</script>
<style scoped lang="scss">
.root {
    height: 100%;
    padding: 20px 0;
    background-color: #f5f5f5;

    .box {
        width: 1326px;
        background-color: #fff;
    }
}
/deep/ .el-checkbox {
    color: rgba(51, 51, 51, 1);
    .el-checkbox__inner {
        border: 1px solid rgba(204, 204, 204, 1);
    }
}
.addr-bar {
    height: 30px;
    &>div:first-child {
        font-size: 20px;
        font-weight: 500;
        color: rgba(212, 48, 48, 1);
    }
    &>div:last-child {
        color: rgba(51, 51, 51, 1);
    }
    /deep/ .el-input__inner {
        width: 216px;
        height: 26px;
    }
}
.addrPDiv {
    .addrDiv, .addrToggleDiv {display: inline-block;vertical-align: top;line-height: 20px;}
    .addrDiv {
        padding: 0 10px;font-size: 14px;margin-right: 12px;
        background-color: rgba(242, 242, 242, 1);
        border-radius: 10px;color: #666;
        div {
            color: #333;max-width: 600px;display: inline-block;vertical-align: top;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    .addrToggleDiv {color: #216EC5;font-size: 14px;cursor: pointer;}
}

.title {
    height: 52px;
    margin-bottom: 12px;
    padding-left: 20px;
    color: rgba(51, 51, 51, 1);
    background-color: rgba(250, 250, 250, 1);
    /deep/ .el-checkbox {width: 210px;}
    &>div:nth-child(2) {width: 270px;}
    &>div:nth-child(3) {width: 140px;}
    &>div:nth-child(4) {width: 129px;}
    &>div:nth-child(5) {width: 129px;}
    &>div:nth-child(6) {width: 158px;}
    &>div:nth-child(7) {width: 158px;}
    &>div:nth-child(8) {width: 158px;}
}
.product {
    min-height: 600px;
    margin-bottom: 30px;
    .shop-name {
        padding: 22px 0 14px 20px;
        img {
            width: 22px;
            height: 22px;
            margin-left: 3px;
        }
    }
    & .product-item:not(:last-of-type) {margin-bottom: 10px;}
    .product-item {
        position: relative;
        border: 1px solid rgba(230, 230, 230, 1);
        padding-top: 40px; /* 为右上角配送区域腾出空间 */
        /* 配送区域选择器，放在右上角 */
        .delivery-area-row {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
            .delivery-area-selector {
                display: inline-flex;margin-left: 10px;
                align-items: center;
                .delivery-area-label {
                    font-size: 14px;
                    color: #606266;
                    margin-right: 5px;
                }
                /deep/ .el-select {
                    width: 240px;
                }
            }
        }
        /* 商品信息行样式 */
        .product-info-row {
            display: flex;
            height: 120px;
            img {
                width: 100px;
                height: 100px;
            }
            .checkbox {
                padding: 0 30px 0 20px;
            }
            .title-box {
                width: 330px;
                padding-left: 12px;
                .tag {
                    width: 60px;
                    height: 20px;
                    margin: 0 10px 7px 0;
                    font-size: 12px;
                    line-height: 20px;
                    text-align: center;
                    color: #fff;
                    background-color: rgba(255, 195, 0, 1);
                }
            }
            .price {
                width: 157px;
                text-align: center;
            }
            .operate {
                width: 158px;
                font-size: 10px;
                color: rgba(102, 102, 102, 1);
                margin-left: 80px;
                div {margin-bottom: 10px;cursor: pointer;}
            }
        }
    }
}
.bottom-bar {
    height: 60px;
    padding-left: 20px;
    border: 1px solid rgba(230, 230, 230, 1);
    &>div{height: 100%;}
    /deep/ .el-checkbox {
        margin-right: 30px;
    }
    .bar-left {
        color: rgba(102, 102, 102, 1);
        span {margin-right: 20px;cursor: pointer;}
    }
    .bar-right {
        &>div:first-child {
            margin: 7px 9px 0 0;
            color: rgba(153, 153, 153, 1);
            cursor: pointer;
            span {margin-right: 9px;}
        }
        .bar-right-price {
            height: 27px;
            margin: 4px 22px 0 0;
            & span:first-child {color: rgba(153, 153, 153, 1);}
            & span:nth-child(2) {
                margin-right: 12px;
                font-size: 18px;
                font-weight: 700;
                color: rgba(212, 48, 48, 1);
            }
            img {
                width: 16px;
                height: 16px;
            }
        }
        button {
            padding: 0 14px;
            height: 60px;
            font-size: 24px;
            font-weight: 400;
            text-align: center;
            line-height: 60px;
            color: rgba(255, 255, 255, 1);
            background-color: rgba(212, 48, 48, 1);
        }
    }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    appearance: none !important;
}
::v-deep input[type='number'] {
    -moz-appearance: textfield !important;
    appearance: textfield !important;
}
//该商品选择框
.checkbox{
  position: relative;
  top: -70px;
  left: 20px;
}
//商品图片
.goodsImg{
  cursor: pointer;
  border: 1px solid red;
  width: 130px;
  height: 130px;
  margin-left: 50px;
  margin-right: 20px;
}
//商品详情
.title-box{
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  margin-right: 30px;
  width: 216px;
}
//该商品单价
.price-container{
  margin-left: 10px;
}
.price-value{
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}
.price-label{
  margin-bottom: 10px;
  font-weight: 200
}
//该商品税率
.price{
  margin-left: -50px;
  margin-right: 40px;
  width: 80px;
  text-align: center;
  margin-top: -80px;
}
//该商品账期
.payment-period-select{
  font-weight: 600;
  width: 110px;
  text-align: center;
  margin-right: 40px;
  margin-top: -80px;
  .zqSelect {width: 110px;}
}
//该商品数量
.num-box{
  width: 100px;
  height: 35px;
  line-height: 35px;
  text-align: center;
//   border: 1px solid black;
  margin-right: 40px;
  margin-top: -80px;
}
.num-box-btn{
  cursor: pointer;
  width: 34px;
  height: 34px;
  background-color: rgb(229,229,227);
  border-bottom: 1px solid black;
}
.num-box-input{
  text-align: center;
  width: 60px;
  height: 34px;
  border-left: 1px solid black;
  border-right: 1px solid black;
  border-top: none;
  border-bottom: 1px solid black;
}
//该商品小计
.price-group{
  margin-top: -60px;
  margin-left: 20px;
  margin-right: 100px;
}
//该商品操作
.operate{
  margin-top: -60px;
}
.operate-btn{
  font-weight: 546;
  margin-bottom: 20px;
  cursor: pointer;
}
.operate-btn:hover {
  color: red;
}
.allPrice{
  //border: 3px solid red;
  width: 800px;
  display: flex;
  justify-content: space-between;
}
.allPrice span{
  color: red;
}
.allPriceBtn1{
  background-color: rgb(255,243,238);
  border: 1px solid #fbd4cd;
  color: rgb(255,50,17);
  font-size: 25px;
  width: 200px;
  height: 60px;
  margin-left: 20px;
}
.allPriceBtn2{
  background-color: rgb(255,50,17);
  color: white;
  font-size: 25px;
  width: 220px;
  height: 60px;
  margin-left: 20px;
}
.product-name{
  max-width: 216px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sku-name{
  margin-top: 4px;
  max-width: 215px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-d {

  /deep/ .el-dialog {
    padding: 0;

    .el-dialog__body {
      height: unset;
      margin-top: 0;
    }

    .el-dialog__header {
      padding: 20px 20px 10px;
      margin-bottom: unset;
      text-align: unset;
      font-weight: unset;

      .el-dialog__title {
        color: #303133;
      }
    }
  }
}
.uploadDiv /deep/ .el-loading-spinner {background-position: center;transform: translate(-50%, -50%);}

/deep/ .el-dialog {
    width: 1000px;
    //width: 1120px;
    min-height: 326px;
    .dialog-body {
        width: 500px;
        padding-top: 30px;

        .el-form-item {
            margin-bottom: 14px;
            &:last-of-type {margin-bottom: 20px;}
        }

        .el-form-item__label {
            // height: 14px;
            // margin-bottom: 20px;
            padding-bottom: 0;
            color: #999;
        }

        .el-input__inner {
            width: 300px;
            height: 35px;
            border: 1px solid rgba(217, 217, 217, 1);
            border-radius: 0;
        }
        .address .el-input__inner {width: 500px;}
        .tel {
            .el-form-item__content {
                display: flex;
                span {
                    margin-right: 10px;
                    color: #333;
                }
            }
            .el-input, .el-input__inner {width: 266px;}
        }
    }
    .butSub {
        width: 80px;
        height: 40px;
        font-size: 16px;
        color: #fff;
        background-color: #216EC6;
        margin-left: 100px;
    }
    .el-table {
        margin-top: 20px;
        border: 1px solid rgba(230, 230, 230, 1);
        // border-bottom: 0;
        font-size: 14px;
        .edit-btn {
            color: rgba(34, 111, 199, 1);
            cursor: pointer;
        }
    }
    .add {
        width: 80px;
        height: 30px;
        margin: 18px 0 40px 0;
        line-height: 30px;
        text-align: center;
        color: rgba(33, 110, 198, 1);
        border: 1px solid rgba(33, 110, 198, 1);
    }
    .el-table__header {
        .cell{
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
        }
        .el-checkbox {display: none;}
    }
    .el-dialog__footer {
        border: 0;
        text-align: center;
        button {
            min-width: 80px;
            padding: 0 10px;
            height: 40px;
        }
        & button:first-child {
            margin-right: 20px;
            color: rgba(128, 128, 128, 1);
            background-color: rgba(230, 230, 230, 1);
        }
        & button:last-child {
            color: #fff;
            background-color: rgba(33, 110, 198, 1);
        }
    }
}
/deep/ .el-input-number--small .el-input__inner {padding: 0 8px;}
/deep/ .el-input-number--small .el-input-number__decrease,
/deep/ .el-input-number--small .el-input-number__increase {width: 20px;}
</style>
