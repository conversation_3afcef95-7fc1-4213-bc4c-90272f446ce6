import service from '@/utils/request'

const { httpPost, httpGet } = service

// 获取物资
const getMaterialPageList = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/listMaterialPage',
        params
    })
}
const putawayProductExport = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/putawayProductExport',
        params,
        responseType: 'blob',
    })
}

const listMaterialSupplier = params => {
    return httpPost({
        url: '/materialMall/supplier/product/listMaterialSupplier',
        params
    })
}

const listMaterialSupplierExport = params => {
    return httpPost({
        url: '/materialMall/supplier/product/listMaterialSupplierExport',
        params,
        responseType: 'blob',
    })
}

const listMaterialSupplierAffirm = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/listMaterialSupplierAffirm',
        params
    })
}
// 修改状态
const updateProductState = params => {
    return httpPost({
        url: '/materialMall/product/updateProductState',
        params
    })
}

const updateProductStateAndMarkUp = params => {
    return httpPost({
        url: '/materialMall/product/updateProductStateAndMarkUp',
        params
    })
}

// 批量提交
const updateProductSupplierSubState = params => {
    return httpPost({
        url: '/materialMall/supplier/product/updateProductSupplierSubState',
        params
    })
}

// 根据条件全部确认
const batchAffirmSupplierProduct = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/batchAffirmSupplierProduct',
        params
    })
}

// 创建
const createMaterial = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/createMaterial',
        params
    })
}
// 创建供应商商品
const createMaterialSupplier = params => {
    return httpPost({
        url: '/materialMall/supplier/product/createMaterialSupplier',
        params
    })
}

// 提交供应商商品
const onSubmitMaterialSupplier = params => {
    return httpPost({
        url: '/materialMall/supplier/product/onSubmitMaterialSupplier',
        params
    })
}
// 回显
const getMaterialInfo = params => {
    return httpPost({
        url: '/materialMall/product/getMaterialInfo',
        params
    })
}
const getShelfLogInfo = params => {
    return httpPost({
        url: '/materialMall/productShelfLog/listPage',
        params
    })
}
const removeShelfLogInfo = id => {
    return httpGet({
        url: '/materialMall/productShelfLog/' + id,
    })
}
const exportShelfLog = params => {
    return httpPost({
        url: '/materialMall/productShelfLog/OutputExcel',
        params,
        responseType: 'blob'
    })
}
// 获取供应商物资
const getMaterialInfoSupplier = params => {
    return httpPost({
        url: '/materialMall/supplier/product/getMaterialInfoSupplier',
        params
    })
}
// 修改
const updateMaterial = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/updateMaterial',
        params
    })
}
const updateMaterialSupplier = params => {
    return httpPost({
        url: '/materialMall/supplier/product/updateMaterialSupplier',
        params
    })
}
// 获取店铺关联关系
const listShopListBySupplierId = params => {
    return httpPost({
        url: '/materialMall/supplier/shopSupplierRele/listShopListBySupplierId',
        params
    })
}
// 获取店铺
const listShopList = params => {
    return httpPost({
        url: '/materialMall/supplier/shopSupplierRele/listShopList',
        params
    })
}

// 批量修改（单表）
const updateBatch = params => {
    return httpPost({
        url: '/materialMall/product/updateBatch',
        params
    })
}

// 批量导入
const importBatchMaterial = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/importBatchMaterial',
        params
    })
}
// 批量删除
const batchDel = params => {
    return httpPost({
        url: '/materialMall/shopManage/product/deleteBatch',
        params
    })
}
// 下载模板
const excelTemplate = params => {
    return httpGet({
        url: '/materialMall/product/excel/template',
        params,
        responseType: 'blob'
    })
}

const excelZoneTemplate = params => {
    return httpPost({
        url: '/materialMall/product/excel/zoneTemplate',
        params,
        responseType: 'blob'
    })
}

const supplierExcelTemplate = params => {
    return httpGet({
        url: '/materialMall/supplier/product/supplierExcelTemplate',
        params,
        responseType: 'blob'
    })
}
// 导入商品
const uploadExcelFile = params => {
    return httpPost({
        url: '/materialMall/product/uploadExcelFile',
        params,
    })
}

const supplierUploadExcelFile = params => {
    return httpPost({
        url: '/materialMall/supplier/product/supplierUploadExcelFile',
        params,
    })
}
const shopUploadProductMallExcelFile = params => {
    return httpPost({
        url: '/materialMall/supplier/product/shopUploadProductMallExcelFile',
        params,
    })
}

//商品销售区域导入
const shopUploadProductMallZoneExcelFile = params => {
    return httpPost({
        url: '/materialMall/supplier/product/shopUploadProductMallZoneExcelFile',
        params,
    })
}
//二级商品销售区域导入
const shopUploadProductMallZoneSupplierExcelFile = params => {
    return httpPost({
        url: '/materialMall/supplier/product/shopUploadProductMallZoneSupplierExcelFile',
        params,
    })
}
// 临购商品导入
const shopUploadLcProductMallExcelFile = params => {
    return httpPost({
        url: '/materialMall/supplier/product/shopUploadLcProductMallExcelFile',
        params,
    })
}
const supplierLcExcelTemplate = params => {
    return httpGet({
        url: '/materialMall/supplier/product/supplierLcExcelTemplate',
        params,
        responseType: 'blob'
    })
}
const supplierUploadLcExcelFile = params => {
    return httpPost({
        url: '/materialMall/supplier/product/supplierUploadLcExcelFile',
        params,
    })
}
export {
    shopUploadProductMallExcelFile,
    getMaterialPageList,
    updateProductState,
    createMaterial,
    getMaterialInfo,
    updateMaterial,
    updateBatch,
    importBatchMaterial,
    batchDel,
    excelTemplate,
    uploadExcelFile,
    listMaterialSupplier,
    listShopListBySupplierId,
    listShopList,
    updateMaterialSupplier,
    updateProductSupplierSubState,
    createMaterialSupplier,
    onSubmitMaterialSupplier,
    getMaterialInfoSupplier,
    listMaterialSupplierAffirm,
    batchAffirmSupplierProduct,
    supplierExcelTemplate,
    supplierUploadExcelFile,
    shopUploadLcProductMallExcelFile,
    supplierLcExcelTemplate,
    supplierUploadLcExcelFile,
    putawayProductExport,
    excelZoneTemplate,
    shopUploadProductMallZoneExcelFile,
    shopUploadProductMallZoneSupplierExcelFile,
    listMaterialSupplierExport,
    getShelfLogInfo,
    removeShelfLogInfo,
    exportShelfLog,
    updateProductStateAndMarkUp

}