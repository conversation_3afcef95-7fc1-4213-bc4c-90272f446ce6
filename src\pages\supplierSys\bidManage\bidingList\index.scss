/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.base-page .left {
  min-width: 200px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .el-dialog {
  .el-dialog__body {
    //height: 380px;
    margin-top: 0px;
  }
}

.e-table {
  min-height: auto;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-dialog__body {
  margin-top: 0;
}

/deep/ #bidingDialog {
  .el-dialog__body {
    height: 680px;
    margin-top: 0px;
  }
}
/deep/ #inventoryBidingDialog {
  .el-dialog__body {
    height: 680px;
    margin-top: 0px;
  }
}
/deep/ .el-dialog.dlg {
  height: 600px;

  .el-dialog__header {
    margin-bottom: 0;
  }

  .el-dialog__body {
    height: 474px;
    margin: 10px;
    display: flex;

    & > div {
      .e-pagination {
        background-color: unset;
      }

      //height: 670px;
      .title {
        height: 22px;
        margin-bottom: 10px;
        padding-left: 26px;
        text-align: left;
        line-height: 22px;
        color: #2e61d7;
        font-weight: bold;
        position: relative;
        display: flex;

        &::before {
          content: '';
          display: block;
          width: 10px;
          height: inherit;
          border-radius: 5px;
          background-color: blue;
          position: absolute;
          left: 10px;
          top: 0;
        }
      }
    }

    .el-input__inner {
      border: 1px solid blue;
      border-radius: 6px;
    }

    .el-input__suffix {
      width: 20px;
    }

    .e-table {
      flex-grow: 1;

      .table {
        height: 100%;
      }
    }

    .box-left {
      width: 260px;
      display: flex;
      flex-direction: column;

      .search {
        padding: 0 10px;
      }
    }

    .box-right {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      & > div {
        display: flex;
        flex-direction: column;
      }

      .top {
        justify-content: left;
        height: 374px;
        margin: 0;
        border-radius: 0;
        box-shadow: unset;
      }

      .bottom {
        flex-grow: 1;
      }
    }
  }

  .el-dialog__footer {
    background-color: #eff2f6;
  }
}

.search_box1 {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  align-items: center;
  gap: 10px;
  background: #fff;
  height: 55px;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/deep/ #supplierDialog2 {
  .el-dialog__body {
    height: 600px;
    margin-top: 0px;
  }
}
/deep/ .el-button {
  padding: 0 0px;
}