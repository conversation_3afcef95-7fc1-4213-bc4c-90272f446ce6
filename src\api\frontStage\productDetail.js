import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

//未登录获取商品详情
const getMaterialInfo = params => {
    return httpGet({
        url: '/materialMall/w/product/materialInfo',
        params,
    })
}

//登陆后获取商品详情
const getLogInMaterialInfo = params => {
    return httpGet({
        url: '/materialMall/product/materialInfo',
        params,
    })
}
const addCart = params => {
    return httpGet({
        url: '/materialMall/userCenter/shoppingCart/addCart',
        params
    })
}

const oneClickRepurchase = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/oneClickRepurchase',
        params
    })
}

const addCartZone = params => {
    return httpPost({
        url: '/materialMall/userCenter/shoppingCart/addCartZone',
        params
    })
}
export {
    getMaterialInfo,
    getLogInMaterialInfo,
    addCart,
    addCartZone,
    oneClickRepurchase
}