<template>    <!-- 自营运维人员  待确认周材商品 -->
    <div class="base-page" v-loading="showLoading">
        <div class="left" :style="{ height: '100%' }">
            <select-material-class ref="materialClassRef" :productType = "0"  :is-lc="3"/>
        </div>
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left" style="width: 800px">
                        <div class="left-btn">
                            <el-button type="primary"  class="btn-greenYellow" @click="updateBatchState(3,'确认')">批量确认</el-button>
                            <el-button type="primary"  class="btn-greenYellow" @click="batchAffirmSupplierProductM(3,'全部确认')">全部确认</el-button>
                            <el-button type="primary"  class="btn-delete" @click="updateBatchState(4,'拒绝')">批量拒绝</el-button>
                            <el-button type="primary"  class="btn-delete" @click="batchAffirmSupplierProductM(4,'全部拒绝')">全部拒绝</el-button>
                            <el-button type="primary"  class="btn-greenYellow" @click="changeSortValue()">批量修改排序</el-button>
                        </div>
                    </div>
                  <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="1" :style="{ color: init.orderBy == 1 ? '#2e61d7' : '' }">
                        排序值
                      </el-dropdown-item>
                      <el-dropdown-item :command="2" :style="{ color: init.orderBy == 2 ? '#2e61d7' : '' }">
                        创建时间
                      </el-dropdown-item>
                      <el-dropdown-item :command="3" :style="{ color: init.orderBy == 3 ? '#2e61d7' : '' }">
                        修改时间
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                    <div class="search_box" style="width: 400px">
                        <el-input clearable type="text" @blur="getTableData" placeholder="输入搜索关键字" v-model="init.keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="getTableData"  alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table @row-click="handleCurrentInventoryClick2" ref="eltableCurrentRow2" v-loading="tableLoading" class="table" :height="rightTableHeight" :data="tableData"  border
                          @selection-change="selectionChangeHandle">
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="130">
                        <template v-slot="scope" >
                            <el-button style="padding:0 8px;" v-if="scope.row.supplierSubmitState==2"
                                       size="mini"
                                       type="danger"
                                       @click="updateState(scope.row,4,'拒绝')">拒绝
                            </el-button>
                            <el-button style="padding:0 8px;" v-if="scope.row.supplierSubmitState==2"
                                       size="mini"
                                       type="success"
                                       @click="updateState(scope.row,3,'确认')">确认</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="名称" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{scope.row.relevanceName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="物资分类" width="240" prop="classPath"/>
                    <el-table-column label="编号" width="240">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.serialNum }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="图片" width="120" type="index">
                        <template v-slot="scope">
                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg" fit="fill"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column label="供应商来源" width="200" prop="supplierName"></el-table-column>
                    <!-- <el-table-column label="销售价格" width="" prop="sellPrice"></el-table-column>
                  <el-table-column label="原价" width="" prop="originalPrice" />
                  <el-table-column label="成本价" width="" prop="costPrice" />
                                     <el-table-column label="差价" width="" prop="profitPrice" /> -->
                  <el-table-column label="库存" width="" prop="stock" />
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input clearable v-model="scope.row.shopSort" @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <!--                   单位换算-->
                    <el-table-column label="材质" width="200" prop="productTexture"/>
<!--                    <el-table-column label="单位换算" width="200" prop="secondUnit"/>-->

                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                    <el-table-column label="修改时间" width="160" prop="gmtModified" />
                </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-show="tableData || tableData.length !== 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--高级查询-->
        <el-dialog v-dialogDrag  title="高级查询" :visible.sync="queryVisible" width="40%" :close-on-click-modal="true">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="商品名称：" >
                            <el-input clearable maxlength="100" placeholder="请输入商品名称" v-model="filterData.productName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="商品编码：" >
                            <el-input clearable maxlength="100" placeholder="请输入商品编码" v-model="filterData.serialNum"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="供应商名称：" >
                            <el-input clearable maxlength="100" placeholder="请输入供应商" v-model="filterData.supplierName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11">
                        <el-form-item label="价格以上：">
                            <el-input clearable v-model="filterData.abovePrice" placeholder="请输入价格区间"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="价格以下：">
                            <el-input clearable type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="创建时间：">
                            <el-date-picker style="width: 100%;"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.createDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22" :offset="0">
                        <el-form-item label="修改时间：">
                            <el-date-picker style="width: 100%;"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.modifiedDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <el-row>
                    <el-col :span="24" :offset="0">
                        <el-form-item label="排序：">
                            <el-radio v-model="init.orderBy" :label="1">按排序值排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="2">按创建时间排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="3">按修改时间排序</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row> -->
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click = "expertSearch">确定</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>
        <!--excel导入返回弹窗-->
        <el-dialog v-dialogDrag   title="导入结果" :visible.sync="showImportExcelLoading"  width="70%" :close-on-click-modal="true">
            <div class="e-table"  style="background-color: #fff">
                <el-table
                    border
                    style="width: 100%"
                    :data="excelResult"
                    class="table"
                    :max-height="$store.state.tableHeight"
                    ref="eltableCurrentRow"
                >
                    <el-table-column prop="id" label="序号"  width="60"></el-table-column>
                    <el-table-column prop="productName" label="商品名称" width=""></el-table-column>
                    <el-table-column prop="state" label="状态" width="80">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==1" type="success">成功</el-tag>
                            <el-tag v-if="scope.row.state==0" type="danger">失败</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="fail" label="失败原因" width=""></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <el-button  @click="showImportExcelLoading = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { mapState, mapActions, mapMutations } from 'vuex'
import {
    updateProductSupplierSubState,
    updateBatch,
    batchDel,
    excelTemplate, listMaterialSupplierAffirm,
    batchAffirmSupplierProduct
} from '@/api/shopManage/product/materialManage'
export default {
    components: {
        SelectMaterialClass, Pagination
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            currentQuery: null,
            showLoading: false,
            showImportExcelLoading: false,
            deviceInventoryLoading: false,
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                supplierSubmitStates: [2],
                inventory: {
                    state: 1,
                    productType: 2,
                },
                // state: [0, 2],
                productType: 2,
                orderBy: 2,
                classId: null,
                keywords: null,
                classPath: [],
            },
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                supplierName: null,
                serialNum: null,
                productName: null,
                stateCheckAll: false, // 选择全局
                state: [], // 状态
                isOneself: null,
                belowPrice: null,
                abovePrice: null,
                createDate: [], // 创建时间
                modifiedDate: [], // 修改时间
                stateOptions: [ {
                    value: 0,
                    label: '待上架'
                }, {
                    value: 1,
                    label: '已上架'
                }, {
                    value: 2,
                    label: '已下架'
                }],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            excelResult: []
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
    },
    methods: {
        showExcelResult (result) {
            this.excelResult = result
            this.showImportExcelLoading = true
        },
        onDownload () {
            excelTemplate({ productType: this.init.inventory.productType }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                a.href = url
                a.download = '临购物资模板.xlsx'
                a.click()
                window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
            })
        },
        handleChangeSort (value) {
            this.init.orderBy = value
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 选择
        selectDeviceRow (value) {
            this.inventory.selectRow = value
        },
        // 批量修改排序
        changeSortValue () {
            if(this.changedRow.length === 0) {
                return this.$message('未修改列表当中的排序值！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                updateBatch(this.changedRow).then(res => {
                    this.message(res)
                    this.changedRow = []
                    this.getTableData()
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if(row.shopSort <= 0 ) {
                row.shopSort = 0
            }
            if(this.changedRow.length === 0) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if(t.productId === row.productId) {
                    t.shopSort = row.shopSort
                    flag = true
                }
            })
            if(!flag) {
                this.changedRow.push({ productId: row.productId, shopSort: row.shopSort })
            }
            flag = true
        },
        skipView (data) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/product/turnMaterialSupplierAffirmDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'turnMaterialSupplierAffirmDetail',
                params: {
                    row: data
                }
            })
        },
        // 物资详情
        handleView (row) {
            row.classPath = this.classPath
            this.skipView(row)
        },
        // 高级搜索确认
        expertSearch () {
            this.init.keywords = null
            this.getTableData()
            this.filterData.productName = null
            this.filterData.serialNum = null
            this.filterData.supplierName = null
            this.filterData.createDate = []
            this.filterData.modifiedDate = []
            this.filterData.belowPrice = null
            this.filterData.abovePrice = null
            this.filterData.isOneself = null
            this.filterData.state = []
            this.filterData.stateCheckAll = false
            this.queryVisible = false
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        // 状态全选
        stateAllSelect (value) {
            if(value) {
                this.filterData.state = this.filterData.stateOptions.map(t => {
                    return t.value
                })
            }else {
                this.filterData.state = []
            }
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 批量删除
        batchDelete () {
            if(this.dataListSelections.length === 0) {
                return this.$message('请勾选要操作的数据！')
            }
            let ids = this.dataListSelections.map(item => {
                return item.productId
            })
            this.clientPop('info', '您确定要批量删除这些数据吗！', async () => {
                this.tableLoading = true
                batchDel(ids).then(res => {
                    this.message(res)
                    this.dataListSelections = []
                    this.getTableData()
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        batchAffirmSupplierProductM (state, title) {
            if(this.tableData.length == 0) {
                return this.$message.error('未找到数据！')
            }
            this.clientPop('info', '您确定要【' + title + '】操作吗？', async () => {
                if(state == 4) {
                    this.$prompt('拒绝原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入拒绝原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入拒绝原因'
                    }).then(({ value }) => {
                        let params = {
                            ...this.currentQuery,
                            updateState: state,
                            updateFailReason: value,
                        }
                        this.tableLoading = true
                        batchAffirmSupplierProduct(params).then(res => {
                            if(res.code == 200) {
                                this.$message.success(res.message)
                                this.getTableData()
                            }
                            this.tableLoading = false
                        })
                    }).catch(() => {
                        this.tableLoading = false
                    })
                }else {
                    let params = {
                        ...this.currentQuery,
                        updateState: state,
                    }
                    this.tableLoading = true
                    batchAffirmSupplierProduct(params).then(res => {
                        if(res.code == 200) {
                            this.$message.success(res.message)
                            this.dataListSelections = []
                            this.getTableData()
                        }
                        this.tableLoading = false
                    }).catch(() => {
                        this.tableLoading = false
                    })
                }
            })
        },
        updateBatchState (state, title) {
            if(this.dataListSelections.length === 0) {
                return this.$message('请勾选要操作的数据！')
            }
            let ids = this.dataListSelections.map(item => {
                return item.productId
            })
            this.clientPop('info', '您确定要批量【' + title + '】操作吗？', async () => {
                if(state == 4) {
                    this.$prompt('拒绝原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入拒绝原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入拒绝原因'
                    }).then(({ value }) => {
                        let params = {
                            productIds: ids,
                            state: state,
                            failReason: value,
                        }
                        this.tableLoading = true
                        updateProductSupplierSubState(params).then(res => {
                            if(res.code == 200) {
                                this.$message.success(res.message)
                                this.dataListSelections = []
                                this.getTableData()
                            }
                            this.tableLoading = false
                        })
                    }).catch(() => {
                        this.tableLoading = false
                    })
                }else {
                    let params = {
                        productIds: ids,
                        state: state
                    }
                    this.tableLoading = true
                    updateProductSupplierSubState(params).then(res => {
                        if(res.code == 200) {
                            this.$message.success(res.message)
                            this.dataListSelections = []
                            this.getTableData()
                        }
                        this.tableLoading = false
                    }).catch(() => {
                        this.tableLoading = false
                    })
                }
            })
        },
        // 单个上架
        updateState (row, state, title) {
            this.clientPop('info', '您确定要对【' + row.productName + '】进行【' + title + '】操作吗！', async () => {
                if(state == 4) {
                    this.$prompt('拒绝原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入拒绝原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入拒绝原因'
                    }).then(({ value }) => {
                        let params = {
                            productIds: [row.productId],
                            state: state,
                            failReason: value,
                        }
                        this.tableLoading = true
                        updateProductSupplierSubState(params).then(res => {
                            if(res.code == 200) {
                                this.$message.success(res.message)
                                this.getTableData()
                            }
                            this.tableLoading = false
                        })
                    }).catch(() => {
                        this.tableLoading = false
                    })
                }else {
                    let params = {
                        productIds: [row.productId],
                        state: state
                    }
                    this.tableLoading = true
                    updateProductSupplierSubState(params).then(res => {
                        if(res.code == 200) {
                            this.$message.success(res.message)
                            this.getTableData()
                        }
                        this.tableLoading = false
                    }).catch(() => {
                        this.tableLoading = false
                    })
                }
            })
        },
        // 单个删除
        deleteRow (row) {
            this.clientPop('info', '您确定要对物资【' + row.productName + '】进行【删除】操作吗？', async () => {
                batchDel([row.productId]).then(res => {
                    this.message(res)
                    this.getTableData()
                })
            })
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.getTableData()
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                // state: this.init.state,
                supplierSubmitStates: this.init.supplierSubmitStates,
                productType: this.init.productType,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if(this.filterData.serialNum != null) {
                params.serialNum = this.filterData.serialNum
            }
            if(this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if(this.filterData.state.length !== 0) {
                params.state = this.filterData.state
            }
            if(this.init.classId != null) {
                params.classId = this.init.classId
            }
            if(this.init.keywords != null) {

                params.keywords = this.init.keywords
            }
            if(this.filterData.modifiedDate != null) {
                params.startModifiedDate = this.filterData.modifiedDate[0],
                params.endModifiedDate = this.filterData.modifiedDate[1]
            }
            if(this.filterData.createDate != null) {
                params.startCreateDate = this.filterData.createDate[0],
                params.endCreateDate = this.filterData.createDate[1]
            }
            if(this.init.orderBy != null) {
                params.orderBy = this.init.orderBy
            }
            if(this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if(this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if(this.filterData.isOneself != null) {
                params.isOneself = this.filterData.isOneself
            }
            // 记录每次查询
            this.currentQuery = params
            this.tableLoading = true
            listMaterialSupplierAffirm(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if(res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
.el-dialog__body{
    margin: 220px;
}
.base-page .left {
  min-width: 200px;
  height: 100%;
  padding: 0;
  overflow: auto;
}
.base-page {
    width: 100%;
    height: 100%;
}
/deep/ .el-dropdown {
  min-width: 75px;
  margin-right: 20px;
}
/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;
    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

//.e-form {
//    padding: 0 20px;
//
//    .tabs-title::before {
//        content: '';
//        height: 22px;
//        width: 8px;
//        border-radius: 40px;
//        background-color: #2e61d7;
//        display: block;
//        position: absolute;
//        left: 20px;
//        margin-right: 20px;
//    }
//}

.e-table {min-height: auto;}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog.dlg {
    height: 800px;
    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 674px;
        margin: 10px;
        display: flex;

        &>div {
            .e-pagination {
                background-color: unset;
            }
            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }
        .e-table{
            flex-grow: 1;
            .table{
                height: 100%;
            }
        }

        .box-left {
            width: 370px;
            display: flex;
            flex-direction: column;
            .search {
                padding: 0 10px;
            }
        }

        .box-right {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            &>div{
                display: flex;
                flex-direction: column;
            }
            .top {
                height: 374px;
                margin: 0;
                border-radius: 0;
                box-shadow: unset;
            }
            .bottom{
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0px;
    }
}
</style>
