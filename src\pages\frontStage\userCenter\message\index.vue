<template>
    <main>
        <div class="msgBox">
            <div class="list-title dfa">站内消息</div>
            <div v-show="viewShow!==true" class="tableBox">
                <el-table
                    ref="msgTable"
                    :data="msgList"
                    :header-row-style="{ fontSize: '16px', color: '#216EC6', fontWeight: '500' }"
                    :row-style="{ fontSize: '14px', color: '#666666', height: '48px' }"
                    @selection-change="selectionChange"
                    @row-click="handleCurrentInventoryClick"
                >
                    <el-table-column type="selection" width="54px">
                    </el-table-column>
                    <el-table-column prop="sendDate" label="发送时间" width="190px">
                    </el-table-column>
                    <el-table-column prop="sendName" label="发信人" width="120px">
                    </el-table-column>
                    <el-table-column prop="title" label="主题" width="223px">
                        <template v-slot="scope">
                            <div class="textOverflow1">{{ scope.row.title }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="count" label="内容摘要" width="223px">
                        <template v-slot="scope">
                            <div class="textOverflow2" style="max-height: 43px;">
                                {{ stripHtmlTags(scope.row.content) }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="90px">
                        <template v-slot="scope">
                            <img
                                :src="scope.row.isRead == 1 ? require('@/assets/images/userCenter/mail2.png') : require('@/assets/images/userCenter/mail1.png')"
                                alt="">
                            <el-link v-show="scope.row.isRead == 1" type="primary">已读</el-link>
                            <el-link v-show="scope.row.isRead == 0" type="info">未读</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100px">
                        <template v-slot="scope">
                            <div class="action">
                                <span @click="handleDeleteMsg(scope.row.stationMessageReceiveId)">删除</span>
                                <span @click="handleViewMsg(scope.row)">查看</span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pager dfb">
                    <div class="batchAction dfa">
                        <el-checkbox v-model="checkAll" label="全选" :indeterminate="false" @change="toggleSelectAll"></el-checkbox>
                        <div class="btn del" @click="deleteSelected">删除选中</div>
                        <div class="btn mark" @click="markSelected">标记已读</div>
                    </div>
                    <pagination
                        :total="page.totalCount"
                        :currentPage="page.currPage"
                        :pageSize="page.pageSize"
                        :totalPage="page.totalPage"
                        :destination="page.destination"
                        :pagerSize="5"
                        @currentChange="currentChange"
                        @sizeChange="sizeChange"
                    ></pagination>
                </div>
            </div>

            <div class="right" v-show="viewShow===true">
                <!-- ---------------------消息查看窗口--------------------- -->
                <div class="e-form" style="padding: 0 10px 10px;">
                    <div class="tabs-title">我的消息</div>
                    <el-form ref="formEdit" :model="formData" label-width="150px">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="发件人名称：" prop="sendName">
                                    <span>{{ formData.sendName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="时间：" prop="sendDate">
                                    <span>{{ formData.sendDate }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="标题：" prop="title">
                                    <span>{{ formData.title }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col class="editorCol" :span="24">
                                <el-form-item label="内容：" prop="content">
                                    <div v-html="formData.content"></div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <!--<el_row>-->
                        <!--<el-row >-->
                        <!--<el-col :span="24">-->
                        <!--<div  @click="updateMessageState(formData.stationMessageReceiveId)">确定</div>-->
                        <!--</el-col>-->
                        <!--</el-row>-->
                        <!--</el_row>-->
                    </el-form>
                </div>
                <div class="p20 e-form"  style="overflow: auto;"  >
                    <div class="tabs-title mb10">附件资料</div>
                    <el-table border :data="fileList" :header-cell-style="{ background: '#f7f7f7' }" v-loading="fileLoading">
                        <el-table-column label="序号" type="index" width="120"></el-table-column>
                        <el-table-column prop="name" label="附件名称" width=""></el-table-column>
                        <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
                                <el-button type="primary" @click="handleDownload(scope.row)">下载</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="theBtn pointer" v-show="formData.isRead==0"
                     @click="updateMessageState(formData.stationMessageReceiveId)">确定
                </div>
                <div class="theBtn2 pointer" v-show="formData.isRead==1" @click="viewShow=false">返回</div>

                <!--  <el-button native-type="button" type="primary" @click="updateupdateMessageState(formData.stationMessageReceiveId)">确定</el-button>-->
                <!--</div>-->
            </div>
        </div>
    </main>
</template>
<script>
import pagination from '../../components/pagination.vue'
import { stripHtmlTags } from '@/utils/common'
import { receiceList, updateReadStateBath, del, updateState, delMessageBath } from '@/api/frontStage/messages'
import { previewFile } from '@/api/platform/common/file'
import { selectFileList } from '@/api/base/file'

export default {
    components: { pagination },
    data () {
        return {
            fileLoading: false,
            fileList: [],
            arr: [],
            selectList: [],
            checkAll: false,
            page: {
                totalPage: 0,
                totalCount: 0,
                currPage: 1,
                pageSize: 10,
                destination: 2
            },
            formData: {},
            msgList: [],
            viewShow: false
        }
    },
    created () {
        window.msgViewBillInfo = this.msgViewBillInfo
        this.getList()
    },
    methods: {
        stripHtmlTags,
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: 7,
            }
            selectFileList(params).then(res=>{
                this.fileList = res.list
            })
        },
        async handleDownload (file) {
            this.fileLoading = true
            let fileRes = await previewFile({ recordId: file.fileFarId })
            const blob = new Blob([fileRes])
            const url = window.URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
            this.fileLoading = false
        },
        message (res) {
            let msgType = res.code === 200 ? 'success' : 'error'
            this.$message({
                message: res.message,
                type: msgType
            })
        },
        // 标记已读
        updateMessageState (stationMessageReceiveId) {
            updateState({ id: stationMessageReceiveId }).then(res => {
                this.viewShow = false
                this.$message({
                    message: res.message,
                    type: 'success'
                })
                this.getList()
            })
        },
        getList () {
            let params = {
                limit: this.page.pageSize,
                page: this.page.currPage,
                receiveType: 3
            }
            receiceList(params).then(res => {
                this.msgList = res.list
                this.page = res
                this.page.totalPage = Math.ceil(this.page.totalCount / this.page.pageSize)
            })
        },
        msgViewBillInfo ( id, type ) {
            if (type == 1) {
                this.$router.push({
                    path: '/performance/floatDetailInfo',
                    name: 'floatDetailInfo',
                    query: {
                        sn: id
                    }
                })
            }
            if (type == 2) {
                this.$router.push({
                    path: '/performance/fixationDetailInfo',
                    name: 'fixationDetailInfo',
                    query: {
                        sn: id
                    }
                })
            }
        },
        // 删除
        handleDeleteMsg (id) {
            del({ id: id }).then(res => {
                this.message(res)
                this.getList()
            })
        },
        // 查看
        handleViewMsg (row) {
            this.viewShow = true
            this.formData = row
            this.getFileInfos(this.formData.stationMessageId)
        },
        // 全选事件
        toggleSelectAll () {
            if (!this.checkAll) {
                return this.$refs['msgTable'].clearSelection()
            }
            this.$refs['msgTable'].toggleAllSelection()
        },
        // 选中项更改事件
        selectionChange (selection) {
            this.arr = selection
            if (selection.length == this.page.pageSize) {
                return this.checkAll = true
            }
            if (this.checkAll) this.checkAll = false
        },
        // 删除选中
        deleteSelected () {
            if (this.arr == null || this.arr == 0) {
                this.$message({
                    message: '未选中数据',
                    type: 'info'
                })
                return
            }
            this.selectList = this.arr.map(item => {
                return item.stationMessageReceiveId
            })
            delMessageBath(this.selectList).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.getList()
            })
        },

        // 标记已读
        markSelected () {
            if (this.arr == null || this.arr == 0) {
                this.$message({
                    message: '未选中数据',
                    type: 'info'
                })
                return
            }
            this.selectList = this.arr.map(item => item.stationMessageReceiveId)
            updateReadStateBath(this.selectList).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: res.message,
                        type: 'success'
                    })
                }
                this.getList()
            })
        },
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.msgTable.toggleRowSelection(row, row.flag)
        },
        // 页码变更
        currentChange (currPage) {
            this.page.currentPage = currPage
            this.getList()
        },
        sizeChange (pageSize) {
            this.page.pageSize = pageSize
            this.getList()
        },

    },
}
</script>
<style scoped lang="scss">
.msgBox {
    //height: 712px;
    border: 1px solid #e6e6e6;
}

.theBtn2 {
    width: 120px;
    height: 40px;
    margin: 20px auto 20px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    background-color: #93b7e0;
}

.theBtn {
    width: 120px;
    height: 40px;
    margin: 20px auto 20px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    background-color: #216EC6;
}

.list-title {
    height: 50px;
    padding: 15px 0 15px 20px;
    font-size: 18px;
    line-height: 20px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);

    &::before {
        width: 3px;
        height: 20px;
        margin-right: 10px;
        content: '';
        display: block;
        background-color: rgba(33, 110, 198, 1);
    }
}
/deep/.el-form-item {
    margin-bottom: 0;
}
.tableBox {
    padding: 22px 30px 0;

    .taberHeader {
        font-size: 16px;
        color: #216EC6;
    }

    .action span {
        color: #216EC6;
        cursor: pointer;
        user-select: none;

        &:first-of-type {
            margin-right: 15px;
        }
    }

    /deep/ .el-table {
        .el-table__body-wrapper {
            min-height: 600px;
        }
        .el-table__body .cell {
            display: flex;
            align-items: center;

            img {
                width: 13px;
                height: 12px;
                margin-right: 5px;
            }
        }

        thead {
            .el-checkbox {
                display: none;
            }

            tr th:nth-child(7) .cell {
                text-align: center;
            }
        }

        .el-table__row {
            td:first-child .cell {
                display: block;
                text-align: center;
            }
        }
    }

    .batchAction {
        padding-left: 20px;

        /deep/ .el-checkbox {
            margin-right: 20px;
        }

        .btn {
            width: 80px;
            height: 30px;
            text-align: center;
            font-size: 14px;
            line-height: 30px;
            cursor: pointer;
            user-select: none;
        }

        .del {
            margin-right: 15px;
            color: #fff;
            background-color: #216EC6;
        }

        .mark {
            color: #216EC6;
            border: 1px solid #216EC6;
        }
    }
}

/deep/ .el-col.editorCol {
  height: unset;
  .el-form-item { align-items: flex-start; }
  .el-form-item__label {
    height: 100% !important;
  }
    .el-form-item__content {
        height: unset !important;
    }
}
</style>