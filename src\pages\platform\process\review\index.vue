<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right" v-show="viewList === true">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <div>
            <div class="left">
              <div class="left-btn">
               <el-button type="primary" @click="handleNew">新增流程</el-button>
              </div>
            </div>
          </div>
          <div class="search_box">
            <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img
                src="@/assets/search.png" slot="suffix" @click="onSearch"/></el-input>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" :style="{ width: '100%' }">
        <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                  @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column label="操作" width="160">
            <template v-slot="scope">
                <span class="action"
                      style="padding:0 8px;"
                      size="mini"
                      type=""
                      @click="handleView(scope.row)"
                >修改
                </span>
              <span class="actionDel"
                    style="padding:0 8px;"
                    size="mini"
                    type=""
                    @click="onDel(scope)"
              >删除
                </span>
            </template>
          </el-table-column>
          <!-- 名称 -->
          <el-table-column label="系统名称" width="">
            <template slot-scope="scope">
              <span>{{ scope.row.systemName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="流程名称" width="">
            <template slot-scope="scope">
                <span class="action" @click="handleView(scope.row)">
                    {{ scope.row.processName }}
                </span>
            </template>
          </el-table-column>
          <el-table-column label="备注" width="">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.remark }}
                </span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" width="">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.modifyName }}
                </span>
            </template>
          </el-table-column>

          <el-table-column label="修改时间" width="">
            <template slot-scope="scope">
                <span>
                    {{ scope.row.gmtModified }}
                </span>
            </template>
          </el-table-column>

        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                     :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange"/>
    </div>
    <div class="right" v-show="viewList !== true">
      <!-- ---------------------新增编辑窗口--------------------- -->
      <div class="e-form" style="padding: 0 10px 10px;" v-show="viewList === 'class'">
        <div class="tabs-title">新增</div>
        <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="流程名称：" prop="processName">
                <el-input v-model="formData.processName" placeholder="请输入流程名称" clearable/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="系统名称：" prop="systemNo">
                <el-select
                    @change="val => onSystemChange(val)"
                    clearable
                    v-model="formData.systemNo"
                    placeholder="请选择系统名称"
                    style="width:  100%;"
                >
                  <el-option v-for="item in systemOptions" :key="item.systemNo" :label="item.systemName" :value="item.systemNo"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col :span="16">
              <el-form-item label="备注：" prop="remark">
                <el-input type="textarea"
                          maxlength="120"
                          placeholder="请输入备注"
                          v-model="formData.remark" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="流程设置：">
              <div class="form-header" style="margin-bottom: 2px">
                <span style="width: 70px; margin-right: 10px; font-weight: bold;">节点</span>
                <span style="margin-left: 200px; width: 70px; margin-right: 10px; font-weight: bold;">角色</span>
                <span style="margin-left: 200px; width: 70px; margin-right: 10px; font-weight: bold;">人员</span>
              </div>
            </el-form-item>
          </el-row>
          <div
              style="margin-left: 20px"
              v-for="(item, index) in formData.processConfigDtlItemVOs"
              :key="index"
              class="form-row"
          >
            <el-row style="margin-left: 120px;width: 60%;margin-top: 5px">
              <el-col :span="6">
                <el-select
                    v-model="item.nodeNo"
                    @change="val => onNodeChange(val, item)"
                    :disabled="item.disabled"
                    placeholder="请选择节点"
                    style="width:  100%;"
                >
                  <el-option v-if="item.disabled" label="提交" value="0"/>
                  <el-option label="审核" value="1"/>
                  <el-option label="审定" value="2"/>
                </el-select>
              </el-col>
              <el-col :span="6" style="margin-left: 5px">
                <el-select
                    style="width:  100%;"
                    v-model="item.roleNo" placeholder="请选择角色" @change="val => onRoleChange(val, item)">
                  <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.name" :value="item.roleId"/>
                </el-select>
              </el-col>
<!--              <el-col :span="6" style="margin-left: 5px">-->
<!--                <el-select-->
<!--                      style="width:  100%;"-->
<!--                      v-model="item.userId" placeholder="请选择人员" @change="val => onUserChange(val, item)">-->
<!--                    <el-option v-for="item in userOptions" :key="item.userId" :label="item.userName"-->
<!--                               :value="item.userId"/>-->
<!--                  </el-select>-->
<!--              </el-col>-->
              <div style="margin-left: 5px;display: flex">
                <el-button
                    v-if="formData.processConfigDtlItemVOs.length > 1"
                    icon="el-icon-minus"
                    circle
                    class="myButtom"
                    @click="removeNode(index)"
                />
                <el-button
                    v-if="index === formData.processConfigDtlItemVOs.length - 1"
                    icon="el-icon-plus"
                    circle
                    class="myButtom"
                    @click="addNode"
                />
              </div>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="footer">
        <div class="right-btn">
          <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getList, update, create, del } from '@/api/platform/process/processConfig'
// eslint-disable-next-line no-unused-vars
import {
    debounce,
    showLoading,
    hideLoading,
} from '@/utils/common'
import { mapActions } from 'vuex'
import {
    getRoleList
} from '@/api/platform/system/sysUser'
export default {
    components: {
        ComPagination
    },
    watch: {},
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            alertname: '消息',
            queryVisible: false,
            action: '编辑',
            viewList: true, // 显示列表还是编辑界面: true 显示列表 false 显示编辑界面
            keywords: '',
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            tableData: [],
            formRules: {
                processName: [{ required: true, message: '请输入流程名称名称', trigger: 'blur' }],
                systemNo: [{ required: true, message: '请选择系统名称', trigger: 'change' }],
            },
            // 流程节点配置，可初始化默认节点
            formData: {
                processName: '',
                systemNo: '',
                processType: '',
                systemName: '',
                remark: '',
                processConfigDtlItemVOs: [
                    { nodeNo: '0', nodeName: '提交', roleNo: '', roleName: '', userId: '', userName: '', disabled: true },
                ]
            },
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},
            filterData: {
                name: '',
                orderBy: 3,
                type: 0,
            },
            nodeOptions: [
                { nodeNo: '0', nodeName: '提交', disabled: true },
                { nodeNo: '1', nodeName: '审核', disabled: false },
                { nodeNo: '2', nodeName: '审定', disabled: false },
            ],
            systemOptions: [
                { systemNo: '111', systemName: '后台管理平台' },
                { systemNo: '222', systemName: '供应商履约平台' },
                { systemNo: '333', systemName: '采购人履约平台' },
            ],
            roleOptions: [],
            // 用户
            userOptions: [
                { value: '全部', userName: '全部', userId: '全部' },
                { value: 'fa33e9c24fe311f0b7257c8ae1a484c8', userName: '王账册', userId: 'fa33e9c24fe311f0b7257c8ae1a484c8' },
                { value: '1d0e7d2c4ff811f0b7327c8ae1a484c8', userName: '李料仓', userId: '1d0e7d2c4ff811f0b7327c8ae1a484c8' },
                { value: '3d9e0cf84dbc11f087647c8ae1a484c8', userName: '陈店助', userId: '3d9e0cf84dbc11f087647c8ae1a484c8' },
                { value: '9b17aa7e4dbc11f087667c8ae1a484c8', userName: '赵卖手', userId: '9b17aa7e4dbc11f087667c8ae1a484c8' },
                { value: 'cc941ea44fe011f0b70a7c8ae1a484c8', userName: '钱算盘', userId: 'cc941ea44fe011f0b70a7c8ae1a484c8' },
                { value: 'dba26f7a4dbc11f087687c8ae1a484c8', userName: '张统筹', userId: 'dba26f7a4dbc11f087687c8ae1a484c8' }
            ],
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        let params = {
            limit: this.pages.pageSize,
            page: this.pages.currPage,
            orderBy: this.filterData.orderBy,
        }
        getList(params).then(res => {
            this.isLoading = false
            this.pages = res
            this.tableData = res.list
        })
        this.getRoleLists()
        this.getParams()
    },
    methods: {
        handleNew () {
            this.emptyForm()
            this.viewList = 'class'
            this.action = '新增'
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        // 删除
        onDel (scope) {
            this.clientPop('info', '您确定要删除该信息吗？', async () => {
                showLoading()
                del({ id: scope.row.processId }).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    } else {
                        this.clientPop('warn', res.message, () => {
                        })
                    }
                    hideLoading()
                })
                hideLoading()
            })
        },
        // 批量删除
        handleDelete () {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {
                })
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.systemId
                })
                del(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },

        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleClose () {
        },
        skipView (data) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/process/review/reviewDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'reviewDetail',
                params: { row: data }
            })
        },
        handleView (row) {
            row.classPath = this.classPath
            this.skipView(row)
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
                this.pages = res
            })

            this.viewList = true
        },
        async getRoleLists () {
            getRoleList().then(res=> {
                this.roleOptions = res
            })
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        // 关键词搜索
        onSearch () {
            // 参数
            this.getParams()
            getList(this.requestParams).then(res => {
                if (res.list) {
                    this.tableData = res.list
                    this.pages = res
                } else {
                    this.clientPop('warn', res.message, () => {
                    })
                }
            })
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 置空表单
        emptyForm () {
            this.formData = {
                processName: '',
                systemNo: '',
                processType: '',
                systemName: '',
                remark: '',
                processConfigDtlItemVOs: [
                    { nodeNo: '0', nodeName: '提交', roleNo: '', roleName: '', userId: '', userName: '', disabled: true },
                ]
            }
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        this.handleCreateData()
                    })
                }
            })
        },
        // 修改数据
        handleEditData () {
            update(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.viewList = true
                    })
                }
            })
        },
        // 保存数据
        handleCreateData () {
            create(this.formData).then(res => {
                if (res.message == '操作成功') {
                    return this.clientPop('suc', '保存成功', () => {
                        this.getTableData()
                        this.getRoleLists()
                        this.viewList = true
                    })
                }
            })
        },
        onCancel () {
            this.viewList = true
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        },
        // 新增节点
        addNode () {
            this.formData.processConfigDtlItemVOs.push(
                { nodeNo: '1', nodeName: '审核', roleNo: '', roleName: '', userId: '', userName: '', disabled: false },
            )
        },
        // 删除节点
        removeNode (index) {
            this.formData.processConfigDtlItemVOs.splice(index, 1)
        },

        onSystemChange (val) {
            const selected = this.systemOptions.find(u => u.systemNo === val)
            if (selected) {
                this.formData.systemName = selected.systemName
                this.formData.systemNo = selected.systemNo
            }
        },
        onNodeChange (val, item) {
            const selected = this.nodeOptions.find(u => u.nodeNo === val)
            if (selected) {
                item.nodeName = selected.nodeName
                item.nodeNo = selected.nodeNo
            }
        },
        onRoleChange (val, item) {
            const selected = this.roleOptions.find(u => u.roleNo === val)
            if (selected) {
                item.roleName = selected.roleName
                item.roleNo = selected.roleId
            }
        },
        onUserChange (val, item) {
            const selected = this.userOptions.find(u => u.userId === val)
            if (selected) {
                item.userName = selected.userName
                item.userId = selected.userId
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.right .top {
  padding-right: 10px
}

.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__explain {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}

.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

.actionDel {
  margin-right: 10px;
  color: red;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}
.myButtom {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-left: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
