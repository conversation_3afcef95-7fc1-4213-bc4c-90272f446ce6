<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="订单信息" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="订单商品" name="productInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">订单信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="formData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单号：">
                                            <span>{{ formData.orderSn }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="卖方：">
                                            <span>{{ formData.shopName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row v-if="productType!=0">
                                    <el-col :span="12">
                                        <el-form-item label="供应商名称">
                                            <span>{{ formData.supplierName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="买方：">
                                            <span>{{ formData.enterpriseName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品名称：">
                                            <div style="width: 80%" class="textOverflow1">
                                                {{ formData.untitled }}
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收件人：">
                                            <span>{{ formData.receiverName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row v-if="productType!=0">
                                    <el-col :span="12">
                                        <el-form-item label="订单类型：">
                                            <span v-if="formData.orderClass==1">普通订单</span>
                                            <span v-if="formData.orderClass==2" >多供方订单</span>
                                            <span v-if="formData.orderClass==3" >二级订单</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="清单类型：">
                                            <div style="width: 80%" class="textOverflow1">
                                                <el-tag v-if="formData.billType==1">浮动价格</el-tag>
                                                <el-tag v-if="formData.billType==2">固定价格</el-tag>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="收件人手机号：">
                                            <span>{{ formData.receiverMobile }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收件地址：">
                                            <span>{{ formData.receiverAddress }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：">
                                            <span>{{ formData.actualAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col v-if="productType!=0" :span="12">
                                        <el-form-item label="不含税总金额：">
                                            <span>{{ formData.noRateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row v-if="productType!=0">
                                    <el-col :span="12">
                                        <el-form-item label="是否开发票：">
                                            <el-tag v-if="formData.orderBillState === 0">初始</el-tag>
                                            <el-tag v-if="formData.orderBillState === 1">已申请</el-tag>
                                            <el-tag v-if="formData.orderBillState === 2" type="success">已开票</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="支付类型：">
                                            <el-tag v-if="formData.payWay === 1">线上支付</el-tag>
                                            <el-tag v-if="formData.payWay === 2">内部结算</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="商品类型：">
                                            <el-tag v-if="formData.productType == 0">零星采购</el-tag>
                                            <el-tag v-if="formData.productType == 1">大宗临购</el-tag>
                                            <el-tag v-if="formData.productType == 2">周转材料</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="确认状态：">
                                            <el-tag v-if="formData.affirmState == 0">待确认</el-tag>
                                            <el-tag type="success" v-if="formData.affirmState == 1">已确认</el-tag>
                                            <el-tag type="danger" v-if="formData.affirmState == 2">已拒绝</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单提交时间：">
                                            <span>{{ formData.flishTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item v-if="productType!=0" label="货款支付周期（单位月）:">
                                            <span>{{ formData.paymentWeek }}</span>
                                        </el-form-item>
                                        <el-form-item v-else label="发货时间：">
                                            <span>{{ formData.deliveryTime }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="订单状态：">
                                            <el-tag v-if="formData.state == 0">草稿</el-tag>
                                            <el-tag v-if="formData.state == 1">已提交</el-tag>
                                            <el-tag v-if="formData.state == 2">待确认</el-tag>
                                            <el-tag v-if="formData.state == 3">已确认</el-tag>
                                            <el-tag v-if="formData.state == 4">待签订合</el-tag>
                                            <el-tag v-if="formData.state == 5">已签合同</el-tag>
                                            <el-tag v-if="formData.state == 6">待发货</el-tag>
                                            <el-tag v-if="formData.state == 7">已关闭</el-tag>
                                            <el-tag v-if="formData.state == 8">发货中</el-tag>
                                            <el-tag v-if="formData.state == 9">待收货</el-tag>
                                            <el-tag v-if="formData.state == 10">已完成</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="超期垫资利息（%）：">
                                            <span>{{ formData.outPhaseInterest }} </span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="账期：">
                                            <span>{{ formData.paymentPeriod }}个月</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="订单备注：">
                                            <span>{{ formData.orderRemark }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <!-- 订单商品-->
                    <div id="productInfo" class="con" v-loading="tableLoading">
                        <div class="tabs-title" id="contractList">订单商品</div>
                        <div class="e-table"  style="background-color: #fff">
                            <div class="top" style="height: 50px; padding-left: 10px">
                                <div class="left">
                                    <el-input type="text" @blur="getTableData" placeholder="输入搜索关键字" v-model="keywords">
                                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTableData" />
                                    </el-input>

                                    <el-button v-show="formData.state < 9 && formData.affirmState == 1" type="primary" @click="openWuliu" style="margin-left: 10px">生成发货单</el-button>

                                    <div class="left-btn" style="margin-left: 20px">
                                        <!--                                        <el-button type="primary" class="btn-greenYellow" @click = "changePrice">批量改价</el-button>-->
                                    </div>
                                </div>
                            </div>
                            <el-table ref="tableRef"
                                      border
                                      style="width: 100%"
                                      :data="tableData"
                                      class="table"
                                      :max-height="$store.state.tableHeight"
                            >
                                <el-table-column label="序号" type="index" width="60"></el-table-column>
                                <el-table-column prop="productSn" label="商品编码" width="250"></el-table-column>
                                <el-table-column prop="productName" label="商品名称" width="150"></el-table-column>
                                <el-table-column prop="productImg" label="商品图片" width="130">
                                    <template slot-scope="scope">
                                        <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productImg"></el-image>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="skuName" label="规格" width="200"/>
                                <el-table-column prop="unit" label="单位" width=""/>
                                <!-- <el-table-column prop="productPrice" label="单价（含税）" width="130"/>
                                <el-table-column prop="noRatePrice" label="单价（不含税）" width="130"/> -->
                                <el-table-column prop="buyCounts" label="购买数量" width=""></el-table-column>
                                <el-table-column   label="发货数量" width="200" v-if="formData.affirmState==1">
                                    <template v-slot="scope">
                                        <!-- TODO 这里问题很大，发货数量步进不应该是小数 -->
                                        <el-input-number v-model="scope.row.orderShipCounts"
                                                         size="mini"
                                                         :min="0"
                                                         :precision="0" :step="1" :max="toMaxCount(scope.row)"
                                                         @change="getChangedRowNum(scope.row)"
                                        />
                                    </template>
                                </el-table-column>
                                <el-table-column  prop="shipCounts" label="已发货数量" width="160px"></el-table-column>
                                <el-table-column  prop="confirmCounts" label="确认收货数量" width="160px"></el-table-column>
                                <el-table-column  prop="returnCounts" label="商城退货数量" width="100"></el-table-column>
                                <el-table-column  prop="pcwpReturn" label="pcwp退货数量" width="100"></el-table-column>
                                <el-table-column  prop="paymentPeriod" label="账期（月）" width="160px"></el-table-column>
                                <!-- TODO 不确定是否展示，暂时隐藏 -->
                                <!-- <el-table-column v-if="formData.billType === 1" prop="netPrice" label="网价" width="100">-->
                                <!--</el-table-column>-->
                                <!--<el-table-column v-if="formData.billType === 1" prop="fixationPrice" label="固定费" width="100">-->
                                <!--</el-table-column>-->
                                <!--<el-table-column v-if="formData.billType === 2" prop="outFactoryPrice" label="出厂价" width="100">-->
                                <!--</el-table-column>-->
                                <!--<el-table-column v-if="formData.billType === 2" prop="transportPrice" label="运杂费" width="100">-->
                                <!--</el-table-column> -->
                                <el-table-column prop="productPrice" :label="productType!=0?'含税单价':'综合单价'" width="130px"/>
                                <el-table-column prop="noRatePrice" v-if="productType==0" label="不含税综合单价" width=""/>
                                <el-table-column prop="totalAmount" label="含税金额" width="130px"/>
                                <el-table-column prop="noRateAmount" label="不含税金额" width="130px"/>
                                <el-table-column prop="isComment" label="评论状态" width="">
                                    <template slot-scope="scope">
                                        <el-tag v-if="scope.row.isComment === 0">未评价</el-tag>
                                        <el-tag v-if="scope.row.isComment === 1" type="success">已评价</el-tag>
                                    </template>
                                </el-table-column>
                                <!--                                <el-table-column prop="buyTime" label="购买时间" width="160"></el-table-column>-->
                                <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                                <!--                                <el-table-column prop="remarks" label="备注" width="200"></el-table-column>-->
                            </el-table>
                        </div>
                        <!--            分页-->
                        <Pagination
                            v-show="tableData != null || tableData.length != 0"
                            :total="paginationInfo.total"
                            :pageSize.sync="paginationInfo.pageSize"
                            :currentPage.sync="paginationInfo.currentPage"
                            @currentChange="getTableData"
                            @sizeChange="getTableData"
                        />
                    </div>

                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button  v-if="formData.affirmState == 0"
                        type="success"
                        @click="affirmTwoOrderM(formData.orderId, '确认')">确认
            </el-button>
            <el-button  v-if="formData.affirmState == 0"
                        type="danger"
                        @click="affirmTwoOrderM(formData.orderId, '拒绝')">拒绝
            </el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle, toFixed } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
import { affirmTwoOrder, createShipBatch, orderItemList } from '@/api/platform/order/orders'
import { findByOrderSn, updateOrderPrice } from '@/api/shopManage/order/order'
export default {
    data () {
        return {
            formLoading: false,
            tableStateTitle: null, // 状态标题
            keywords: null,
            //基本信息表单数据
            formData: {},
            // 表格数据
            tableData: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedRow: [],
            changedRowNum: [],
            tableLoading: false,
        }
    },
    components: {
        Pagination
    },
    created () {
        // this.formData = this.$route.params.row
        this.findByOrderSnM()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'productInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        productType () {
            return this.$route.params.productType
        }
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        formatNumber (value) {
            // 使用toFixed()方法截取小数点后两位
            return parseFloat(value).toFixed(2)
        },
        parseNumber (value) {
            // 将输入的字符串转换为数字
            return parseFloat(value)
        },
        openWuliu () {
            if (this.changedRowNum.length  <= 0) {
                this.$message({
                    type: 'warning',
                    message: '请输入发货物资的数量'
                })
            }else {
                this.createShip()
            }

        },
        createShip () {
            this.clientPop('info', `您确定要生成订单号为${this.formData.orderId}的发货单吗？`, async () => {
                let params = {
                    orderId: this.formData.orderId,
                    // 零星采购计划
                    sourceType: 2,
                    // 零星采购
                    productType: 0,
                    // deliveryFlowId: this.orderShip.deliveryFlowId,
                    // logisticsCompany: this.orderShip.logisticsCompany,
                    dtls: this.changedRowNum
                }
                createShipBatch(params).then(res=>{
                    if (res.code == 200) {
                        this.$message({
                            type: 'success',
                            message: '发货单生成成功'
                        })
                        this.getTableData()
                        this.changedRowNum = []
                        // this.$router.push('/supplierSys/order/searchOrder/towIndex')
                        this.$router.push('/supplierSys/order/searchOrder/twoOrder/okIndex/' + this.formData.productType)
                    }
                })
                this.getTableData()
            })

        },

        //控制订单的发货数量
        getChangedRowNum (row) {
            // 这里发货数量的限制严格复制的原代码，具体业务逻辑不清楚
            let flag
            if (this.productType != 0) {
                flag = row.orderShipCounts >  Number(row.buyCounts) - (Number(row.shipCounts) - Number(row.pcwpReturn)) - Number(row.returnCounts) || row.orderShipCounts < 0
            }else {
                flag = row.orderShipCounts > Number(row.aboveQty) - Number(row.shipCounts) - Number(row.returnCounts) + Number(row.pcwpReturn) || row.orderShipCounts < 0
            }
            if (flag) {
                this.$message({
                    type: 'warning',
                    message: '发货数量大于购买数量'
                })
                if (this.productType != 0) {
                    row.orderShipCounts = this.fixed4(row.buyCounts - (row.shipCounts - row.pcwpReturn) - row.returnCounts)
                }else {
                    row.orderShipCounts = this.fixed4(Number(row.aboveQty) - Number(row.returnCounts) - Number(row.shipCounts) + Number(row.pcwpReturn))
                }
            }else {
                if(this.changedRowNum.length == 0) {
                    this.changedRowNum.push(
                        {
                            orderItemId: row.orderItemId,
                            shipCounts: this.fixed4(row.orderShipCounts)

                        })
                    return
                }
                let flag = false
                this.changedRowNum.forEach(t => {
                    if(t.orderItemId == row.orderItemId) {
                        t.shipCounts = this.fixed4(row.orderShipCounts)
                        flag = true
                    }
                })
                if(!flag) {
                    this.changedRowNum.push({ orderItemId: row.orderItemId, shipCounts: this.fixed4(row.orderShipCounts) })
                }
                flag = true}

        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        affirmTwoOrderM (orderId, txt) {
            this.clientPop('info', '您确定要对该订单进行【 ' + txt + '】操作吗？', async () => {
                this.formLoading = true
                let params = {
                    orderId: orderId,
                    isAffirm: null
                }
                if(txt == '确认') {
                    params.isAffirm = true
                }
                if(txt == '拒绝') {
                    params.isAffirm = false
                }
                affirmTwoOrder(params).then(res => {
                    this.formLoading = false
                    if(res.code != null && res.code == 200) {
                        this.$message.success('操作成功！')
                        this.$router.go(-1)
                    }
                }).catch(() => {
                    this.formLoading = false
                })
            })
        },
        findByOrderSnM () {
            this.formLoading = true
            findByOrderSn({ orderSn: this.$route.query.orderSn }).then(res => {
                this.formData = res
                this.getTableData()
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 批量修改排序
        changePrice () {
            if(this.changedRow.length == 0) {
                return this.$message('未修改数据！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateOrderPrice(this.changedRow).then(res => {
                    if(res.code == 200) {
                        this.$message.success('修改成功！')
                        this.findByOrderSnM()
                        this.changedRow = []
                    }
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if(this.changedRow.length == 0) {
                this.changedRow.push({ orderItemId: row.orderItemId, productPrice: row.productPrice })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if(t.orderItemId == row.orderItemId) {
                    t.productPrice = row.productPrice
                    flag = true
                }
            })
            if(!flag) {
                this.changedRow.push({ orderItemId: row.orderItemId, productPrice: row.productPrice })
            }
            flag = true
        },
        // 获取表格数据
        getTableData () {
            let params = {
                orderId: this.formData.orderId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.keywords != null) {
                params.keywords = this.keywords
            }
            this.tableLoading = true
            orderItemList(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        addData () {},
        deleteData () {},
        //取消
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder/twoOrder')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        //设置错误标签
        setError (name) {
            if (!this.errorList.find(x => x === name)) {
                this.errorList.push(name)
            }
        },
        toMaxCount (row) {
            // TODO 这里复制的原代码，但明显有问题
            if (this.formData.finishType == 1) {
                return row.closeMaxQty
            }else {
                if (this.productType == 0) {
                    return row.aboveQty - row.shipCounts - row.returnCounts + row.pcwpReturn
                }else {
                    return row.buyCounts - row.returnCounts - row.shipCounts + row.pcwpReturn
                }
            }
        }
    }
}
</script>

<style lang='scss' scoped>
.mainTitle {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgb(246, 246, 246);
    border: 1px solid rgb(236, 236, 236);
    margin: auto;
    margin-bottom: 15px;
    padding-left: 10px;
}

.separ {
    width: 30px;
    height: 40px;
    line-height: 18px;
    text-align: center;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
</style>
