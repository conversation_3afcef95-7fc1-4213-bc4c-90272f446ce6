// import mammoth from 'mammoth'
import { previewFile } from '@/api/platform/common/file'

// 移除直接的PDFJS导入，改为动态加载ES5版本
let PDFJS = null
let PDFWorkerLoaded = false

class MultiFormatPrinter {
    /**
   * 构造函数
   * @param {Object} options 配置选项
   */
    constructor (options = {}) {

        // 默认配置
        this.defaultOptions = {
            printTitle: '文档打印',
            convertDelay: 1000,
            extraCss: [],
            beforeFetch: () => { },
            afterFetch: () => { },
            beforeConvert: () => { },
            afterConvert: () => { },
            beforePrint: () => { },
            afterPrint: () => { },
            onError: error => console.error('打印错误:', error)
        }

        // 合并配置
        this.options = { ...this.defaultOptions, ...options }

        // 初始化打印区域
        this.initPrintArea()

        // 预加载PDFJS的ES5版本
        this.loadPdfJs()
    }

    /**
   * 动态加载PDFJS的ES5兼容版本
   */
    async loadPdfJs () {
        if (!PDFJS) {
            try {
            // 先卸载可能存在的全局PDFJS
                if (window.PDFJS) {
                    delete window.PDFJS
                }

                // 加载ES5版本的pdfjs
                const pdfJsModule = await import('pdfjs-dist/es5/build/pdf.min.js')
                PDFJS = pdfJsModule.default || pdfJsModule

                // 修复Worker加载问题
                if (!PDFWorkerLoaded) {
                // 明确指定worker路径，使用import方式
                    import('pdfjs-dist/es5/build/pdf.worker.min.js').then(workerModule => {
                        PDFJS.GlobalWorkerOptions.workerSrc = workerModule.default
                        PDFWorkerLoaded = true
                    }).catch(error => {
                        console.error('加载PDF worker失败:', error)
                        // 降级方案：使用CDN worker
                        PDFJS.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${PDFJS.version}/pdf.worker.min.js`
                        PDFWorkerLoaded = true
                    })
                }
            } catch (error) {
                console.error('加载PDFJS失败:', error)
                throw new Error('PDF处理库加载失败，请检查依赖')
            }
        }
        return PDFJS
    }

    /**
   * 初始化打印区域
   */
    initPrintArea () {
        this.printAreaId = `print-area-${Date.now()}`
        let printArea = document.getElementById(this.printAreaId)

        if (!printArea) {
            printArea = document.createElement('div')
            printArea.id = this.printAreaId
            printArea.style.display = 'none'
            printArea.style.position = 'absolute'
            printArea.style.top = '-9999px'
            document.body.appendChild(printArea)
        }

        this.printArea = printArea
    }

    /**
   * 根据文件ID打印
   * @param {string} fileId 文件的recordId
   * @param {string} type 类型: word, pdf, image
   * @param {Object} extraParams 额外请求参数
   */
    printFromFileId (fileId, type, extraParams = {}) {
        this.fileId = fileId
        this.extraParams = extraParams
        return this.print(null, type)
    }

    /**
   * 打印资源
   * @param {Object} params 请求参数，如不提供将使用fileId
   * @param {string} type 类型，如不指定则自动检测
   */
    async print (params = {}, type) {
        try {
            // 执行前置钩子
            this.options.beforeFetch()

            // 获取资源 - 使用previewFile方法
            const response = await this.fetchResource(params)
            this.options.afterFetch(response)

            // 执行转换前钩子
            this.options.beforeConvert()

            // 根据类型处理资源
            let contentType = ''
            if (response.headers && typeof response.headers.get === 'function') {
                // 标准Response对象
                contentType = response.headers.get('content-type') || ''
            } else if (response.headers) {
                // axios响应对象或普通对象
                contentType = response.headers['content-type'] ||
                    response.headers['Content-Type'] || ''
            }

            const resourceType = type || this.detectType(contentType)

            // 确保PDFJS已加载
            if (resourceType === 'pdf') {
                await this.loadPdfJs()
            }

            // 根据类型处理并打印
            switch (resourceType) {
            case 'word':
                await this.handleWord(response)
                break
            case 'pdf':
                await this.handlePdf(response)
                break
            case 'image':
                this.handleImage(response)
                break
            default:
                throw new Error(`不支持的文件类型: ${resourceType}`)
            }

            // 执行转换后钩子
            this.options.afterConvert()

            // 延迟打印，确保内容已渲染
            setTimeout(() => this.executePrint(), this.options.convertDelay)

        } catch (error) {
            console.error('打印错误:', error)
            this.options.onError(error)
        }
    }

    /**
   * 从接口获取资源 - 使用previewFile方法
   * @param {Object} params 请求参数
   */
    async fetchResource (params) {
        // 优先使用构造函数中传入的fileId，其次使用params中的recordId
        const recordId = this.fileId || (params && params.recordId)

        if (!recordId) {
            throw new Error('请提供文件的recordId')
        }

        // 合并参数
        const requestParams = {
            recordId,
            ...this.extraParams,
            ...params
        }

        // 调用previewFile方法获取资源
        return await previewFile(requestParams)
    }

    /**
   * 处理Word文档
   * @param {Blob} blob Word文档的Blob对象
   */
    async handleWord (blob) {
        console.log('handleWord', blob)
        // const result = await mammoth.convertToHtml({ blob })
    //     this.printArea.innerHTML = `
    //   <div class="print-header">
    //     <h1>${this.options.printTitle}</h1>
    //   </div>
    //   <div class="print-content">${result.value}</div>
    // `
    //     this.applyStyles()
    }

    /**
   * 处理PDF文档
   * @param {Blob} blob PDF文档的Blob对象
   */
    async handlePdf (blob) {
    // 确保PDFJS已加载
        if (!PDFJS) {
            await this.loadPdfJs()
        }

        // 确保worker已准备好
        if (!PDFWorkerLoaded) {
            await new Promise(resolve => {
                const checkWorker = setInterval(() => {
                    if (PDFWorkerLoaded) {
                        clearInterval(checkWorker)
                        resolve()
                    }
                }, 100)
            })
        }

        try {
        // 尝试正常模式加载
            const pdfDoc = await PDFJS.getDocument({
                data: await blob.arrayBuffer(),
                useWorkerFetch: false,
                workerPort: null,
                disableWorker: false
            }).promise

            // 渲染PDF
            this.renderPdfPages(pdfDoc)
        } catch (error) {
            console.error('PDF处理错误:', error)

            // 尝试禁用worker的降级方案
            try {
                console.log('尝试使用无worker模式加载PDF')
                const pdfDoc = await PDFJS.getDocument({
                    data: await blob.arrayBuffer(),
                    disableWorker: true, // 禁用worker
                    useOnlyCssZoom: true,
                    nativeImageDecoderSupport: 'none'
                }).promise

                // 关键修复：使用相同的渲染逻辑处理降级方案
                this.renderPdfPages(pdfDoc)

            } catch (fallbackError) {
                console.error('降级方案也失败:', fallbackError)
                this.options.onError(fallbackError)
            }
        }
    }

    // 提取共用的PDF渲染逻辑
    async renderPdfPages (pdfDoc) {
        let pdfHtml = `<div class="print-header"><h1>${this.options.printTitle}</h1></div>`
        pdfHtml += '<div class="pdf-container">'

        // 渲染所有页面
        for (let i = 1; i <= pdfDoc.numPages; i++) {
            const page = await pdfDoc.getPage(i)
            const viewport = page.getViewport({ scale: 1.5 })
            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')

            canvas.height = viewport.height
            canvas.width = viewport.width

            await page.render({
                canvasContext: context,
                viewport: viewport
            }).promise

            pdfHtml += `<div class="pdf-page">${canvas.outerHTML}</div>`
        }

        pdfHtml += '</div>'
        this.printArea.innerHTML = pdfHtml
        this.applyStyles()
    }

    /**
   * 处理图片
   * @param {Blob} blob 图片的Blob对象
   * @param {string} contentType 内容类型
   */
    handleImage (blob) {
        const imageUrl = URL.createObjectURL(blob)
        this.printArea.innerHTML = `
      <div class="print-header">
        <h1>${this.options.printTitle}</h1>
      </div>
      <div class="image-container">
        <img src="${imageUrl}" alt="打印图片" class="print-image" />
      </div>
    `
        this.applyStyles()
    }

    /**
   * 应用样式
   */
    applyStyles () {
        // 添加内置样式
        const style = document.createElement('style')
        style.textContent = `
      .print-header {
        text-align: center;
        margin-bottom: 20px;
      }
      .print-content p {
        margin: 10px 0;
        line-height: 1.6;
      }
      .print-content h1, h2, h3 {
        margin: 15px 0 10px;
      }
      .pdf-container {
        width: 100%;
      }
      .pdf-page {
        margin-bottom: 20px;
        text-align: center;
      }
      .image-container {
        text-align: center;
        padding: 20px;
      }
      .print-image {
        max-width: 100%;
        max-height: 80vh;
      }
    `
        this.printArea.appendChild(style)

        // 添加额外的CSS
        if (this.options.extraCss && this.options.extraCss.length) {
            [].concat(this.options.extraCss).forEach(cssUrl => {
                const link = document.createElement('link')
                link.rel = 'stylesheet'
                link.href = cssUrl
                this.printArea.appendChild(link)
            })
        }

        // 执行打印前处理钩子
        this.options.beforePrint(this.printArea)
    }

    /**
   * 执行打印
   */
    executePrint () {
        const printWindow = window.open('', '_blank')

        // 构建打印页面
        printWindow.document.write(`
      <html>
        <head>
          <title>${this.options.printTitle}</title>
          <style>
            @media print {
              @page {
                margin: 1.5cm;
              }
              body {
                font-family: Arial, sans-serif;
              }
            }
          </style>
        </head>
        <body>
          ${this.printArea.innerHTML}
          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            }
          </script>
        </body>
      </html>
    `)

        printWindow.document.close()

        // 执行打印后钩子
        this.options.afterPrint()
    }

    /**
   * 检测文件类型
   * @param {string} contentType 内容类型
   */
    detectType (contentType) {
        if (contentType.includes('word') || contentType.includes('msword')) {
            return 'word'
        } else if (contentType.includes('pdf')) {
            return 'pdf'
        } else if (contentType.includes('image')) {
            return 'image'
        }
        return 'unknown'
    }

    /**
   * 销毁实例
   */
    destroy () {
        const printArea = document.getElementById(this.printAreaId)
        if (printArea && printArea.parentElement) {
            printArea.parentElement.removeChild(printArea)
        }
    }
}

// Vue插件安装函数
MultiFormatPrinter.install = function (Vue, globalOptions = {}) {
    // 注册全局组件
    Vue.component('multi-format-printer', {
        props: {
            options: {
                type: Object,
                default: () => ({})
            },
            fileId: {
                type: String,
                default: ''
            },
            type: {
                type: String,
                default: ''
            }
        },
        render (h) {
            // 默认插槽：自定义触发按钮
            const triggerSlot = this.$slots.trigger || [
                h('button', {
                    on: {
                        click: () => this.print()
                    }
                }, '打印文档')
            ]

            return h('div', triggerSlot)
        },
        methods: {
            print (params = {}, options = {}) {
                // 创建打印机实例
                const printer = new MultiFormatPrinter({
                    ...globalOptions,
                    ...this.options,
                    ...options
                })

                // 执行打印
                if (this.fileId) {
                    printer.printFromFileId(this.fileId, this.type || options.type, params)
                } else {
                    printer.print(params, this.type || options.type)
                }

                // 保存实例以便后续操作
                this.printerInstance = printer
            }
        },
        beforeDestroy () {
            if (this.printerInstance) {
                this.printerInstance.destroy()
            }
        }
    })

    // 挂载到Vue原型，便于全局调用
    Vue.prototype.$multiPrinter = function (options = {}) {
        return new MultiFormatPrinter({ ...globalOptions, ...options })
    }
}

// 导出
export default MultiFormatPrinter
