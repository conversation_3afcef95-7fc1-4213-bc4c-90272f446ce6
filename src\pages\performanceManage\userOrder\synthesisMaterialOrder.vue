<template>
    <!-- 列表 -->
    <div class="right">
        <div class="e-table">
            <div class="top">
                <div class="left">
                    <!--                        <div class="left-btn">-->
                    <!--                            <el-select  @change="stateTopOptionsClick" v-model="stateOptionTitle" placeholder="请选择状态">-->
                    <!--                                <el-option-->
                    <!--                                    v-for="item in stateOptions"-->
                    <!--                                    :key="item.value"-->
                    <!--                                    :label="item.label"-->
                    <!--                                    :value="item.value">-->
                    <!--                                </el-option>-->
                    <!--                            </el-select>-->
                    <!--                        </div>-->
                </div>
                <div class="search_box">
                    <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                    <el-radio v-model="filterData.orderBy" :label="2">按订单提交时间排序</el-radio>
                    <el-radio v-model="filterData.orderBy" :label="3">按发货时间排序</el-radio>
                    <el-input v-model="keywords" placeholder="输入搜索关键字" style="width: 300px" type="text" @blur="handleInputSearch">
                        <img slot="suffix" alt="" src="@/assets/search.png" @click="handleInputSearch"/>
                    </el-input>
                    <div class="adverse">
                        <el-button size="small" type="primary" @click="queryVisible = true">高级搜索</el-button>
                    </div>
                </div>
            </div>
        </div>
        <!--            表格-->
        <div class="e-table">
            <el-table
                v-loading="tableLoading" :data="tableData" :height="rightTableHeight" border class="table"
                @selection-change="selectionChangeHandle"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="订单号" prop="orderSn" width="240">
                    <template slot-scope="scope">
                        <span class="action" @click="handleView(scope.row)">{{ scope.row.orderSn }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="untitled" width="200"></el-table-column>
                <!--                    <el-table-column label="状态" prop="state">-->
                <!--                        <template slot-scope="scope">-->
                <!--                            <el-tag v-if="showTableState(scope.row)">{{ tableStateTitle }}</el-tag>-->
                <!--                        </template>-->
                <!--                    </el-table-column>-->
                <el-table-column label="状态" prop="state">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.state == 0" type="info">草稿</el-tag>
                        <span v-if="scope.row.state == 1">已提交</span>
                        <span v-if="scope.row.state == 2">待确认</span>
                        <span v-if="scope.row.state == 3">已确认</span>
                        <span v-if="scope.row.state == 4">待签订合</span>
                        <span v-if="scope.row.state == 5">已签合同</span>
                        <span v-if="scope.row.state == 6">待发货</span>
                        <span v-if="scope.row.state == 7">已关闭</span>
                        <span v-if="scope.row.state == 8">发货中</span>
                        <span v-if="scope.row.state == 9">待收货</span>
                        <span v-if="scope.row.state == 10">已完成</span>
                    </template>
                </el-table-column>
                <el-table-column label="订单类型" prop="orderClass" width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.orderClass == 1">普通订单</span>
                        <span v-if="scope.row.orderClass == 2">多供方订单</span>
                        <span v-if="scope.row.orderClass == 3">已拆分订单</span>
                    </template>
                </el-table-column>
                <el-table-column label="商品类型" prop="productType" width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.productType == 0">物资</span>
                        <span v-if="scope.row.productType == 10">低值易耗品</span>
                        <span v-if="scope.row.productType == 11">办公用品</span>
                        <span v-if="scope.row.productType == 12">主要材料</span>
                    </template>
                </el-table-column>
                <el-table-column label="机构名称" v-if="showDevFunc" prop="enterpriseName" width=""></el-table-column>
                <el-table-column label="收件人" prop="receiverName"></el-table-column>
                <el-table-column label="收件人手机号" prop="receiverMobile" width="150"/>
                <el-table-column label="收件地址" prop="receiverAddress" width="200"/>
                <!--                    <el-table-column label="总金额" width="120" prop="actualAmount" />-->
                <!--                    <el-table-column label="物流单号" width="200" prop="deliveryFlowId" >-->
                <!--                    </el-table-column>-->
                <!--                    <el-table-column label="物流公司" width="200" prop="logisticsCompany"/>-->
                <el-table-column label="创建时间" prop="gmtCreate" width="160"/>
                <el-table-column label="订单提交时间" prop="flishTime" width="160"/>
                <el-table-column label="发货时间" prop="deliveryTime" width="160"/>
                <el-table-column label="完成时间" prop="successDate" width="160"/>
                <el-table-column label="订单备注" prop="orderRemark" width="300"></el-table-column>
            </el-table>
        </div>
        <!--            分页-->
        <Pagination
            v-show="tableData != null || tableData.length != 0"
            :currentPage.sync="paginationInfo.currentPage"
            :pageSize.sync="paginationInfo.pageSize"
            :total="paginationInfo.total"
            @currentChange="getTableData"
            @sizeChange="getTableData"
        />
        <!--高级查询-->
        <el-dialog :visible.sync="queryVisible" title="高级查询" width="40%">
            <el-form ref="form" :inline="false" :model="filterData" label-width="120px">
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="订单类型：">
                            <el-select style="width: 100%;" v-model="filterData.orderClass" placeholder="请选择订单类型">
                                <el-option
                                    v-for="item in filterData.orderClasss"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="商品类型：">
                            <el-select style="width: 100%;" v-model="filterData.productType" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.productTypeSelect"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="订单状态：">
                            <el-select style="width: 100%;" v-model="filterData.selectSateValue" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="showDevFunc">
                    <el-col :span="22">
                        <el-form-item label="数据权限：">
                        <el-select style="width: 100%;" :value="dataSource" @change="dataSourceChange">
                            <el-option label="全部机构" :value="0"/>
                            <el-option label="本机构" :value="1"/>
                            <el-option v-if="userPermission.hasSubOrg()" label="下级机构" :value="2"/>
                            <el-option v-for="item in userPermission.subOrg"
                                :label="item.orgName" :value="item.orgId" :key="item.orgId"
                            />
                        </el-select>
                    </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="订单号：">
                            <el-input v-model="filterData.orderSn" clearable maxlength="100" placeholder="请输入订单号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="商品名称：">
                            <el-input v-model="filterData.untitled" clearable maxlength="100" placeholder="请输入商品名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="店铺名称：">
                            <el-input v-model="filterData.shopName" clearable maxlength="100" placeholder="请输入店铺名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="创建时间：">
                            <el-date-picker style="width: 100%;"
                                v-model="filterData.dateValue"
                                end-placeholder="结束日期"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="datetimerange"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="订单提交时间：">
                            <el-date-picker style="width: 100%;"
                                v-model="filterData.okDate"
                                end-placeholder="结束日期"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="datetimerange"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="发货时间：">
                            <el-date-picker style="width: 100%;"
                                v-model="filterData.deliverGoodsDate"
                                end-placeholder="结束日期"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="datetimerange"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="11">
                        <el-form-item label="总金额以上：">
                            <el-input v-model="filterData.abovePrice" placeholder="请输入价格区间" type="number"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="11">
                        <el-form-item label="总金额以下：">
                            <el-input v-model="filterData.belowPrice" placeholder="请输入价格区间" type="number"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce } from '@/utils/common'
import { mapState } from 'vuex'
import { selectOrderList } from '@/api/platform/order/orders'
import { UserPermission } from '@/utils/permissions'
export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            tableLoading: false,
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            stateOptionTitle: '', // 选中的状态标题
            // 表格数据
            tableStateTitle: null, // 表格的状态
            dataListSelections: [], //选中的数据
            className: null,
            classId: null, // 分类id
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                untitled: null,
                orderSn: null,
                shopName: null,
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
                okDate: [], // 订单提交时间
                deliverGoodsDate: [], // 发货时间
                selectStateTitle: null, // 选中的标题
                selectSateValue: null,  // 选中的值
                productType: null,
                orderClass: null,
                orderClasss: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 1,
                        label: '普通订单'
                    }
                    , {
                        value: 2,
                        label: '多供方订单'
                    }
                ],
                stateOptions: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 0,
                        label: '草稿'
                    }
                    , {
                        value: 6,
                        label: '待发货'
                    }
                    , {
                        value: 7,
                        label: '已关闭'
                    }, {
                        value: 8,
                        label: '已发货'
                    }, {
                        value: 9,
                        label: '待收货'
                    }, {
                        value: 10,
                        label: '已完成'
                    }],
                productTypeSelect: [
                    {
                        value: null,
                        label: '全部'
                    },
                    {
                        value: 0,
                        label: '物资'
                    },
                    {
                        value: 10,
                        label: '低值易耗品'
                    },
                    {
                        value: 11,
                        label: '办公用品'
                    },
                ],
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            // 数据权限
            userPermission: new UserPermission,
            dataSource: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    },
    methods: {
        // 数据权限切换
        dataSourceChange (state) {
            this.dataSource = state
            if(state === 0) {
                this.userPermission.getAllOrgData()
            }else if(state === 1) {
                this.userPermission.getHostOrgData()
            }else if(state === 2) {
                this.userPermission.getSubOrgData()
            }else if(state.length > 1) {
                this.userPermission.getSubOrgData(state)
            }
        },
        // 显示状态
        showTableState (row) {
            let stateValue = row.state
            if (stateValue === 7) {
                return false
            }
            for (let i = 0; i < this.stateOptions.length; i++) {
                if (stateValue === this.stateOptions[i].value) {
                    this.tableStateTitle = this.stateOptions[i].label
                    return true
                }
            }
        },
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.selectOptionValue = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            // if(row.orderClass == 2) {
            //     this.$router.push({
            //         path: '/platform/order/towOrderDetail',
            //         name: 'towOrderDetail',
            //         query: {
            //             orderSn: row.orderSn
            //         }
            //     })
            // }
            // if(row.orderClass == 1) {
            //     this.$router.push({
            //         //path后面跟跳转的路由地址
            //         path: '/platform/order/orderDetail',
            //         //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
            //         name: 'orderDetail',
            //         query: {
            //             orderSn: row.orderSn
            //         }
            //     })
            // }
            this.$router.push({ path: '/performanceManage/synthesisMaterialDetail', query: { orderSn: row.orderSn } })
        },
        resetSearchConditions () {
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.untitled = null
            this.filterData.orderSn = null
            this.filterData.shopName = null
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.okDate = []
            this.filterData.deliverGoodsDate = []
            this.filterData.selectSateValue = null // 选中的值
            this.filterData.productType = null
            this.filterData.orderClass = null
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.selectOptionValue = null
            this.stateOptionTitle = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 高级搜索状态选中
        stateOptionsClick (value) {
            this.filterData.selectSateValue = value
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 获取表格数据
        getTableData () {
            let params = {
                classId: this.classId,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                productType: 12,
                // 数据权限
                dataSelect: this.userPermission.orgDisplayState, // （1本机及子级2只看本级3看指定子级）
                dataScope: this.userPermission.currentSubOrgId
            }
            if (this.filterData.orderClass != null) {
                params.orderClass = this.filterData.orderClass
            }
            if (this.selectOptionValue != null) {
                params.state = this.selectOptionValue
            }
            if (this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if (this.filterData.productType != null && this.filterData.productType != '') {
                params.productType = this.filterData.productType
            }
            if (this.filterData.selectSateValue != null) {
                params.state = this.filterData.selectSateValue
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.filterData.okDate != null) {
                params.okStartDate = this.filterData.okDate[0]
                params.okEndDate = this.filterData.okDate[1]
            }
            if (this.filterData.deliverGoodsDate != null) {
                params.deliverGoodsStartDate = this.filterData.deliverGoodsDate[0]
                params.deliverGoodsEndDate = this.filterData.deliverGoodsDate[1]
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.untitled != null) {
                params.untitled = this.filterData.untitled
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            selectOrderList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list || []
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message ({ message, code }) {
            let type = code === 200 ? 'success' : 'error'
            this.$message({ message, type })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;
}

.e-table {
    min-height: auto;
    .top {
        height: 50px;
    }
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}
</style>
