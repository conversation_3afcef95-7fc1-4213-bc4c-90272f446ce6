<template><!-- 后台管理平台 管理员审核自营店上架商品 详情 -->
  <div class="e-form">
    <BillTop @cancel="handleClose"></BillTop>
    <div v-loading="formLoading" class="tabs warningTabs">
      <el-tabs tab-position="left" v-model="tabsName"
               @tab-click="onChangeTab">
        <el-tab-pane label="详情" name="baseInfo" :disabled="clickTabFlag">
        </el-tab-pane>
        <el-tab-pane :disabled="clickTabFlag" label="审核记录" name="auditRecords">
        </el-tab-pane>
        <div id="tabs-content">
          <div id="baseInfo" class="con">
            <div class="tabs-title" id="baseInfo">详情</div>
            <!--新增-->
            <div class="form" v-if="showForm">
              <el-form :model="addForm.formData" :rules="formRules" label-width="200px" ref="formEdit"
                       :disabled="true" class="demo-ruleForm">
                <el-row>
                  <!-- <el-col :span="12">
                    <el-form-item label="名称：" prop="productName">
                      <el-input clearable v-model="addForm.formData.productName"></el-input>
                    </el-form-item>
                  </el-col> -->
                  <el-col :span="12">
                    <el-form-item label="物资名称：" prop="relevanceName">
                      <el-input placeholder="请选择物资" clearable disabled
                                v-model="addForm.formData.relevanceName"></el-input>
                      <el-button size="mini" type="primary" @click="importDeviceSelect">选择</el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="商品编码：" prop="serialNum">
                      <el-input clearable v-model="addForm.formData.serialNum"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="分类：" prop="classId">
                      <category-cascader :classId.sync='addForm.formData.classId'
                                         :catelogPath="addForm.formData.classPath"
                                         customStyle="width: 100%"
                                         style="width: 100%"
                                         :productType="0" @change="resetRelevanceAndBrand"></category-cascader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="税率（%）：" prop="taxRate">
                      <el-input clearable type="number"
                                v-model="addForm.formData.taxRate"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="品牌：" prop="brandName">
                      <el-input clearable v-model="addForm.formData.brandName"></el-input>
                      <el-button size="mini" type="primary" @click="brandDialog">选择</el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="排序：">
                      <el-input clearable type="number"
                                v-model="addForm.formData.shopSort"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="规格：" prop="skuName">
                      <el-input clearable v-model="addForm.formData.skuName"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="计量单位" prop="unit">
                      <el-select @change="numUnitChange" v-model="addForm.formData.unit"
                                 placeholder="请选择计量单位">
                        <el-option v-for="item in addForm.numUnitOptions" :key="item.value"
                                   :label="item.label" :value="item.label">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="12">
                    <el-form-item label="销售价格" prop="sellPrice">
                      <el-input
                        placeholder="请输入销售价格" clearable type="number"
                        oninput="if(value.length > 16)value = value.slice(0, 16)"
                        v-model="addForm.formData.sellPrice"
                      ></el-input>
                    </el-form-item>
                  </el-col> -->
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="库存：" prop="stock">
                      <el-input clearable type="number"
                                v-model="addForm.formData.stock"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="材质：" prop="productTexture">
                      <el-input
                          v-model="addForm.formData.productTexture" clearable
                          oninput="if(value.length > 16)value = value.slice(0, 16)"
                          placeholder="请输入材质"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="采购进价：" prop="purchasePrice">
                      <el-input
                          v-model="addForm.formData.purchasePrice" clearable
                          placeholder="请输入采购进价"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="年化率（%）：" prop="annualizedRate">
                      <el-input
                          v-model="addForm.formData.annualizedRate" clearable
                          placeholder="请输入年化率"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12" v-if="addForm.formData.priceType === 0">
                    <el-form-item label="账期：" prop="accountPeriod">
                      <el-select v-model="addForm.formData.accountPeriod" placeholder="请选择账期" multiple>
                        <el-option
                          v-for="item in accountPeriodOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="加成率：">
                      <el-radio v-model="addForm.formData.markUp" label="1">固定加成率</el-radio>
                      <el-radio v-model="addForm.formData.markUp" label="2">自定义加成率</el-radio>
                      <el-input v-model="addForm.formData.markUpNum"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                      <el-form-item label="价格类型：" prop="priceType">
                      <el-radio-group v-model="addForm.formData.priceType">
                          <el-radio :label="0">一口价</el-radio>
                          <el-radio :label="1">参考价</el-radio>
                      </el-radio-group>
                      </el-form-item>
                  </el-col>
                </el-row>
                <div v-if="addForm.formData.priceType === 1">
                  <div style="max-height: 25vh;overflow-y: auto;margin: 10px 0;">
                    <div>
                      <div v-for="(item, index) in addForm.formData.regionTableData" :key="item.index">
                        <el-row>
                          <el-col :span="9">
                            <el-form-item :label="item.regionName">
                              <div v-if="item.regionName === '全区域'" style="width: 100%">
                                <el-select v-model="item.detailAddress" multiple placeholder="请选择">
                                  <el-option
                                    v-for="item in economizeData"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                  >
                                  </el-option>
                                </el-select>
                              </div>
                              <div v-else style="width: 100%">
                                  <el-select
                                      style="width: 100%"
                                      v-model="item.detailAddress"
                                      @change="handleAddressChange(item.index)"
                                      :options="marketData"
                                      :props="{ multiple: true, checkStrictly: true }"
                                      clearable>
                                  </el-select>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label="销售价格（含税）：" :prop="'regionTableData.' + index + '.taxInPrice'" :rules="formRules.taxInPrice" label-width="150px">
                              <el-input
                                v-model="item.taxInPrice"
                                style="width: 100%"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item label="销售价格（不含税）：" label-width="150px">
                              <el-input
                                v-model="item.price"
                                style="width: 100%"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6" v-if="item.maolilv">
                            <el-form-item label="毛利率（%）：" label-width="150px">
                              <el-input
                                v-model="item.maolilv"
                                style="width: 100%"
                              ></el-input>
                            </el-form-item>
                          </el-col>
<!--                          <el-col :span="1">
                            <el-form-item>
                              <el-button icon="el-icon-delete" type="text" size="mini" @click="removeOneParamsData(item)"></el-button>
                            </el-form-item>
                          </el-col>-->
                        </el-row>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="addForm.formData.priceType === 0 && addForm.formData.accountPeriod.length > 0">
                  <div class="tabContent">
                    <div v-for="item in addForm.formData.accountPeriod" :key="item" class="accountPeriodTabs">
                      <div :class="currentTab === item ? 'tabTitle' : 'tabTitleNoSelect'" @click="getCurrentTab(item)">{{item + "个月账期信息"}}</div>
                    </div>
                  </div>
                  <div style="max-height: 25vh;overflow-y: auto;margin: 10px 0;">
                    <div>
                      <div v-for="(item, index) in addForm.formData.currentRegionTableData" :key="item.index">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item :label="item.regionName">
                              <div v-if="item.regionName === '全区域'" style="width: 100%">
                                <el-select v-model="item.detailAddress" multiple placeholder="请选择" @change="handleAddressChange2">
                                  <el-option
                                    v-for="item in economizeData"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                  >
                                  </el-option>
                                </el-select>
                              </div>
                              <div v-else style="width: 100%">
                                <el-select
                                  style="width: 100%"
                                  v-model="item.detailAddress"
                                  @change="handleAddressChange3(item.index)"
                                  :options="marketData"
                                  :props="{ multiple: true, checkStrictly: true }"
                                  clearable>
                                </el-select>
                              </div>
                            </el-form-item>
                          </el-col>
                          <el-col :span="5">
                            <el-form-item label="先款后货：" label-width="150px" :prop="'currentRegionTableData.' + index + '.payBeforeDelivery'" :rules="formRules.payBeforeDelivery">
                              <el-input
                                v-model="item.payBeforeDelivery"
                                style="width: 100%"
                                @change="payBeforeDeliveryChange(item.index)"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="5">
                            <el-form-item label="销售价格（含税）：" :prop="'currentRegionTableData.' + index + '.taxInPrice'" :rules="formRules.taxInPrice" label-width="150px">
                              <el-input
                                v-model="item.taxInPrice"
                                style="width: 100%"
                                @change="taxInPriceChange1(item.index)"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="5">
                            <el-form-item label="销售价格（不含税）：" label-width="150px">
                              <el-input
                                v-model="item.price"
                                style="width: 100%"
                              ></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="5" v-if="item.maolilv">
                            <el-form-item label="毛利率（%）：" label-width="150px">
                              <el-input
                                v-model="item.maolilv"
                                style="width: 100%"
                              ></el-input>
                            </el-form-item>
                          </el-col>
<!--                          <el-col :span="1">
                            <el-form-item>
                              <el-button icon="el-icon-delete" type="text" size="mini" @click="removeOneParamsData1(item)"></el-button>
                            </el-form-item>
                          </el-col>-->
                        </el-row>
                      </div>
                    </div>
                  </div>
                </div>
                <el-row>
                  <el-col :span="24">
                    <el-form-item required class="uploader" label="物资主图：" prop="adminFile">
                      <el-upload :class="addForm.adminFileLength === 1 ? 'hide_box_admin' : ''"
                                 action="fakeaction" ref="adminFileRef"
                                 :file-list="addForm.formData.adminFile" list-type="picture-card"
                                 :before-upload="handleBeforeUpload" :auto-upload="false" :limit="1"
                                 :on-remove="adminFileRemove" :on-change="adminFileChange"
                                 :on-preview="handlePictureCardPreview">
                        <i class="el-icon-plus"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item required class="uploader" label="物资图片：" prop="productFiles">
                      <el-upload action="fakeaction" ref="productFileRef"
                                 :file-list="addForm.formData.productFiles" list-type="picture-card"
                                 :before-upload="handleBeforeUpload" :auto-upload="false" :multiple="true"
                                 :on-remove="productFileRemove" :on-change="productFileChange"
                                 :on-preview="handlePictureCardPreview">
                        <i class="el-icon-plus"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item required class="uploader" label="物资小图：" prop="minFile">
                      <el-upload :class="addForm.minFileLength === 1 ? 'hide_box_min' : ''"
                                 action="fakeaction" ref="minFileRef" :file-list="addForm.formData.minFile"
                                 list-type="picture-card" :before-upload="handleBeforeUpload"
                                 :auto-upload="false" :on-remove="minFileRemove" :on-change="minFileChange"
                                 :on-preview="handlePictureCardPreview">
                        <i class="el-icon-plus"></i>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="物资描述：" prop="productDescribe">
                      <editor v-model="addForm.formData.productDescribe" :disabled='true'></editor>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <div id="auditRecordsCon" class="con">
            <div id="auditRecords" class="tabs-title">审核记录</div>
            <div class="e-table">
              <el-table
                ref="auditTableRef"
                class="table"
                :data="auditTableData"
                border
                :height="rightTableHeight"
                highlight-current-row
                v-loading="auditTableLoading"
              >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column label="审核类型" prop="auditType">
                </el-table-column>
                <el-table-column label="审核人" prop="auditName">
                </el-table-column>
                <el-table-column label="人员类型" prop="type">
                </el-table-column>
                <el-table-column label="审核时间" prop="auditTime">
                </el-table-column>
                <el-table-column label="审核意见" prop="record" show-overflow-tooltip>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tabs>
    </div>
    <div class="buttons">
      <div class="buttonsCDiv" v-if="addForm.formData.markUp == 0 || addForm.formData.markUp == 1 || (addForm.formData.markUp == 2 && addForm.formData.jcState == 4)">
        <el-button style="padding:0 8px;" v-if="addForm.formData.state === 3" size="mini" type="success"
                 @click="updateState(addForm.formData, 1, '通过')">通过
        </el-button>
        <el-button style="padding:0 8px;" v-if="addForm.formData.state === 3" size="mini" type="danger"
                  @click="updateState(addForm.formData, 4, '不通过')">不通过
        </el-button>
      </div>
      <div class="buttonsCDiv" v-if="addForm.formData.markUp == 2 && addForm.formData.jcState == 0">
        <el-button style="padding:0 8px;" size="mini" type="success"
                 @click="updateProductJcState(addForm.formData,2,'')">审核通过
        </el-button>
        <el-button style="padding:0 8px;" size="mini" type="danger"
                  @click="updateProductJcState(addForm.formData,1,'')">审核不通过
        </el-button>
      </div>
      <div class="buttonsCDiv" v-if="addForm.formData.markUp == 2 && addForm.formData.jcState == 2">
        <el-button style="padding:0 8px;" size="mini" type="success"
                 @click="updateProductJcState(addForm.formData,4,'')">审批通过
        </el-button>
        <el-button style="padding:0 8px;" size="mini" type="danger"
                  @click="updateProductJcState(addForm.formData,3,'')">审批不通过
        </el-button>
      </div>
      <el-button @click="handleClose">返回</el-button>
    </div>
    <!--表格-->
    <el-dialog v-dialogDrag title="选择品牌" :visible.sync="showBrandDialog" width="70%" style="margin-left: 20%;"
               :close-on-click-modal="false">
      <div class="e-table" style="background-color: #fff">
        <div class="top" style="height: 50px; padding-left: 10px">
          <div class="left">
            <el-input clearable type="text" @blur="getBrandTableData" placeholder="输入搜索关键字"
                      v-model="brand.keywords">
              <img :src="require('@/assets/search.png')" slot="suffix" @click="getBrandTableData" />
            </el-input>
          </div>
        </div>
        <el-table ref="tableRef" highlight-current-row border :data="brand.tableData"
                  class="table" @row-click="handleCurrentClick" :max-height="$store.state.tableHeight">
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column prop="name" label="品牌名" width="150"></el-table-column>
          <!--<el-table-column prop="logo" label="品牌logo" width="90"></el-table-column>-->
          <el-table-column prop="descript" label="介绍"></el-table-column>
          <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
          <el-table-column prop="gmtModified" label="更新时间" width="160"></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
                <Pagination v-show="brand.tableData && brand.tableData.length > 0"
                            :total="brand.paginationInfo.total" :pageSize.sync="brand.paginationInfo.pageSize"
                            :currentPage.sync="brand.paginationInfo.currentPage" @currentChange="getBrandTableData"
                            @sizeChange="getBrandTableData" />
            </span>
      <div class="buttons">
        <el-button @click="showBrandDialog = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="imgPreviewDialog" title="图片预览">
      <img class="center mb20" style="display: block" :src="previewImg" alt="">
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import { regionData, CodeToText, TextToCode } from 'element-china-area-data'
import CategoryCascader from '@/components/category-cascader'
import editor from '@/components/quillEditor'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle, addImgUrl, spliceImgUrl, toFixed } from '@/utils/common'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { getBrandPageList } from '@/api/shopManage/brand/brand'
import { uploadFile } from '@/api/platform/common/file'
import { updateProductState, getCheckMaterialInfo, updateProductJcState } from '@/api/platform/product/materialManage'

export default {
    data () {
        return {
            regionTableIndex: 1,
            regionTablePeriodIndex: 7,
            regionTableIndex1: 1,
            regionTableIndex2: 1,
            regionTableIndex3: 1,
            regionTableIndex4: 1,
            regionTableIndex5: 1,
            regionTableIndex6: 1,
            zoneListLoading: false,
            zoneList: [],
            imgPreviewDialog: false,
            dialogVisible: false,
            previewImg: '',
            mainImg: '',
            smallImg: '',
            auditTableLoading: false,
            dialogImageUrl: '',
            formLoading: false,
            formRules: {
                relevanceName: [
                    { required: true, message: '请选择名称', trigger: 'blur' }
                ],
                productName: [
                    { required: true, message: '请输入物资名称', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' }
                ],
                settlePrice: [
                    { required: true, message: '请输入结算价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                skuName: [
                    { required: true, message: '请输入规格', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                stock: [
                    { required: true, message: '请输入库存', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,3})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                costPrice: [
                    { required: true, message: '请输入成本价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                unit: [
                    { required: true, message: '请选择计量单位', trigger: 'blur' }
                ],
                originalPrice: [
                    { required: true, message: '请输入原价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                sellPrice: [
                    { required: true, message: '请输入销售价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' }
                ],
                adminFile: [
                    { required: true, message: '请上传文件', trigger: 'blur' }
                ],
                productFiles: [
                    { required: true, message: '请上传文件', trigger: 'blur' }
                ],
                minFile: [
                    { required: true, message: '请上传文件', trigger: 'blur' }
                ],
                productDescribe: [
                    { required: true, message: '请输入描述', trigger: 'blur' }
                ]
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            // 商品库
            inventory: {
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1
                }
            },
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            uploadImgSize: 20, // 上传文件大小
            //表单数据
            formData: {},
            addForm: {
                formData: {
                    regionTableData: [
                        { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 1, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] }
                    ],
                    currentRegionTableData: [
                        { index: 2, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 2, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] }
                    ],
                    zone: '',
                    zoneAddrList: [],
                    zoneId: '',
                    zonePath: '',
                    productType: 0,
                    relevanceName: null,
                    productName: null,
                    productKeyword: null,
                    classId: null,
                    classPath: [],
                    productMinPrice: null,
                    brandId: null,
                    brandName: null,
                    shopSort: null,
                    skuName: null,
                    settlePrice: null,
                    costPrice: null,
                    stock: 1,
                    unit: null,
                    originalPrice: null,
                    sellPrice: null,
                    province: null,
                    city: null,
                    county: null,
                    detailedAddress: null,
                    productFiles: [],
                    minFile: [],
                    adminFile: [],
                    productDescribe: null
                },
                adminFileLength: 0,
                minFileLength: 0,
                // 地址
                addressData: regionData, // 地址数据
                selectAddressOptions: [], // 地址选择
                // 计量单位
                numUnitOptions: [{
                    value: 1,
                    label: '件'
                }, {
                    value: 2,
                    label: '台'
                }, {
                    value: 3,
                    label: '个'
                }]
            },
            // 品牌数据
            brand: {
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1
                }
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            auditTableData: [],
            currentTab: null,
            accountPeriodOptions: [{
                value: 1,
                label: '1个月账期'
            }, {
                value: 2,
                label: '2个月账期'
            }, {
                value: 3,
                label: '3个月账期'
            }, {
                value: 4,
                label: '4个月账期'
            }, {
                value: 5,
                label: '5个月账期'
            }, {
                value: 6,
                label: '6个月账期'
            }],
            addressData: regionData, // 地址数据
            economizeData: [], //只有省数据
            marketData: [], //省市数据
            scrollToId: 'baseInfo'
        }
    },
    components: {

        Pagination,
        CategoryCascader,
        editor
    },
    created () {
        this.rowData = this.$route.params.row
        if (this.rowData.viewType == 'add') {
            this.viewType = 'add'
            if (this.rowData.classPath != null) {
                this.addForm.formData.classPath = this.rowData.classPath
                this.addForm.formData.classId = this.rowData.classPath[this.rowData.classPath.length - 1]
            }
            this.showForm = true
        } else {
            this.getMaterialInfo()
        }
        this.getEconomizeAndMarketList()
    },
    mounted () {
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) return
            this.scrollToId = ''
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = $item ? $item.offsetTop : null
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => document.getElementById(item).offsetTop)
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
        rightTableHeight () {
            return this.screenHeight - 261
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        }
    },
    methods: {
        getEconomizeAndMarketList () {
            let economizeList = []
            let marketList = []
            this.addressData.forEach((e, i) => {
                economizeList.push({ label: e.label, value: e.value })
                marketList.push({ label: e.label, value: e.value, children: [] })
                e.children.forEach(s => {
                    marketList[i].children.push({ label: s.label, value: s.value })
                })
            })
            this.economizeData = economizeList
            this.marketData = marketList
        },
        updateProductJcState (row, state, yj) {
            let params = {
                productId: row.productId,
                jcState: state
            }
            if (state == 1 || state == 2) {
                params.jcFzrYj = yj
            }
            if (state == 3 || state == 4) {
                params.jcLdYj = yj
            }
            updateProductJcState(params).then(res => {
                if (res.code === 200) {
                    this.handleClose()
                }
            })
        },
        resetRelevanceAndBrand () {
            let arr = ['relevanceName', 'brandName', 'brandId', 'relevanceId']
            arr.forEach(item => (this.addForm.formData[item] = ''))
        },
        updateState (row, state, title) {
            this.clientPop('info', '您确定要对【' + row.productName + '】进行【' + title + '】操作吗！', async () => {
                if (state == 4) {
                    this.$prompt('不通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            productIds: [row.productId],
                            state: state,
                            failReason: value
                        }
                        this.formLoading = true
                        updateProductState(params).then(res => {
                            if (res.code == 200) {
                                this.$message.success(res.message)
                                this.handleClose()
                            }
                            this.formLoading = false
                        })
                    }).catch(() => {
                        this.formLoading = false
                    })
                } else {
                    let params = {
                        productIds: [row.productId],
                        state: state
                    }
                    this.formLoading = true
                    updateProductState(params).then(res => {
                        if (res.code == 200) {
                            this.$message.success(res.message)
                            this.handleClose()
                        }
                        this.formLoading = false
                    }).catch(() => {
                        this.formLoading = false
                    })
                }
            })
        },
        // 下架
        updateStateBatch (state) {
            let params = {
                productIds: [this.addForm.formData.productId],
                state: state
            }
            this.clientPop('info', '您确定要下架这个物资吗！', async () => {
                updateProductState(params).then(res => {
                    this.handleClose()
                    this.message(res)
                })
            })
        },
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
              document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        getMaolilv (regionPrice, purchasePrice, productSource) {
            if (productSource === 3) {
                regionPrice.forEach(e=>{
                    e.maolilv = this.fixed2(((e.taxInPrice - purchasePrice) / e.taxInPrice) * 100)
                })
            }
            if (productSource === 2) {
                regionPrice.forEach(e=>{
                    e.maolilv = this.fixed2(((e.bonusTaxInPrice - e.taxInPrice) / e.bonusTaxInPrice) * 100)
                })
            }
        },
        HandleArrays (arr) {
            let Arr = []
            let Arrall = []
            for(let item of arr) {
                if(item.regionName != '全区域') {
                    let x = item.selectAddressOptions || []
                    for(let i of x) {
                        Arr.push(i[0])
                    }
                    item.selectAddressOptions = Arr
                }
                if(item.regionName == '全区域') {
                    let x = item.selectAddressOptionsAll || []
                    for(let i of x) {
                        Arrall.push(i)
                    }
                    item.selectAddressOptionsAll = Arrall
                }
            }
        },
        // 获取物资详情
        getMaterialInfo () {
            let params = {
                productId: this.rowData.productId,
                productType: 0
            }
            this.formLoading = true
            getCheckMaterialInfo(params).then(res => {
                for(let itemI of res.regionPrice) {
                    if(itemI.regionName != '全区域') {
                        let ab = itemI.selectAddressOptions || []
                        let abArr = []
                        for(let aItem of ab) {
                            abArr.push([aItem])
                        }
                        itemI.selectAddressOptions = abArr
                    }
                }
                this.HandleArrays(res.regionPrice)
                console.log('res.regionPrice',  res.regionPrice)
                let allSelectedCities = []
                res.regionPrice.forEach(item => {
                    if (item.regionName === '全区域') {
                        item.detailAddress = item.area.split(',')
                        if (item.areaCode.startsWith('[')) {
                            try {
                                item.selectAddressOptionsAll = JSON.parse(item.areaCode)
                            } catch (e) {
                                item.selectAddressOptionsAll = item.areaCode.split(',')
                            }
                        } else {
                            item.selectAddressOptionsAll = item.areaCode.split(',')
                        }
                    }
                    else {
                        item.detailAddress = item.area.split(',')
                        if (!item.selectAddressOptions) {
                            if (item.areaCode.startsWith('[[')) {
                                try {
                                    item.selectAddressOptions = JSON.parse(item.areaCode)
                                } catch (e) {
                                    const cityCodes = item.areaCode.split(',')
                                    item.selectAddressOptions = cityCodes.map(code => code)
                                }
                            } else {
                                const cityCodes = item.areaCode.split(',')
                                item.selectAddressOptions = cityCodes.map(code =>  code)
                            }
                        }
                    }
                })
                this.selectedCity = [...new Set(allSelectedCities)]
                this.marketData = this.marketData.map(city => ({
                    ...city,
                    disabled: this.selectedCity.includes(city.value)
                }))
                // this.addressFormatShow(res)
                this.addForm.minFileLength = res.minFile.length
                this.addForm.adminFileLength = res.adminFile.length
                res['currentRegionTableData'] = []
                res['regionTableData'] = []
                this.addForm.formData = res
                addImgUrl(res, this.imgUrlPrefixAdd)
                this.addForm.formData.accountPeriod = res.accountPeriod.split(',').map(Number)
                this.currentTab = this.addForm.formData.accountPeriod[0]
                if (this.addForm.formData.shopType == 1) {
                    this.getMaolilv(res.regionPrice, this.addForm.formData.purchasePrice, this.addForm.formData.productSource)
                }
                if (this.addForm.formData.priceType === 0) {
                    let regionList = []
                    for (let i = 1;i < 7;i++) {
                        let regionOption = res.regionPrice.filter(item1 => item1.accountPeriod === i)
                        if (regionOption.length > 0) {
                            regionOption[0]['index'] = i
                            regionList.push(regionOption[0])
                            regionOption.splice(0, 1)
                            for (let item of regionOption) {
                                item.index = this.regionTablePeriodIndex++
                                regionList.push(item)
                            }
                            if (regionOption.length > 0) {
                                let indexNumber = Number(regionOption[regionOption.length - 1].regionName.replace('区域', '')) + 1
                                switch (i) {
                                case 1:
                                    this.regionTableIndex1 = indexNumber
                                    break
                                case 2:
                                    this.regionTableIndex2 = indexNumber
                                    break
                                case 3:
                                    this.regionTableIndex3 = indexNumber
                                    break
                                case 4:
                                    this.regionTableIndex4 = indexNumber
                                    break
                                case 5:
                                    this.regionTableIndex5 = indexNumber
                                    break
                                case 6:
                                    this.regionTableIndex6 = indexNumber
                                    break
                                }
                            }
                        } else {
                            regionList.push({ index: i, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: i, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
                        }
                    }
                    this.addForm.formData.regionTableData = regionList
                    this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
                } else {
                    this.addForm.formData.regionTableData = res.regionPrice
                    this.addForm.formData.regionTableData.forEach((e, i) => {
                        e['index'] = i + 1
                        if (i === (this.addForm.formData.regionTableData.length - 1)) {
                            this.regionTableIndex = Number(e.regionName.replace('区域', '')) + 1
                        }
                    })
                }
                this.addForm.formData.markUp = String(this.addForm.formData.markUp)
                let auditTableList = []
                let optionTj = {
                    auditType: '提交',
                    auditName: this.addForm.formData.jcTjName,
                    type: '供应商',
                    auditTime: this.addForm.formData.jcTjTime,
                    record: this.addForm.formData.jcTjYj
                }
                auditTableList.push(optionTj)
                let optionFzr = {
                    auditType: '审核',
                    auditName: this.addForm.formData.jcFzrName,
                    type: '电商运营部负责人',
                    auditTime: this.addForm.formData.jcFzrTime,
                    record: this.addForm.formData.jcFzrYj
                }
                auditTableList.push(optionFzr)
                let optionLd = {
                    auditType: '审定',
                    auditName: this.addForm.formData.jcLdName,
                    type: '分管领导',
                    auditTime: this.addForm.formData.jcLdTime,
                    record: this.addForm.formData.jcLdYj
                }
                auditTableList.push(optionLd)
                this.auditTableData = []
                if (this.addForm.formData.jcState >= 3) {
                    this.auditTableData = auditTableList
                } else if (this.addForm.formData.jcState >= 1) {
                    this.auditTableData = auditTableList.slice(0, 2)
                } else if (this.addForm.formData.jcState >= 0) {
                    this.auditTableData.push(auditTableList[0])
                }
                this.showForm = true
                console.log('this.addForm.formData.regionTableData', this.addForm.formData.regionTableData)
                for (const item of this.addForm.formData.regionTableData) {
                    if(item.area && item.regionName != '全区域') {
                        item.detailAddress = item.area
                    }
                }
                console.log('this.addForm.formData.currentRegionTableData', this.addForm.formData.currentRegionTableData)
            }).finally(() => {
                this.formLoading = false
            })
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.formData.unit = value
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null) return
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.addForm.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 物资库点击
        handleCurrentInventoryClick (row) {
            this.addForm.formData.relevanceName = row.equipmentName
            this.addForm.formData.equipmentId = row.equipmentId
            // this.addForm.formData.productName = row.equipmentName
            this.showDeviceDialog = false
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                page: this.inventory.paginationInfo.currentPage,
                limit: this.inventory.paginationInfo.pageSize
            }
            if (this.inventory.keywords != null) {
                params.keyword = this.inventory.keywords
            }
            // getDeviceInventoryPageList(params).then(res =>{
            //     this.inventory.tableData = res.list
            //     this.inventory.paginationInfo.total = res.totalCount
            //     this.inventory.paginationInfo.pageSize = res.pageSize
            //     this.inventory.paginationInfo.currentPage = res.currPage
            // })
        },
        // 选择物资名称
        importDeviceSelect () {
            this.showDeviceDialog = true
            this.getDeviceInventory()
        },

        // 提交
        submit () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                    if (this.viewType === 'add') {
                        // this.formLoading = true
                        // createShopDevice(this.addForm.formData).then(res =>{
                        //     let classInfo = {
                        //         classPath: this.addForm.formData.classPath,
                        //         classId: this.addForm.formData.classId
                        //     }
                        //     // 重置
                        //     this.resetFormData()
                        //     // 恢复分类
                        //     this.addForm.formData.classPath = classInfo.classPath
                        //     this.addForm.formData.classId = classInfo.classId
                        //     this.$refs.adminFileRef.clearFiles()
                        //     this.$refs.productFileRef.clearFiles()
                        //     this.$refs.minFileRef.clearFiles()
                        //     this.addForm.minFileLength = 0
                        //     this.addForm.adminFileLength = 0
                        //     this.formLoading = false
                        //     this.message(res)
                        // })
                    } else {
                        // this.formLoading = true
                        // updateShopDevice(this.addForm.formData).then(res =>{
                        //     this.getMaterialInfo()
                        //     this.formLoading = false
                        //     this.message(res)
                        // })
                    }
                } else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }
            })
        },
        // 地区
        handleAddressChange () {
            let addArr = this.addForm.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.addForm.formData.province = province
            this.addForm.formData.city = city
            this.addForm.formData.county = county
            this.addForm.formData.detailedAddress = province + city + county
        },
        // 小图上传
        minFileChange (file, fileList) {
            fileList.pop()
            this.uploadMinFile(file, fileList)
        },
        // 物资主图上传
        adminFileChange (file, fileList) {
            fileList.pop()
            this.uploadAdminFile(file, fileList)
        },
        // 物资图片上传
        productFileChange (file, fileList) {
            fileList.pop()
            this.uploadProductFile(file, fileList)
        },
        // 品牌单选
        handleCurrentClick (row) {
            this.addForm.formData.brandId = row.brandId
            this.addForm.formData.brandName = row.name
            this.showBrandDialog = false
        },
        // 获取品牌表格
        getBrandTableData () {
            let params = {
                page: this.brand.paginationInfo.currentPage,
                limit: this.brand.paginationInfo.pageSize
            }
            if (this.brand.keywords != null) {
                params.name = this.brand.keywords
            }
            getBrandPageList(params).then(res => {
                this.brand.tableData = res.list
                this.brand.paginationInfo.total = res.totalCount
                this.brand.paginationInfo.pageSize = res.pageSize
                this.brand.paginationInfo.currentPage = res.currPage
            })
        },
        brandDialog () {
            this.showBrandDialog = true
            this.getBrandTableData()
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params.raw)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 上传主图
        uploadAdminFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.adminFileLength = 1
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath
                file.name = res[0].objectName
                file.isMain = 1
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 0
                this.addForm.formData.adminFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 上传物资图片
        uploadProductFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            uploadFile(form).then(res => {
                let productFile = {}
                productFile.url = res[0].objectPath
                productFile.name = res[0].objectName
                productFile.isMain = 0
                productFile.relevanceType = 1
                productFile.fileType = 1
                productFile.fileFarId = res[0].recordId
                productFile.imgType = 0
                this.addForm.formData.productFiles.push(productFile)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 上传小图
        uploadMinFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.minFileLength = 1
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 1
                this.addForm.formData.minFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        // 主图删除
        adminFileRemove (file, fileList) {
            this.addForm.adminFileLength = fileList.length
            let recordId = this.addForm.formData.adminFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.adminFile = []
            })
        },
        handleAddressChange2 (params) {
            if (params != null && params.length > 0) {
                let addrStr = []
                params.forEach(e=>{
                    addrStr.push(CodeToText[e])
                })
                this.addForm.formData.regionTableData.filter(item => item.index === this.currentTab)[0].detailAddress = addrStr
                this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
            }
        },
        handleAddressChange3 (index) {
            let regionOption = this.addForm.formData.regionTableData.filter(item => item.index === index)[0]
            let addArr = regionOption.selectAddressOptions
            if (addArr != null && addArr.length > 0) {
                let addrStr = []
                addArr.forEach(e=>{
                    if (e.length === 1) {
                        addrStr.push(CodeToText[e[0]])
                    }else if (e.length === 2) {
                        addrStr.push(CodeToText[e[0]] + CodeToText[e[1]])
                    }
                })
                regionOption.detailAddress = addrStr
                this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
            }
        },
        taxInPriceChange1 (index) {
            let regionOption = this.addForm.formData.currentRegionTableData.filter(item => item.index === index)[0]
            regionOption.taxInPrice = this.fixed2(regionOption.taxInPrice)
            if (this.addForm.formData.taxRate != null) {
                regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
            } else {
                this.$message.error('税率不能为空')
                regionOption.taxInPrice = ''
            }
        },
        payBeforeDeliveryChange (index) {
            let regionOption = this.addForm.formData.currentRegionTableData.filter(item => item.index === index)[0]
            if (this.addForm.formData.annualizedRate == null || this.addForm.formData.annualizedRate == '') {
                regionOption.payBeforeDelivery = ''
                return this.$message.error('年化率不能为空')
            }
            let taxInPrice = regionOption.payBeforeDelivery * (1 + (this.addForm.formData.annualizedRate / 1200 * regionOption.accountPeriod))
            regionOption.taxInPrice = this.fixed2(taxInPrice)
            regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
        },
        getCurrentTab (val) {
            this.currentTab = val
            this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
        },
        // 物资图片删除
        productFileRemove (file) {
            let files = this.addForm.formData.productFiles
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId }).then(res => {
                this.message(res)
                this.addForm.formData.productFiles = newFiles
            })

        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // 小图删除
        minFileRemove (file, fileList) {
            this.addForm.minFileLength = fileList.length
            let recordId = this.addForm.formData.minFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.minFile = []
            })
        },
        //取消
        handleClose () {
            this.$router.replace('/platform/product/shopMaterialManage')
        },
        onChangeTab (e) {
            if ( this.scrollToId === e.name ) {
                return ''
            }
            this.scrollToId = e.name
            const height = document.querySelector('#' + e.name)
            try {
                $('#tabs-content').scrollTo(height.offsetTop, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                productType: 0,
                relevanceName: null,
                productName: null,
                productKeyword: null,
                classId: null,
                classPath: [],
                productMinPrice: null,
                brandId: null,
                brandName: null,
                shopSort: null,
                skuName: null,
                settlePrice: null,
                costPrice: null,
                stock: 1,
                unit: null,
                originalPrice: null,
                sellPrice: null,
                province: null,
                city: null,
                county: null,
                detailedAddress: null,
                productFiles: [],
                adminFile: [],
                minFile: [],
                productDescribe: null
            }
        },
        // 消息提示
        message ({ message, code }) {
            if (code !== 200) return
            this.$message.success(message)
        }
    }
}
</script>

<style lang='scss' scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  appearance: textfield !important;
  -moz-appearance: textfield !important;
}

.warningTabs {
  padding-top: 70px;
}

.e-table {
  min-height: auto;
  background: #fff;
}

.upload {
  margin: 20px auto;
  display: flex;
  justify-content: center;
  text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

/deep/ .el-tabs__content {
  padding-bottom: 20px !important;

  &::-webkit-scrollbar {
    width: 0;
  }
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
  display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
  display: none;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 580px;
    margin-top: 0px;
  }
}
.tabContent {
  margin-left: 9%;
  margin-bottom: 1%;
  border-bottom: 1px solid #cdcdcd;
}
.accountPeriodTabs {
  font-size: 19px;
  display: inline-block;
  margin-right: 2%;
  .tabTitle {
    border-bottom: 5px solid #2e65d9;
    padding: 6px 0;
    color: #2e65d9;
    cursor: pointer;
  }
  .tabTitleNoSelect {
    padding: 6px 0;
    cursor: pointer;
  }
}
.buttonsCDiv {display: inline-block;}
</style>
