// 流程按钮显示控制
import api from './index'
const { getAuditUser, getSubmitUser, getWithdrawUser } = api

/**
 * 通用按钮显示判断逻辑
 * @param {Function} apiFunc 接口函数
 * @param {Array} params 接口参数
 * @param {Array} roleIds 角色ID
 * @returns {boolean} 是否显示
 */
const checkButtonPermission = async (apiFunc, params, roleIds) => {

    if (!Array.isArray(roleIds) || roleIds.length === 0) {
        console.warn('角色列表为空或格式不正确')
        return false
    }

    if (typeof apiFunc !== 'function') {
        console.error('接口函数类型错误')
        return false
    }

    try {
        const res  = await apiFunc(...params)
        console.log('权限检查结果：', res)
        const roleId = res.code == 200 ? '-1'  : res
        return roleIds.includes(roleId)
    } catch (error) {
        console.error('权限检查请求失败', error)
        return false
    }
}

/**
 * 批量获取按钮显示状态
 * @param {string} processId 流程ID
 * @param {string} businessKey 业务ID
 * @param {Object} userInfo 用户信息对象
 * @returns {Object} 包含各按钮显示状态的对象
 */
export const btnIsShow = async (processId, businessKey, userInfo) => {

    if (!userInfo || !userInfo.mallRoleList || !userInfo.mallRoles) {
        console.warn('用户信息不完整，无法获取角色列表')
        return {
            submitBtn: false,
            auditBtn: false,
            withdrawBtn: false
        }
    }

    const roleIds = [...new Set([
        ...userInfo.mallRoleList.map(item => item?.roleId).filter(Boolean),
        ...userInfo.mallRoles.map(item => item?.roleId).filter(Boolean)
    ])]

    const [submitBtn, auditBtn, withdrawBtn] = await Promise.all([
        checkButtonPermission(getSubmitUser, [processId], roleIds),
        checkButtonPermission(getAuditUser, [processId, businessKey], roleIds),
        checkButtonPermission(getWithdrawUser, [processId, businessKey], roleIds)
    ])
    console.log('按钮显示状态：', { submitBtn, auditBtn, withdrawBtn })
    // 返回结果 submitBtn: 提交按钮 auditBtn: 审核按钮 withdrawBtn: 撤回按钮
    return { submitBtn, auditBtn, withdrawBtn }
}
export const submitBtnIsShow = (processId, roleIds) =>
    checkButtonPermission(getSubmitUser, [processId], roleIds)

export const auditBtnIsShow = (processId, businessKey, roleIds) =>
    checkButtonPermission(getAuditUser, [processId, businessKey], roleIds)

export const withdrawBtnIsShow = (processId, businessKey, roleIds) =>
    checkButtonPermission(getWithdrawUser, [processId, businessKey], roleIds)
