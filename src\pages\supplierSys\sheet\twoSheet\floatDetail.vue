<template>
    <div class="e-form">
        <!--浮动-->
        <BillTop @cancel="handleClose"></BillTop>
        <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="对账单详情" name="baseInfo" :disabled="clickTabFlag"/>
                <el-tab-pane label="对账单明细" name="reconciliationDtl" :disabled="clickTabFlag"/>
                <div id="tabs-content">
                    <!-- 基本信息 -->
                    <div id="baseInfo" class="con">
                        <div class="tabs-title" id="baseInfo">对账单详情</div>
                        <el-form :model="reconciliationForm" :rules="reconciliationFormRules" label-width="200px"
                                 ref="reconciliationFormRef" :disabled="false" class="demo-ruleForm">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="对账时间：" prop="startEndTme">
                                        <el-date-picker
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            v-model="reconciliationForm.startEndTme"
                                            type="daterange"
                                            :clearable="false"
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12" v-if="reconciliationForm.startEndTme[1]!=null"
                                        v-show="reconciliationForm.createType==1">
                                    <el-form-item label="供应商名称：" prop="twoSupplierName">
                                        <el-input placeholder="请选择对账供应商" disabled
                                                  v-model="reconciliationForm.twoSupplierName"/>
                                        <el-button size="mini" type="primary" @click="selectContractClick">选择
                                        </el-button>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12" >
                                    <el-form-item label="选择物资：" prop="">
                                        <el-button size="mini"
                                                   :disabled="reconciliationForm.startEndTme == null ||  reconciliationForm.startEndTme.length == 0"
                                                   type="primary"
                                                   @click="selectMaterialBtnClick">选择物资
                                        </el-button>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="税率：" prop="taxRate">
                                        <el-input-number :precision="2" :min=0 :max=100
                                                         style="width: 120px;min-width: 100px;" clearable
                                                         v-model="reconciliationForm.taxRate"
                                                         @change="taxRateChange"/>
                                        %
                                    </el-form-item>
                                </el-col>

                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="含税总金额：" prop="rateAmount">
                                        <span>{{ (Number(reconciliationForm.rateAmount) || 0).toFixed(2) }}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="不含税总金额：" prop="noRateAmount">
                                        <span>{{ (Number(reconciliationForm.noRateAmount) || 0).toFixed(2) }}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                          <el-row>
                            <el-col :span="12">
                              <el-form-item label="税额：" prop="taxAmount">
                                <span>{{ (Number(reconciliationForm.taxAmount) || 0).toFixed(2) }}</span>
                              </el-form-item>
                            </el-col>
                          </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="备注：" prop="remarks">
                                        <el-input
                                            style="width: 1000px;" type="textarea" :auto-resize="false"
                                            v-model="reconciliationForm.remarks"
                                            placeholder="请输入备注" maxlength="1000"
                                            show-word-limit
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <!--计划清单-->
                    <div id="reconciliationDtl" class="con">
                        <div class="tabs-title" id="reconciliationDtl">对账单明细</div>
                        <div class="e-table table-container">
                            <el-table
                                ref="tableRef"
                                border
                                :data="tableData"
                                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                :row-style="{ fontSize: '14px', height: '48px' }"
                            >
                                <el-table-column label="序号" type="index" width="60" fixed="left"/>
                                <el-table-column label="单据日期" prop="receivingDate" width="140">
                                    <template v-slot="scope">
                                        <span>{{ scope.row.receivingDate.split(' ')[0] }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="类型" width="120" fixed="left">
                                    <template v-slot="scope">
                                        <el-tag type="danger" v-show="scope.row.reconciliationType===2">退货对账
                                        </el-tag>
                                        <el-tag type="success" v-show="scope.row.reconciliationType===1">发货对账
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="orderSn" label="订单号" width="220"/>
                                <el-table-column prop="materialName" label="物资名称" width="180"/>
                                <!-- <el-table-column prop="productName" label="商品名称" width="200"/> -->
                                <el-table-column prop="texture" label="材质" width="140"/>
                                <el-table-column prop="spec" label="规格型号" width="140"/>
                                <el-table-column prop="unit" label="单位"/>
                                <el-table-column prop="quantity" label="对账数量" width="140">
                                    <template v-slot="scope">
                                        <span>{{ Math.floor(Number(scope.row.quantity) || 0) }}</span>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="price" label="含税单价" width="120px">
                                    <template v-slot="scope">
                                        <span>{{ (Number(scope.row.price) || 0).toFixed(2) }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="noRatePrice" label="不含税单价" width="120px">
                                    <template v-slot="scope">
                                        <span>{{ (Number(scope.row.noRatePrice) || 0).toFixed(2) }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="totalAmount" label="含税金额" width="120px">
                                    <template v-slot="scope">
                                        <span>{{ (Number(scope.row.totalAmount) || 0).toFixed(2) }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="noRateAmount" label="不含税金额" width="120px">
                                    <template v-slot="scope">
                                        <span>{{ (Number(scope.row.noRateAmount) || 0).toFixed(2) }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="taxAmount" label="税额" width="120px">
                                    <template v-slot="scope">
                                        <span>{{ (Number(scope.row.taxAmount) || 0).toFixed(2) }}</span>
                                    </template>
                                </el-table-column>

                                <!--                                    <el-table-column prop="remarks" label="备注" >-->
                                <!--                                        <template v-slot="scope">-->
                                <!--                                            <el-input-->
                                <!--                                                v-model="scope.row.remarks"-->
                                <!--                                                @change="getChangedRow(scope.row)">-->
                                <!--                                            </el-input>-->
                                <!--                                        </template>-->
                                <!--                                    </el-table-column>-->
                            </el-table>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button type="primary" @click="saveSheetM">保存</el-button>
            <el-button type="primary" class="btn-greenYellow" @click="saveAndSubmitSheetM">保存并提交</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
        <el-dialog
            v-dialogDrag custom-class="dlg" width="90%" title="物资选择"
            :visible.sync="selectMaterialShow"
        >
            <div class="box-left">
                <div class="e-table">
                    <div class="top">
                        <div style="width: 200px">
                            <el-input type="text" @blur="getTwoMaterialOrderListM" placeholder="输入搜索关键字"
                                      v-model="keywords2">
                                <img :src="require('@/assets/search.png')" slot="suffix"
                                     @click="getTwoMaterialOrderListM"/>
                            </el-input>
                        </div>
                    </div>
                    <el-table ref="siteReceivingTableRefL"
                              border
                              max-height="340px"
                              @selection-change="siteReceivingTableSelectL"
                              @row-click="siteReceivingTableRowClickL"
                              :data="contractOrderTableDate"
                              v-loading="selectContractOrderLoading"
                              class="table"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>
                        <el-table-column prop="orderSn" label="订单号" width=""></el-table-column>
                        <el-table-column prop="gmtCreate" label="创建日期" width="">
                            <template v-slot="scope">
                                {{ scope.row.gmtCreate }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <Pagination
                        v-if="contractOrderTableDate != null && contractOrderTableDate.length > 0"
                        :total="paginationInfo.total"
                        :pageSize.sync="paginationInfo.pageSize"
                        :currentPage.sync="paginationInfo.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </div>
            </div>
            <div class="box-right">
                <div class="e-table table-container">
                    <div class="top">
                        <div style="width: 200px">
                            <el-input type="text" @blur="getReconciliationDtlListM2" placeholder="输入搜索关键字"
                                      v-model="keywords3">
                                <img :src="require('@/assets/search.png')" slot="suffix"
                                     @click="getReconciliationDtlListM2"/>
                            </el-input>
                        </div>
                    </div>
                    <el-table ref="siteReceivingTableRefR"
                              v-loading="siteReceivingLoading"
                              border
                              max-height="390px"
                              @selection-change="siteReceivingTableRowClickR"
                              @row-click="siteReceivingTableSelectR"
                              :data="siteReceivingTableDate"
                              class="table"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60" fixed="left"/>
                        <el-table-column label="单据日期" prop="receivingDate" width="160">
                            <template v-slot="scope">
                                <span>{{ scope.row.receivingDate.split(' ')[0] }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="类型" width="120" fixed="left">
                            <template v-slot="scope">
                                <el-tag type="danger" v-show="scope.row.reconciliationType===2">退货对账
                                </el-tag>
                                <el-tag type="success" v-show="scope.row.reconciliationType===1">发货对账
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderSn" label="订单号" width="220"/>
                        <el-table-column prop="materialName" label="物资名称" width="200"/>
                        <!-- <el-table-column prop="productName" label="商品名称" width="200"/> -->
                        <el-table-column prop="spec" label="规格型号"/>
                        <el-table-column prop="unit" label="单位"/>
                        <el-table-column prop="quantity" label="对账数量">
                            <template v-slot="scope">
                                <span>{{ scope.row.quantity }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="price" label="含税单价" width="120px">
                            <template v-slot="scope">
                                <span>{{ (Number(scope.row.price) || 0).toFixed(2) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="noRatePrice" label="不含税单价" width="120px">
                            <template v-slot="scope">
                                <span>{{ (Number(scope.row.noRatePrice) || 0).toFixed(2) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="totalAmount" label="含税金额" width="120px">
                            <template v-slot="scope">
                                <span>{{ (Number(scope.row.totalAmount) || 0).toFixed(2) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="noRateAmount" label="不含税金额" width="120px">
                            <template v-slot="scope">
                                <span>{{ (Number(scope.row.noRateAmount) || 0).toFixed(2) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="taxRate" label="税率" width="80px">
                            <template v-slot="scope">
                                <span>{{ scope.row.taxRate }}%</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="taxAmount" label="税额" width="120px">
                            <template v-slot="scope">
                                <span>{{ (Number(scope.row.taxAmount) || 0).toFixed(2) }}</span>
                            </template>
                        </el-table-column>
                        <!--                            <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
                    </el-table>
                    <Pagination
                        v-show="siteReceivingTableDate != null && siteReceivingTableDate.length > 0"
                        :total="paginationInfo2.total"
                        :pageSize.sync="paginationInfo2.pageSize"
                        :currentPage.sync="paginationInfo2.currentPage"
                        @currentChange="currentChangeUser2"
                        @sizeChange="sizeChangeUser2"
                    />
                </div>
            </div>
            <span slot="footer">
                <el-button type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认选择</el-button>
                <el-button @click="selectMaterialShow = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog v-dialogDrag :title="selectTwoSupplierTableTitle" :visible.sync="showTwoSupplier" width="80%"
                   style="margin-left: 10%;" :close-on-click-modal="false">
            <div class="e-table" v-loading="selectTwoSupplierLoading">
                <div class="top dfa" style="height: 50px; padding-left: 10px">
                    <el-input style="width: 200px; " type="text" @blur="getTwoSupplierListM"
                              placeholder="输入搜索关键字" v-model="keywords">
                        <img :src="require('@/assets/search.png')" slot="suffix" @click="getTwoSupplierListM"/>
                    </el-input>
                </div>
                <el-table
                    ref="selectTwoSupplierR"
                    border
                    @selection-change="selectTwoSupplierSelectM"
                    :data="selectTwoSupplierDate"
                    class="table"
                    :max-height="$store.state.tableHeight"
                >
                    <!--<el-table-column type="selection" width="40"></el-table-column>-->
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="操作">
                        <template v-slot="scope">
                            <div class="pointer" style="color: rgba(33, 110, 198, 1);"
                                 @click="twoSupplierRowClick(scope.row)">选择供应商
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="供应商企业名称" prop="supplierName"/>
                </el-table>
            </div>
            <!--分页-->
            <span slot="footer">
                <Pagination
                    :total="paginationInfo.total"
                    :pageSize.sync="paginationInfo.pageSize"
                    :currentPage.sync="paginationInfo.currentPage"
                    @currentChange="currentChangeUser"
                    @sizeChange="sizeChangeUser"
                />
            </span>
        </el-dialog>
    </div>
</template>
<script>
import $ from 'jquery'
import {  toFixed, calculateNotTarRateAmount, calculateNotTarRateAmountFour } from '@/utils/common'
import '@/utils/jquery.scrollTo.min'
import { getUuid, throttle } from '@/utils/common'
import {
    ReconciliationSupplierCreate,
    getReconciliationDtlList,
    getHaveTwoMaterialOrderList
} from '@/api/reconciliation/twoReconciliation'
import { getList } from '@/api/shopManage/shop/shopSupplierRele'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import {  capitalsAndNum, taxRateItemAmount } from '@/utils/material_reconciliationUtils/compute'
import { twoReconciliationAmountM, twoReconciliationFixAmountM } from '@/utils/material_reconciliationUtils/twoCompute'

export default {
    components: {
        Pagination
    },
    data () {
        return {
            productTypeDis: false,
            formLoading: false,
            reconciliationFormRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                taxRate: [
                    { required: true, message: '请输入公司税率', trigger: 'blur' },
                    { validator: this.validateNumber, trigger: 'blur' }
                ],
            },
            siteReceivingTableSelectRowData: [],
            selectTwoSupplierRowDate: [],
            siteReceivingTableDate: [],
            siteReceivingLoading: false,
            showSiteReceivingDia: false,

            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            selectTwoSupplierTableTitle: null,
            selectTwoSupplierLoading: false,
            showTwoSupplier: false,
            selectTwoSupplierDate: [],
            contractOrderTableDate: [],
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            orderIds: [],
            reconciliationForm: {
                billId: null,
                billNo: null,
                orderId: null,
                orderSn: null,
                businessType: 2,
                twoSupplierOrgId: null,
                twoSupplierName: null,
                supplierName: null,
                supplierOrgId: null,
                supplierEnterpriseId: null,
                noRateAmount: null,
                rateAmount: null,
                totalAmount: null,
                otherNoRateAmount: null,
                otherRateAmount: null,
                otherTotalAmount: null,
                startTime: null,
                endTime: null,
                startEndTme: [],
                createType: 2,
                twoSupplierIsAffirm: 0,
                twoSupplierAffirmTime: null,
                supplierAffirmTime: null,
                supplierIsAffirm: null,
                taxRate: null,
                // 内外供应商信息
                creditCode: null,
                orgShort: null,
                dtl: [],
                // TODO 待完善

            },
            showReconciliationForm: false,
            reconciliationFormLoading: false,
            selectMaterialShow: false,
            maxNum: *********,
            tableData: [],
            selectContractOrPlanTableTitle: '',
            keywords: '',  //供应商名称查询
            keywords2: null, //订单编号查询
            keywords3: '',   //可对帐的订单编号和商品名称查询查询
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            selectContractOrderLoading: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            selectContractOrderDate: false,
            showSelectContractOrPlan: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: [],
            checkTimeNow: 0, // 选中时的时间
        }
    },
    created () {
        this.reconciliationForm.createType = this.$route.query.createType
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
        'reconciliationForm.taxRate': {
            handler (value) {
                let prams = twoReconciliationAmountM(this.tableData, value)
                this.tableData = prams.tableData
                this.reconciliationForm.taxAmount = prams.taxAmount
                this.reconciliationForm.rateAmount = prams.reconciliationAmount
                this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
            },
        }
    },
    methods: {
        selectMaterialBtnClick () {
            this.siteReceivingTableDate = []
            this.selectMaterialShow = true
            this.getTwoMaterialOrderListM()
        },
        siteReceivingTableSelectL (value) {
            let nowTime = new Date().getTime()
            if(nowTime - this.checkTimeNow < 200) {
                this.checkTimeNow = nowTime
                return false
            }
            let billNoArr = []
            for(let itemI of value) {
                if(billNoArr.indexOf(itemI.supplierName) == -1) {
                    billNoArr.push(itemI.supplierName)
                }
            }
            if(billNoArr.length > 1) {
                if(!this.dataListSelections.length) {
                    this.$refs.siteReceivingTableRefL.clearSelection()
                }else {
                    let supplierName = this.dataListSelections[0].supplierName
                    this.$nextTick(() => {
                        this.contractOrderTableDate.forEach(item => {
                            if(supplierName != item.supplierName ) {
                                this.$refs.siteReceivingTableRefL.toggleRowSelection(item, false)
                            }
                        })
                    })
                }
                this.checkTimeNow = new Date().getTime()
                return this.$message.warning('只能选择同一个供应商的数据')
            }
            this.selectContractOrderDate = value
            if (this.selectContractOrderDate.length > 0) {
                this.orderIds = this.selectContractOrderDate.map(item=>item.parentOrderId)
                this.getReconciliationDtlListM2()
            }else {
                this.siteReceivingTableDate = []
            }

        },
        siteReceivingTableRowClickL (selectRow) {
            selectRow.flag = !selectRow.flag
            this.$refs.siteReceivingTableRefL.toggleRowSelection(selectRow, selectRow.flag)
        },
        getReconciliationDtlListM2 () {
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                twoSupplierOrgId: this.reconciliationForm.twoSupplierOrgId,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                userType: 1,
                productType: 10
            }
            if (this.keywords3 != null) {
                params.keywords = this.keywords3
            }
            if (this.orderIds != null && this.orderIds.length > 0) {
                params.orderIds = this.orderIds
            }else {
                return
            }
            if(this.$refs.siteReceivingTableRefR) {this.$refs.siteReceivingTableRefR.clearSelection()}
            let _this = this
            getReconciliationDtlList(params).then(res => {
                this.siteReceivingTableDate = res.list
                _this.$nextTick(() => {
                    _this.siteReceivingTableDate.forEach(item => {
                        _this.$refs.siteReceivingTableRefR.toggleRowSelection(item, true)
                    })
                })
                let prams = twoReconciliationFixAmountM(this.tableData, this.reconciliationForm.taxRate)
                this.tableData = prams.tableData
                this.reconciliationForm.taxAmount = prams.taxAmount
                this.reconciliationForm.rateAmount = prams.reconciliationAmount
                this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
            })
        },
        // 获取获取对应企业的订单货发货单数据数据
        getTwoMaterialOrderListM () {
            if (this.reconciliationForm.twoSupplierName == null || this.reconciliationForm.twoSupplierName == '') {
                this.$message.warning('请先选择供应商名称')
                this.selectMaterialShow = false
                return
            }

            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                twoSupplierOrgId: this.reconciliationForm.twoSupplierOrgId,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                productType: 0
            }
            if (this.keywords2 != null) {
                params.keywords = this.keywords2
            }
            params.billType = 2
            if(this.$refs.siteReceivingTableRefL) {this.$refs.siteReceivingTableRefL.clearSelection()}
            this.selectContractOrderLoading = true
            getHaveTwoMaterialOrderList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.contractOrderTableDate = res.list
                this.selectContractOrderLoading = false
            }).finally(() => {
                this.selectContractOrderLoading = false
            })
        },
        validateNumber (rule, value, callback) {
            if (value < 0 || value >= 100) {
                callback(new Error('供应商税率不能超过100'))
            } else {
                callback()
            }
        },
        selectContractClick () {
            this.selectTwoSupplierTableTitle = '选择供应商'
            this.getTwoSupplierListM()
            this.showTwoSupplier = true
        },
        selectTwoSupplierSelectM (value) {
            this.selectTwoSupplierRowDate = value
        },
        //选择供应商
        twoSupplierRowClick (row) {
            this.reconciliationForm.twoSupplierName = row.supplierName
            this.reconciliationForm.twoSupplierOrgId = row.supplierId
            this.reconciliationForm.taxRate = row.taxRate
            this.showTwoSupplier = false

        },
        //之前代码，选择供应商
        // twoSupplierRowClick (row) {
        //     this.reconciliationForm.twoSupplierName = row.supplierName
        //     this.reconciliationForm.twoSupplierOrgId = row.supplierId
        //     this.reconciliationForm.taxRate = row.taxRate
        //     this.showTwoSupplier = false
        //     this.getReconciliationDtlListM()
        // },
        //查询全部订单
        getReconciliationDtlListM () {
            let params = {
                twoSupplierOrgId: this.reconciliationForm.twoSupplierOrgId,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                userType: 1,
                limit: 100,
                page: 1,
                sourceType: 2

            }
            if (this.orderIds != null && this.orderIds.length > 0) {
                params.orderIds = this.orderIds
            }
            getReconciliationDtlList(params).then(res => {
                this.tableData = res.list
                let prams = twoReconciliationAmountM(this.tableData, this.reconciliationForm.taxRate)
                this.tableData = prams.tableData
                this.reconciliationForm.taxAmount = prams.taxAmount
                this.reconciliationForm.rateAmount = prams.reconciliationAmount
                this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
            })
        },

        getReconciliationDtlList2M () {
            let params = {
                twoSupplierOrgId: this.reconciliationForm.twoSupplierOrgId,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                userType: 1,
                limit: 100,
                page: 1,
                sourceType: 2

            }
            getReconciliationDtlList(params).then(res => {
                this.tableData = res.list
                let taxAmount = 0
                let totalAmount = 0
                let noRateAmount = 0
                this.tableData.forEach(item => {
                    let prams = taxRateItemAmount(item.price, item.quantity, this.reconciliationForm.taxRate)
                    item.noRatePrice = prams.noRatePrice
                    item.taxAmount = prams.taxAmount
                    item.totalAmount = prams.acceptanceAmount
                    item.noRateAmount =  prams.acceptanceNoRateAmount
                    taxAmount = this.fixed2(Number(taxAmount) + Number(item.taxAmount))
                    totalAmount.fixed2(Number(totalAmount) + Number(item.totalAmount))
                    noRateAmount = this.fixed2(Number(noRateAmount) + Number(item.noRateAmount))
                })
                this.reconciliationForm.rateAmount = totalAmount
                this.reconciliationForm.noRateAmount = capitalsAndNum(totalAmount, noRateAmount, this.reconciliationForm.taxRate )
            })
        },
        siteReceivingTableRowClickR (value) {
            this.siteReceivingTableSelectRowData = value
        },
        siteReceivingTableSelectR (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRefR.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableDateSelectAffirmClick () {
            // 验证所有物资的税率是否相同
            if (this.siteReceivingTableDate.length > 0) {
                let firstTaxRate = this.siteReceivingTableDate[0].taxRate
                let differentTaxRates = new Set()

                this.siteReceivingTableDate.forEach(item => {
                    if (Number(item.taxRate) !== Number(firstTaxRate)) {
                        differentTaxRates.add(item.taxRate)
                    }
                })

                if (differentTaxRates.size > 0) {
                    let allTaxRates = new Set([firstTaxRate, ...differentTaxRates])
                    let taxRateList = Array.from(allTaxRates).join('%、') + '%'
                    this.$message.error(`税率不同物资无法合并对账，当前选择的物资包含税率：${taxRateList}，请选择相同税率的物资`)
                    return
                }

                // 从物资选择弹框中获取税率
                // 优先使用后台返回的税率，如果没有则使用默认值
                if (firstTaxRate != null && firstTaxRate !== '') {
                    this.reconciliationForm.taxRate = Number(firstTaxRate)
                } else if (!this.reconciliationForm.taxRate) {
                    this.reconciliationForm.taxRate = 13 // 默认税率13%
                }
            }

            this.selectMaterialShow = false

            // 确保含税单价有效
            this.siteReceivingTableDate.forEach(item => {
                // 确保含税单价不小于0
                if (!item.price || item.price < 0) {
                    item.price = 0
                }

                // 重新计算该行的所有金额字段
                this.calculateRowAmounts(item)
            })

            this.tableData = this.siteReceivingTableDate
            // 使用专门的二级供应商对账单计算方法
            let prams = twoReconciliationAmountM(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = prams.tableData
            this.reconciliationForm.taxAmount = prams.taxAmount
            this.reconciliationForm.rateAmount = prams.reconciliationAmount
            this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
        },
        currentChangeUser2 (currPage) {
            this.paginationInfo2.currentPage = currPage
            this.getSiteReceivingTableDateM()
        },
        sizeChangeUser2 (pageSize) {
            this.paginationInfo2.pageSize = pageSize
            this.getSiteReceivingTableDateM()
        },
        currentChangeUser (currPage) {
            this.paginationInfo.currentPage = currPage
            this.getTwoSupplierListM()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo.pageSize = pageSize
            this.getTwoSupplierListM()
        },
        //获取二级供应商列表
        getTwoSupplierListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            this.selectTwoSupplierLoading = true
            getList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.selectTwoSupplierDate = res.list
            }).finally(() => {
                this.selectTwoSupplierLoading = false
            })
        },
        saveSheetM () {
            if (this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.price == 0.00) {
                    return this.$message.error('对账含税单价不能为0！')
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要保存吗！', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        ReconciliationSupplierCreate(this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('保存成功')
                                this.$router.go(-1)
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        saveAndSubmitSheetM () {
            if (this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.price == 0.00) {
                    return this.$message.error('对账含税单价不能为0！')
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要保存并提交吗！', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        this.reconciliationForm.state = 2
                        ReconciliationSupplierCreate(this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.$router.go(-1)
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        // 选择计划
        selectPlanClick () {
            this.selectTwoSupplierTableTitle = '选择计划'
            this.getTwoSupplierListM()
            this.showTwoSupplier = true
        },

        // 删除单
        deleteM (row) {
            this.tableData = this.tableData.filter(t => {
                if (t.uuid != row.uuid) {
                    return true
                } else {
                    return false
                }
            })
        },
        // 拆单
        dismantleM (row) {
            // 插入到当前点击的下一个节点
            let insertIndex = this.tableData.length
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.uuid == row.uuid) {
                    insertIndex = i + 1
                }
            }
            let newRow = {
                ...row,
                acceptanceAmount: this.fixed2(0),
                quantity: 0,
                uuid: getUuid()
            }
            this.tableData.splice(insertIndex, 0, newRow)
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 处理固定费用
        disposeFixationPriceM (row) {
            if (row.fixationPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.fixationPrice = this.fixed2(0)
            } else {
                row.fixationPrice = this.fixed2(row.fixationPrice)
            }
        },
        // 处理到货网价
        disposeFreightPriceM (row) {
            if (row.freightPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.freightPrice = this.fixed2(0)
            } else {
                row.freightPrice = this.fixed2(row.freightPrice)
            }
        },
        // 处理数量
        disposeQuantityM (row) {
            if (row.quantity <= 0) {
                return row.quantity = this.fixed4(0)
            }
            // 计算最大值
            let maxNum = this.fixed4(row.maxQuantity)
            let countNum = 0
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.materialName === row.materialName && t.spec === row.spec && t.unit == row.unit && t.uuid !== row.uuid) {
                    countNum++
                    if (maxNum <= 0) {
                        maxNum = 0
                        continue
                    }
                    maxNum = maxNum - t.quantity
                }
            }
            // 如果一次没添加，则表示操作的第一个
            if (countNum == 0) {
                maxNum = row.maxQuantity
            }
            if (row.quantity >= maxNum) {
                row.quantity = this.fixed4(maxNum)
            } else {
                row.quantity = this.fixed4(row.quantity)
            }
        },
        // 计算单行的所有金额字段 - 使用与后端一致的计算方法
        calculateRowAmounts (row) {
            let price = Number(row.price) || 0
            let quantity = Number(row.quantity) || 0
            let taxRate = Number(this.reconciliationForm.taxRate) || 13

            if (price > 0 && quantity > 0 && taxRate >= 0) {
                // 使用与后端一致的计算方法
                // 计算含税金额
                row.totalAmount = this.fixed2(price * quantity)

                // 计算不含税单价 - 使用calculateNotTarRateAmount方法
                row.noRatePrice = calculateNotTarRateAmount(price, taxRate)

                // 计算不含税金额 - 使用calculateNotTarRateAmount方法（与后端一致）
                row.noRateAmount = calculateNotTarRateAmount(row.totalAmount, taxRate)

                // 计算税额 - 使用calculateNotTarRateAmountFour方法
                row.taxAmount = this.fixed2(Number(calculateNotTarRateAmountFour(row.totalAmount, taxRate)) * Number(taxRate / 100))
            } else {
                row.totalAmount = 0
                row.noRatePrice = 0
                row.noRateAmount = 0
                row.taxAmount = 0
            }
        },

        // 数量变化处理
        getChangedRow (row) {
            // 一旦变化则是1
            row.updateType = 1
            // 处理数量
            this.disposeQuantityM(row)
            // 重新计算该行的所有金额字段
            this.calculateRowAmounts(row)
            // 使用专门的二级供应商对账单计算方法重新计算总金额
            let prams = twoReconciliationAmountM(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = prams.tableData
            this.reconciliationForm.taxAmount = prams.taxAmount
            this.reconciliationForm.rateAmount = prams.reconciliationAmount
            this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 税率变化处理
        taxRateChange () {
            if (this.reconciliationForm.taxRate == null || this.reconciliationForm.taxRate < 0) {
                this.reconciliationForm.taxRate = this.fixed2(0)
            } else if (this.reconciliationForm.taxRate >= 100) {
                this.reconciliationForm.taxRate = 100
            } else {
                this.reconciliationForm.taxRate = this.fixed2(this.reconciliationForm.taxRate)
            }

            // 使用专门的二级供应商对账单计算方法重新计算所有金额
            let prams = twoReconciliationAmountM(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = prams.tableData
            this.reconciliationForm.taxAmount = prams.taxAmount
            this.reconciliationForm.rateAmount = prams.reconciliationAmount
            this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        }
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliationDtl']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/* 表格水平滚动支持 */
.table-container {
    overflow-x: auto;
    width: 100%;
}

.table-container .el-table {
    min-width: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
    appearance: textfield !important;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}

/deep/ .el-dialog.dlg {
    height: 600px;

    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 474px;
        margin: 10px;
        display: flex;

        & > div {
            .e-pagination {
                background-color: unset;
            }

            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }

        .e-table {
            flex-grow: 1;

            .table {
                height: 100%;
            }
        }

        .box-left {
            width: 660px;
            display: flex;
            flex-direction: column;

            .top {
                box-shadow: unset;
            }
        }

        .box-right {
            flex-grow: 1;
            display: flex;
            flex-direction: column;

            & > div {
                display: flex;
                flex-direction: column;
            }

            .top {
                justify-content: left;
                border-radius: 0;
                box-shadow: unset;
            }

            .bottom {
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}
</style>