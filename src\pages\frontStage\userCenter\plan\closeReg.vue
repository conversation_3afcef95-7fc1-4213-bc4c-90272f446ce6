<template>
    <div class="df">
        <div class="boxBottom">
            <div class="icon center"></div>
            <div class="msg1">订单提交成功</div>
            <button class="center" style="margin-top: 60px" @click="$router.go(orderAll?-3:-2)">返回</button>
        </div>
        <el-dialog v-dialogDrag title="失败的订单" :visible.sync="shopDialog" width="80%"
                   style="margin-left: 10%;" :close-on-click-modal="false">
            <div class="e-table" style="background-color: #fff">
                <el-table ref="tableRef"
                          border
                          style="width: 100%"
                          :data="errorOrderVO"
                          class="table"
                          :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="shopName" label="店铺名称" width="200"></el-table-column>
                    <el-table-column prop="cause" label="失败原因" width=""></el-table-column>
                </el-table>
                <button class="returnButt" style="margin-top: 60px" @click="$router.go(-2)">返回2</button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props: ['errorOrderVO', 'shopDialog', 'orderAll'],
    data () {
        return {
        }
    },
    watch: {
        errorOrderVO (value) {
            console.log(value)
            if (value != null && value.length != 0) {
                this.shopDialog = true
            }
        },
    },
    computed: {

    },
    methods: {

    }
}
</script>
<style scoped lang="scss">
.df {
    flex-direction: column;
}
.boxTop {
    height: 87px;
    border-bottom: 1px solid #D9D9D9;
    .title {
        width: 200px;
        height: 100%;
        font-size: 26px;
        font-weight: 500;
        line-height: 87px;
        text-align: center;
        border-bottom: 4px solid #216EC6;
        color: #333;
        user-select: none;
    }
}
.returnButt {
    margin-left: 40%;
    width: 120px;
    height: 40px;
    font-size: 22px;
    color: #fff;
    background-color: #216EC6;
}
.boxBottom {
    text-align: center;
    flex-grow: 1;
    .icon {
        width: 100px;
        height: 100px;
        margin-top: 120px;
        background: url(../../../../assets/images/userCenter/zc_chenggong.png);
    }
    .msg1 {
        margin: 30px 0 15px 0;
        font-size: 22px;
        color: #333;
        text-align: center;
        font-weight: 400;
    }
    .msg2 {
        margin-bottom: 72px;
        font-size: 16px;
        text-align: center;
        color: #999;
    }
    button {
        width: 150px;
        height: 50px;
        font-size: 22px;
        color: #fff;
        background-color: #216EC6;
    }
}
</style>