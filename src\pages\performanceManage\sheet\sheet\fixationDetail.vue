<!-- 固定价格对账-新增 -->
<template>
    <div class="base-page">
        <div class="e-form">
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" style="padding-top: 70px;">
                <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab" v-loading="formLoading">
                    <el-tab-pane label="对账单详情" name="baseInfo" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="对账单明细" name="reconciliationDtl" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <div id="tabs-content">
                        <!-- 基本信息 -->
                        <div id="baseInfo" class="con">
                            <div class="tabs-title" id="baseInfo">对账单详情</div>
                            <el-form :model="reconciliationForm" :rules="reconciliationFormRules"  label-width="200px" ref="reconciliationFormRef" :disabled="false" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="业务类型：" prop="reconciliationProductType">
                                            <el-radio v-model="reconciliationForm.reconciliationProductType" :label="0" :disabled="productTypeDis">零星采购</el-radio>
                                            <el-radio v-model="reconciliationForm.reconciliationProductType" :label="1" :disabled="productTypeDis">大宗临购</el-radio>
                                            <el-radio v-model="reconciliationForm.reconciliationProductType" :label="2" :disabled="productTypeDis">周转材料</el-radio>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="对账时间：" prop="startEndTme">
                                            <el-date-picker
                                                value-format="yyyy-MM-dd"
                                                v-model="reconciliationForm.startEndTme"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="供货单位：" prop="supplierName">
                                            <el-input placeholder="请选择供货单位" disabled
                                                      v-model="reconciliationForm.supplierName"/>
                                            <el-button
                                                :disabled="reconciliationForm.reconciliationProductType == null || reconciliationForm.startEndTme == null ||  reconciliationForm.startEndTme.length == 0"
                                                size="mini" type="primary" @click="selectedEnterprise">选择供货单位
                                            </el-button>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="选择物资：" prop="">
                                            <el-button size="mini"
                                                       :disabled="!reconciliationForm.supplierName || reconciliationForm.reconciliationProductType == null || reconciliationForm.startEndTme == null ||  reconciliationForm.startEndTme.length == 0"
                                                       type="primary"
                                                       @click="selectMaterialBtnClick">选择物资</el-button>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12" v-if="reconciliationForm.reconciliationProductType === 12">
                                        <el-form-item label="合同编号：" prop="sourceBillNo">
                                            <span>{{reconciliationForm.sourceBillNo}}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" v-if="reconciliationForm.reconciliationProductType === 10">
                                        <el-form-item label="计划编号：" prop="sourceBillNo">
                                            <span>{{reconciliationForm.sourceBillNo}}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收货单位：" prop="purchasingOrgName">
                                            <span>{{reconciliationForm.purchasingOrgName}}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：" prop="reconciliationAmount">
                                            <span>{{ (Number(reconciliationForm.reconciliationAmount) || 0).toFixed(2) }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="不含税总金额：" prop="reconciliationNoRateAmount">
                                            <span>{{ (Number(reconciliationForm.reconciliationNoRateAmount) || 0).toFixed(2) }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账类型：" prop="type">
                                            <el-tag v-if="reconciliationForm.type == 1">浮动价格对账</el-tag>
                                            <el-tag v-if="reconciliationForm.type == 2">固定价格对账</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="税率（%）：" prop="taxRate">
                                            <el-input
                                                type="number"
                                                v-model="reconciliationForm.taxRate"
                                                @change="taxRateChange()">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="税额：" prop="taxAmount">
                                    <span>{{ (Number(reconciliationForm.taxAmount) || 0).toFixed(2) }}</span>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注：" prop="remarks">
                                            <el-input   type="textarea" :auto-resize="false" v-model="reconciliationForm.remarks"
                                                      placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <!--计划清单-->
                        <div id="reconciliationDtl" class="con">
                            <div class="tabs-title" id="reconciliationDtl">对账单明细</div>
                            <div class="e-table table-container"  style="background-color: #fff" >
                                <el-table
                                    ref="tableRef"
                                    border
                                    style="width: 100%"
                                    :data="tableData"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column label="序号" type="index" width="60"  fixed="left"></el-table-column>
                                    <el-table-column prop="receivingDate" label="收料日期" width="130">
                                        <template v-slot="scope">
                                            {{ scope.row.receivingDate | dateStr }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="120" fixed="left"  v-if="reconciliationForm.reconciliationProductType == 12">
                                        <template slot-scope="scope">
                                            <span class="pointer" style="color: rgba(33, 110, 198, 1); margin-left: 20px" @click="dismantleM(scope.row)">拆单</span>
                                            <span class="pointer" style="color: rgb(176,5,5); margin-left: 20px" @click="deleteM(scope.row)">删除</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="orderSn" label="订单号" width="220"/>
                                    <el-table-column prop="materialName" label="物资名称" width="200"/>
                                    <el-table-column prop="spec" label="规格型号" width=""/>
                                    <el-table-column prop="unit" label="单位" width=""/>
                                    <el-table-column prop="texture" label="材质" width=""/>
                                    <el-table-column prop="maxQuantity" label="可选数量" width="70"/>
                                    <el-table-column prop="quantity" label="已选数量" width="100" >
                                        <template v-slot="scope">
                                            <span>{{ scope.row.quantity }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="price" label="含税单价" width="100" >
                                        <template v-slot="scope">
                                            <span>{{ (Number(scope.row.price) || 0).toFixed(2) }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="noRatePrice" label="不含税单价" width="120px">
                                        <template v-slot="scope">
                                            <span>{{ (Number(scope.row.noRatePrice) || 0).toFixed(2) }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="acceptanceAmount" label="含税金额" width="120px">
                                        <template v-slot="scope">
                                            <span>{{ (Number(scope.row.acceptanceAmount) || 0).toFixed(2) }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="acceptanceNoRateAmount" label="不含税金额" width="120px">
                                        <template v-slot="scope">
                                            <span>{{ (Number(scope.row.acceptanceNoRateAmount) || 0).toFixed(2) }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="taxAmount" label="税额" width="120px">
                                        <template v-slot="scope">
                                            <span>{{ (Number(scope.row.taxAmount) || 0).toFixed(2) }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="remarks" label="备注" width="" >
                                        <template v-slot="scope">
                                            <el-input
                                                v-model="scope.row.remarks">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </el-tabs>
            </div>
            <div class="buttons">
                <el-button type="primary" @click="saveSheetM(0)">保存</el-button>
                <el-button type="primary" v-if="userInfo.roles.includes('物资对账提交权限')" @click="saveSheetM(1)">保存并提交</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
            <el-dialog
                v-dialogDrag custom-class="dlg" width="90%" title="物资选择"
                :visible.sync="selectMaterialDialogShow"
            >
                <div class="box-left">
                    <div class="e-table">
                        <div class="top">
                            <div style="width: 200px">
                                <el-input  type="text" @blur="getContractOrPlanListM" placeholder="输入搜索关键字" v-model="keywords">
                                    <img :src="require('@/assets/search.png')" slot="suffix" @click="getContractOrPlanListM" />
                                </el-input>
                            </div>
                        </div>
                        <el-table ref="siteReceivingTableRefL"
                                  border
                                  max-height="340px"
                                  @selection-change="siteReceivingTableSelectL"
                                  @row-click="siteReceivingTableRowClickL"
                                  :data="contractOrPlanTableDate"
                                  v-loading="selectContractOrPlanLoading"
                                  class="table"
                        >
                            <el-table-column type="selection" header-align="center" align="center" width="50" :reserve-selection="true"></el-table-column>
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="billNo" label="计划编号" width="150"></el-table-column>
                            <el-table-column prop="orderSn" label="订单编号" width="150"></el-table-column>
                            <el-table-column prop="gmtCreate" label="创建日期" width="150">
                                <template v-slot="scope">
                                    {{ scope.row.gmtCreate | dateStr }}
                                </template>
                            </el-table-column>
                        </el-table>
                        <Pagination
                            v-if="contractOrPlanTableDate != null && contractOrPlanTableDate.length > 0"
                            :total="paginationInfo.total"
                            :pageSize.sync="paginationInfo.pageSize"
                            :currentPage.sync="paginationInfo.currentPage"
                            @currentChange="currentChangeUser"
                            @sizeChange="sizeChangeUser"
                        />
                    </div>
                </div>
                <div class="box-right">
                    <div class="e-table">
                        <div class="top" >
                            <div style="width: 200px">
                                <el-input type="text" @blur="getSiteReceivingTableDateM" placeholder="输入搜索关键字" v-model="keywords2">
                                    <img :src="require('@/assets/search.png')" slot="suffix" @click="getSiteReceivingTableDateM" />
                                </el-input>
                            </div>
                            <el-date-picker
                                :clearable="false"
                                style="margin-left: 20px"
                                @change="getSiteReceivingTableDateM"
                                value-format="yyyy-MM-dd"
                                v-model="reconciliationForm.startEndTme"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </div>
                        <el-table ref="siteReceivingTableRefR"
                                  v-loading="siteReceivingLoading"
                                  border
                                  max-height="390px"
                                  @selection-change="siteReceivingTableSelectR" row-key="orderNo"
                                  @row-click="siteReceivingTableRowClickR"
                                  :data="siteReceivingTableDate"
                                  class="table"
                        >
                            <el-table-column type="selection" width="40"></el-table-column>
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="orderNo" label="订单编号" width=""></el-table-column>
                            <el-table-column prop="materialName" label="物资名称" width=""></el-table-column>
                            <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
                            <el-table-column prop="texture" label="材质" width=""></el-table-column>
                            <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                            <el-table-column prop="quantity" label="剩余可对账数量" width=""></el-table-column>
<!--                            <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
                        </el-table>
                        <Pagination
                            v-show="siteReceivingTableDate != null && siteReceivingTableDate.length > 0"
                            :total="paginationInfo2.total"
                            :pageSize.sync="paginationInfo2.pageSize"
                            :currentPage.sync="paginationInfo2.currentPage"
                            @currentChange="currentChangeUser2"
                            @sizeChange="sizeChangeUser2"
                        />
                    </div>
                </div>
                <div class="buttons">
                    <el-button type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认选择</el-button>
                    <el-button @click="selectMaterialDialogShow = false">取消</el-button>
                </div>

            </el-dialog>
            <el-dialog v-dialogDrag :title="selectContractOrPlanTableTitle" :visible.sync="showSelectContractOrPlan"  width="80%" style="margin-left: 10%;" :close-on-click-modal="false">
                <div class="e-table" style="background-color: #fff" v-loading="selectContractOrPlanLoading">
                    <div class="top dfa" style="height: 50px; padding-left: 10px">
                        <el-input style="width: 200px; " type="text" @blur="getContractOrPlanListM" placeholder="输入搜索关键字" v-model="keywords">
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getContractOrPlanListM" />
                        </el-input>
                    </div>
                    <el-table ref="bidingOrderItemRef"
                              border
                              style="width: 100%"
                              :data="contractOrPlanTableDate"
                              class="table"
                              :max-height="$store.state.tableHeight"
                    >
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column label="操作" width="80">
                            <template slot-scope="scope">
                                <el-button type="primary" class="btn-greenYellow" @click="selectContractOrPlanClickM(scope.row)">选择
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="reconciliationForm.reconciliationProductType == 10" prop="billNo" label="计划编号" width=""></el-table-column>
                        <el-table-column v-if="reconciliationForm.reconciliationProductType == 12" prop="contractNo" label="合同编号" width=""></el-table-column>
                        <el-table-column prop="storageName" label="供应商名称" width=""></el-table-column>
                        <el-table-column prop="gmtCreate" label="创建时间" width=""></el-table-column>

                    </el-table>
                </div>
                <!--分页-->
                <span slot="footer">
                    <Pagination
                        :total="paginationInfo.total"
                        :pageSize.sync="paginationInfo.pageSize"
                        :currentPage.sync="paginationInfo.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </span>
                <div class="buttons">
                    <el-button @click="showSelectContractOrPlan = false">关闭</el-button>
                </div>
            </el-dialog>
            <el-dialog v-dialogDrag title="可对账物资明细" :visible.sync="showSiteReceivingDia"  width="80%" style="margin-left: 10%;" :close-on-click-modal="false">
                <div class="e-table" style="background-color: #fff" v-loading="siteReceivingLoading">
                    <div class="top dfa" style="height: 50px; padding-left: 10px">
                        <el-input style="width: 200px; " type="text" @blur="getSiteReceivingTableDateM" placeholder="输入搜索关键字" v-model="keywords2">
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getSiteReceivingTableDateM" />
                        </el-input>
                    </div>
                    <el-table ref="siteReceivingTableRef"
                              border
                              style="width: 100%"
                              @selection-change="siteReceivingTableSelectM"
                              @row-click="siteReceivingTableRowClickM"
                              :data="siteReceivingTableDate"
                              class="table"
                              :max-height="$store.state.tableHeight"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="orderNo" label="订单编号" width=""></el-table-column>
                        <el-table-column prop="materialName" label="物资名称" width=""></el-table-column>
                        <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
                        <el-table-column prop="texture" label="材质" width=""></el-table-column>
                        <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
<!--                        <el-table-column prop="totalAmount" label="金额" width=""></el-table-column>-->
                        <el-table-column prop="quantity" label="剩余可对账数量" width=""></el-table-column>
<!--                        <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
<!--                        <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>-->
<!--                        <el-table-column prop="sourceNumber" label="源单编号" width=""></el-table-column>-->
                    </el-table>
                </div>
                <!--分页-->
                <span slot="footer">
                    <Pagination
                        :total="paginationInfo2.total"
                        :pageSize.sync="paginationInfo2.pageSize"
                        :currentPage.sync="paginationInfo2.currentPage"
                        @currentChange="currentChangeUser2"
                        @sizeChange="sizeChangeUser2"
                    />
                </span>
                <div class="buttons">
                    <el-button  type="primary" @click="siteReceivingTableDateSelectAffirmClick">确认</el-button>
                    <el-button @click="showSiteReceivingDia = false">关闭</el-button>
                </div>
            </el-dialog>
            <el-dialog
                v-dialogDrag :title="selectPurchasingOrgNameTitle" :visible.sync="showPurchasingOrgNameView" width="80%"
                style="margin-left: 10%;" :close-on-click-modal="false"
            >
                <div class="e-table" v-loading="selectPurchasingOrgNameLoading">
                    <div class="top dfa" style="height: 50px; padding-left: 10px">
                        <el-input
                            style="width: 200px; " type="text" @blur="getOrderPlanListM"
                            placeholder="输入搜索关键字" v-model="supplierKeywords"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getOrderPlanListM"/>
                        </el-input>
                    </div>
                    <el-table
                        ref="selectTwoSupplierR"
                        border
                        :data="purchasingOrgNameTable"
                        class="table"
                        :max-height="$store.state.tableHeight"
                    >
                        <!--<el-table-column type="selection" width="40"></el-table-column>-->
                        <el-table-column label="序号" type="index" width="60"/>
                        <el-table-column label="操作">
                            <template v-slot="scope">
                                <div
                                    class="pointer" style="color: rgba(33, 110, 198, 1);"
                                    @click="submitOrgName(scope.row)"
                                >选择供货单位
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="供货单位名称" prop="storageName"/>
                    </el-table>
                </div>
                <!--分页-->
                <span slot="footer">
                    <Pagination
                        :total="paginationInfo1.total"
                        :pageSize.sync="paginationInfo1.pageSize"
                        :currentPage.sync="paginationInfo1.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { calculateNotTarRateAmountFour, toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { getUuid, throttle, calculateYesTarRateAmount, calculateNotTarRateAmount } from '@/utils/common'
import {
    materialReconciliationCreate,
    getContactSupplierPageList,
    getReconciliablePlansByEnterprisePageList,
    getCanUseSiteReceivingDtl,
    findReconcileableItem
} from '@/api/reconciliation/reconciliation'
import { getReconcilableMaterialList } from '@/api/orderShipDtl/orderShipDtl'
import Pagination from '@/components/pagination/pagination.vue'
import { mapState } from 'vuex'
import {
    reconciliationFixCountAmount,
    taxRateItemAmount
} from '@/utils/material_reconciliationUtils/compute'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        }
    },
    components: {
        Pagination
    },
    data () {
        return {
            //新增  添加选择收货单位
            selectPurchasingOrgNameTitle: null,
            selectPurchasingOrgNameLoading: false,
            supplierKeywords: '',
            showPurchasingOrgNameView: false,
            purchasingOrgNameTable: false,
            storageName: '',
            selectPurchasingOrgNameRowDate: [],
            paginationInfo1: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            productTypeDis: false,
            formLoading: false,
            reconciliationFormRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' },
                ],
            },
            siteReceivingTableSelectRowData: [],
            siteReceivingTableDate: [],
            siteReceivingLoading: false,
            showSiteReceivingDia: false,
            keywords2: null,
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            selectContractOrPlanTableTitle: null,
            selectContractOrPlanLoading: false,
            showSelectContractOrPlan: false,
            selectMaterialDialogShow: false,
            contractOrPlanTableDate: [], dataListSelections: [],
            keywords: null,
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            reconciliationForm: {
                title: null,
                type: null,
                orderId: null,
                orderSn: null,
                businessType: null,
                reconciliationProductType: null,
                sourceBillId: null,
                sourceBillNo: null,
                supplierId: null,
                supplierEnterpriseId: null,
                supplierName: null,
                purchaserId: null,
                purchaserLocalId: null,
                purchaserName: null,
                purchasingOrgId: null,
                purchasingLocalOrgId: null,
                purchasingOrgName: null,
                acceptanceName: null,
                reconciliationAmount: 0,
                taxAmount: 0,
                reconciliationNoRateAmount: 0,
                settleAmount: null,
                startTime: null,
                endTime: null,
                startEndTme: [],
                createType: 1,
                // 内外供应商信息
                creditCode: null,
                orgShort: null,
                supplierOrgId: null,
                taxRate: null,
                dtl: [],
            },
            originalTaxRate: null, // 保存原始税率
            showReconciliationForm: false,
            reconciliationFormLoading: false,
            maxNum: *********,
            tableData: [
            ],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: [],
            checkTimeNow: 0, // 选中时的时间
        }
    },
    created () {
        this.reconciliationForm.type = this.$route.query.type
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        //新增添加部选择
        selectedEnterprise () {
            this.selectPurchasingOrgNameTitle = '选择供货单位'
            this.getOrderPlanListM()
            this.showPurchasingOrgNameView = true
        },
        getOrderPlanListM () {
            let params = {
                page: this.paginationInfo1.currentPage,
                limit: this.paginationInfo1.pageSize,
            }
            if (this.supplierKeywords != null) {
                params.keywords = this.supplierKeywords
            }
            if (this.reconciliationForm.reconciliationProductType != null) {
                params.productType = this.reconciliationForm.reconciliationProductType
            }
            this.selectPurchasingOrgNameLoading = true
            getContactSupplierPageList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.purchasingOrgNameTable = res.list
            }).finally(() => {
                this.selectPurchasingOrgNameLoading = false
            })
        },
        //选择供货单位
        submitOrgName (row) {
            this.reconciliationForm.supplierName = row.storageName
            this.reconciliationForm.supplierId = row.storageId
            this.storageName = row.storageName
            this.showPurchasingOrgNameView = false
        },
        selectMaterialBtnClick () {
            this.siteReceivingTableDate = []
            this.selectMaterialDialogShow = true
            this.getContractOrPlanListM()

        },
        siteReceivingTableSelectM (value) {
            this.siteReceivingTableSelectRowData = value
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        // 选择数据确认
        siteReceivingTableDateSelectAffirmClick () {
            if(this.siteReceivingTableSelectRowData.length == 0) {
                return this.$message.error('未选择数据')
            }
            let newArr = []

            // 检查是否使用了getCanUseSiteReceivingDtl接口（有pBillId的情况）
            let hasPBillId = this.dataListSelections && this.dataListSelections.some(t => t.pBillId)

            this.siteReceivingTableSelectRowData.forEach(row => {
                let dtl = {}

                if (hasPBillId) {
                    // 如果有pBillId，判断是否为周转材料
                    if (this.reconciliationForm.reconciliationProductType === 2) { //周转材料
                    // 周转材料使用findReconcileableItem接口返回的字段映射
                        dtl = {
                            // 基础信息
                            orderId: row.billId, // findReconcileableItem返回billId作为周材入库ID
                            orderSn: row.billNo, // findReconcileableItem返回billNo作为入库编号
                            tradeId: row.dtlId, // findReconcileableItem返回dtlId作为周材入库明细ID
                            orderItemId: row.sourceDtlId, // findReconcileableItem返回sourceDtlId作为源单明细ID

                            // 物资信息
                            materialName: row.materialName,
                            materialClassId: row.materialClassId,
                            materialClassName: row.materialClassName,
                            spec: row.spec,
                            texture: row.texture,
                            unit: row.unit,

                            // 供应商信息
                            supplierId: row.supplierId || '', // findReconcileableItem可能没有supplierId
                            supplierName: row.supplierName,

                            // 采购信息
                            purchaserId: '', // findReconcileableItem没有采购人员信息
                            purchaserName: '',
                            purchasingOrgId: row.warehouseId, // findReconcileableItem返回warehouseId作为仓库ID
                            purchasingOrgName: row.orgName, // findReconcileableItem返回orgName作为单据机构名称

                            // 数量和价格
                            maxQuantity: row.receiptQuantity || row.totalQuantity, // findReconcileableItem返回receiptQuantity或totalQuantity
                            quantity: row.receiptQuantity || row.totalQuantity, // 默认使用入库数量
                            ratePrice: 0, // 周转材料可能没有价格信息，默认为0
                            noRatePrice: '0.00', // 不含税单价，后续计算
                            price: 0, // 含税单价，固定价格对账时由出厂价+运杂费计算得出

                            // 日期
                            receivingDate: row.warehousingDate, // findReconcileableItem返回warehousingDate作为入库时间

                            // 对账单类型
                            type: row.businessTypeKey, // findReconcileableItem返回businessTypeKey

                            // 备注
                            remarks: '',

                            // 其他字段
                            settledAmount: 0,
                            fixationPrice: 0,
                            freightPrice: 0,
                            acceptanceAmount: 0,
                            acceptanceNoRateAmount: 0,
                            uuid: getUuid(),

                            // 周转材料特有字段
                            leaseTime: row.leaseTime, // 租赁时间
                            timeUnit: row.timeUnit, // 计时单位
                            warehouseId: row.warehouseId, // 仓库ID
                            warehouseName: row.warehouseName, // 仓库名称
                            sourceNo: row.sourceNo, // 源单编号
                            state: row.state // 状态
                        }
                    } else {
                        // 如果有pBillId，零星采购和大宗临购，使用getCanUseSiteReceivingDtl接口返回的字段映射
                        // 计算含税单价
                        let taxInclusivePrice = (row.totalAmount / row.totalQuantity).toFixed(2)
                        // 获取税率，优先使用表单中的税率，如果没有则使用默认值11%
                        let currentTaxRate = this.reconciliationForm.taxRate
                        // 计算不含税单价：不含税单价 = 含税单价 / (1 + 税率/100)
                        let noTaxPrice = (taxInclusivePrice / (1 + currentTaxRate / 100)).toFixed(2)

                        dtl = {
                        // 基础信息
                            receiptBillId: row.billId,
                            receiptBillSn: row.billNo,
                            receiptBillDtlId: row.dtlId,
                            sourceBillId: row.sourceId,
                            sourceBillNo: row.sourceNumber,
                            sourceName: row.sourceName,
                            orderId: row.orderId,
                            orderSn: row.orderNo, // getCanUseSiteReceivingDtl返回orderNo
                            tradeId: row.tradeId,
                            orderItemId: row.orderDtlId, // getCanUseSiteReceivingDtl返回orderDtlId

                            // 物资信息
                            materialId: row.materialId,
                            materialClassId: row.materialClassId,
                            materialClassName: row.materialClassName,
                            materialMallId: row.materialMallId,
                            materialName: row.materialName,
                            spec: row.spec,
                            texture: row.texture,
                            unit: row.unit,

                            // 供应商信息
                            supplierId: row.supplierId,
                            supplierName: row.supplierName,

                            // 采购信息
                            purchaserId: row.purchasingPersonnelId, // getCanUseSiteReceivingDtl返回purchasingPersonnelId
                            purchaserName: row.purchasingPersonnelName, // getCanUseSiteReceivingDtl返回purchasingPersonnelName
                            purchasingOrgId: row.orgId, // getCanUseSiteReceivingDtl返回orgId
                            purchasingOrgName: row.orgName, // getCanUseSiteReceivingDtl返回orgName

                            // 数量和价格
                            maxQuantity: row.quantity, // getCanUseSiteReceivingDtl返回quantity
                            quantity: row.quantity, // 默认使用数量
                            ratePrice: taxInclusivePrice, // getCanUseSiteReceivingDtl返回networkPrice作为含税单价
                            noRatePrice: noTaxPrice, // 不含税单价，根据含税单价和税率计算得出
                            price: taxInclusivePrice, // 含税单价，固定价格对账时由出厂价+运杂费计算得出

                            // 日期
                            receivingDate: row.receivingDate, // getCanUseSiteReceivingDtl返回receivingDate
                            warehouseId: row.warehouseId, // 仓库ID
                            warehouseName: row.warehouseName, // 仓库名称

                            // 对账单类型
                            type: row.type,

                            // 备注
                            remarks: row.remarks || '',

                            // 其他字段
                            settledAmount: 0,
                            fixationPrice: 0,
                            freightPrice: 0,
                            acceptanceAmount: 0,
                            acceptanceNoRateAmount: 0,
                            uuid: getUuid()
                        }
                    }
                } else {
                    // 如果没有pBillId，使用getReconcilableMaterialList接口返回的字段映射
                    dtl = {
                        // 基础信息
                        orderId: row.orderId,
                        orderSn: row.orderSn,
                        tradeId: row.tradeId,
                        orderItemId: row.orderItemId,

                        // 物资信息
                        materialName: row.materialName,
                        materialClassId: row.materialClassId,
                        materialClassName: row.materialClassName,
                        spec: row.spec,
                        texture: row.texture,
                        unit: row.unit,

                        // 供应商信息
                        supplierId: row.supplierId,
                        supplierName: row.supplierName,

                        // 采购信息
                        purchaserId: row.purchaserId,
                        purchaserName: row.purchaserName,
                        purchasingOrgId: row.purchasingOrgId,
                        purchasingOrgName: row.purchasingOrgName,

                        // 数量和价格
                        maxQuantity: row.maxQuantity,
                        quantity: row.maxQuantity, // 默认使用最大可对账数量
                        ratePrice: row.ratePrice || 0, // 含税单价
                        noRatePrice: row.noRatePrice ? Number(row.noRatePrice).toFixed(2) : '0.00', // 不含税单价，截取2位小数
                        price: 0, // 含税单价，固定价格对账时由出厂价+运杂费计算得出

                        // 日期
                        receivingDate: row.receiveDate,

                        // 对账单类型
                        type: row.type,

                        // 备注
                        remarks: row.remarks,

                        // 其他字段（保持原有逻辑）
                        settledAmount: 0,
                        fixationPrice: 0,
                        freightPrice: 0,
                        acceptanceAmount: 0,
                        acceptanceNoRateAmount: 0,
                        uuid: getUuid()
                    }
                }

                if(dtl.quantity == null || dtl.quantity <= 0) {
                    return this.$message.error('剩余数量为0不能选择！')
                }
                newArr.push(dtl)
            })

            // 直接使用选中的数据，确保不含税单价正确计算
            newArr.forEach(t => {
                let taxRate = this.reconciliationForm.taxRate

                // 含税单价使用后台返回的价格
                t.price = this.fixed2(Number(t.ratePrice || 0))

                // 确保含税单价不能小于0
                if (t.price < 0) {
                    t.price = 0
                }

                // 确保参数都是有效的数值，避免NaN
                let price = Number(t.price) || 0
                let quantity = Number(t.quantity) || 0

                if (price >= 0 && taxRate > 0) {
                    // 传递原始不含税单价给taxRateItemAmount函数
                    let originalNoRatePrice = (!hasPBillId && t.noRatePrice && Number(t.noRatePrice) > 0) ? t.noRatePrice : null

                    // 保存原始不含税单价到数据项中，供后续计算使用
                    if (originalNoRatePrice) {
                        t.originalNoRatePrice = originalNoRatePrice
                    }

                    let prams = taxRateItemAmount(price, quantity, taxRate, originalNoRatePrice)

                    t.noRatePrice = prams.noRatePrice
                    t.taxAmount = prams.taxAmount
                    t.acceptanceNoRateAmount = prams.acceptanceNoRateAmount
                    t.acceptanceAmount = prams.acceptanceAmount
                }
            })

            this.tableData = newArr

            // 根据数据来源设置表单字段
            let firstRow = this.siteReceivingTableSelectRowData[0]
            if (hasPBillId) {
                // 周转材料使用findReconcileableItem接口的字段映射
                this.reconciliationForm.purchaserId = firstRow.purchasingPersonnelId || '' // 周转材料没有采购人员信息
                this.reconciliationForm.purchaserName = firstRow.purchasingPersonnelName || ''
                this.reconciliationForm.purchasingOrgName = firstRow.orgName || ''
                this.reconciliationForm.purchasingOrgId = firstRow.orgId || ''
                if(this.reconciliationForm.reconciliationProductType === 0) {
                    this.reconciliationForm.sourceBillId = firstRow.sourceId
                    this.reconciliationForm.sourceBillNo = firstRow.sourceNo
                    this.reconciliationForm.sourceBillName = firstRow.sourceName
                    this.reconciliationForm.businessType = 2
                }else if(this.reconciliationForm.reconciliationProductType === 1) {
                    this.reconciliationForm.businessType = 6
                }
            } else {
                // getReconcilableMaterialList接口的字段映射（原有逻辑）
                this.reconciliationForm.purchaserId = firstRow.purchaserId || ''
                this.reconciliationForm.purchaserName = firstRow.purchaserName || ''
                // 设置收货单位信息
                if (firstRow.purchasingOrgId) {
                    this.reconciliationForm.purchasingOrgId = firstRow.purchasingOrgId
                }
                if (firstRow.purchasingOrgName) {
                    this.reconciliationForm.purchasingOrgName = firstRow.purchasingOrgName
                }
            }

            // 确认选择时不变更供货单位信息，保持原有的供货单位
            // this.reconciliationForm.supplierId = firstRow.supplierId
            // this.reconciliationForm.supplierName = firstRow.supplierName
            // 计算金额
            this.gCountAmountM()
            this.showSiteReceivingDia = false
            this.selectMaterialDialogShow = false
        },
        getSiteReceivingTableDateM () {

            this.reconciliationForm.taxRate = this.dataListSelections[0].taxRate
            let params = {
                page: this.paginationInfo2.currentPage,
                limit: this.paginationInfo2.pageSize,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                orgId: this.reconciliationForm.purchasingOrgId,
                businessType: this.reconciliationForm.businessType,
                type: 2 // 固定价格对账传值type=2
            }

            // 检查选中的计划数据中是否有pBillId
            let hasPBillId = this.dataListSelections && this.dataListSelections.some(t => t.pBillId)

            if (hasPBillId) {
                // 如果有pBillId，判断是否为周转材料
                if (this.reconciliationForm.reconciliationProductType === 2) {
                    // 周转材料调用findReconcileableItem接口
                    let params2 = {
                        page: this.paginationInfo2.currentPage,
                        limit: this.paginationInfo2.pageSize,
                        startDate: this.reconciliationForm.startEndTme[0],
                        endDate: this.reconciliationForm.startEndTme[1],
                    }
                    if (this.dataListSelections && this.dataListSelections.length > 0) {
                        let orderIds = this.dataListSelections.map(t => t.orderId)
                        if(orderIds.length == 0) {
                            return this.$message.error('未选择订单')
                        }
                        params2.placeId = orderIds
                    }else {
                        return this.$message.error('未选择订单')
                    }
                    if(this.keywords2 != null) {
                        params2.keyword = this.keywords2
                    }
                    params2.orgId = this.dataListSelections[0].orgId
                    if(this.$refs.siteReceivingTableRefR) {this.$refs.siteReceivingTableRefR.clearSelection()}
                    this.siteReceivingLoading = true
                    let _this = this
                    this.reconciliationForm.taxRate = this.dataListSelections[0].taxRate

                    // 调用findReconcileableItem接口
                    findReconcileableItem(params2).then(res => {
                        this.paginationInfo2.total = res.totalCount
                        this.paginationInfo2.pageSize = res.pageSize
                        this.paginationInfo2.currentPage = res.currPage
                        this.siteReceivingTableDate = res.list

                        _this.$nextTick(() => {
                            _this.siteReceivingTableDate.forEach(item => {
                                _this.$refs.siteReceivingTableRefR.toggleRowSelection(item, true)
                            })
                        })
                    }).finally(() => {
                        this.siteReceivingLoading = false
                    })
                } else {
                    // 如果有pBillId但是零星采购和大宗临购，调用getCanUseSiteReceivingDtl接口
                    if (this.dataListSelections && this.dataListSelections.length > 0) {
                        let orderIds = this.dataListSelections.map(t => t.orderId)
                        if(orderIds.length == 0) {
                            return this.$message.error('未选择订单')
                        }
                        params.orderIds = orderIds
                    }else {
                        return this.$message.error('未选择订单')
                    }
                    if(this.keywords2 != null) {
                        params.keyword = this.keywords2
                    }
                    params.orgId = this.dataListSelections[0].orgId
                    if(this.$refs.siteReceivingTableRefR) {this.$refs.siteReceivingTableRefR.clearSelection()}
                    this.siteReceivingLoading = true
                    let _this = this
                    this.reconciliationForm.taxRate = this.dataListSelections[0].taxRate

                    // 调用getCanUseSiteReceivingDtl接口
                    getCanUseSiteReceivingDtl(params).then(res => {
                        this.paginationInfo2.total = res.totalCount
                        this.paginationInfo2.pageSize = res.pageSize
                        this.paginationInfo2.currentPage = res.currPage
                        // 转换后端返回的字段名称以匹配前端表格绑定
                        this.siteReceivingTableDate = res.list.map(item => {
                            return {
                                ...item,
                                orderSn: item.orderNo, // 将orderNo转换为orderSn
                                maxQuantity: item.quantity // 将quantity转换为maxQuantity
                            }
                        })

                        _this.$nextTick(() => {
                            _this.siteReceivingTableDate.forEach(item => {
                                _this.$refs.siteReceivingTableRefR.toggleRowSelection(item, true)
                            })
                        })
                    }).finally(() => {
                        this.siteReceivingLoading = false
                    })
                }
            } else {
                // 如果没有pBillId，调用getReconcilableMaterialList接口
                if (this.dataListSelections && this.dataListSelections.length > 0) {
                    let orderSn = this.dataListSelections.map(t => t.orderSn)
                    if(orderSn.length == 0) {
                        return this.$message.error('未选择订单')
                    }
                    params.orderSn = orderSn.join(',')
                }else {
                    return this.$message.error('未选择订单')
                }
                // 如果是内部用户
                if(this.userInfo.isInterior == 1) {
                    params.orgShort = this.userInfo.orgInfo.shortCode
                } else {
                    params.creditCode = this.userInfo.socialCreditCode
                }
                if(this.keywords2 != null) {
                    params.keywords = this.keywords2
                }

                if(this.$refs.siteReceivingTableRefR) {this.$refs.siteReceivingTableRefR.clearSelection()}
                this.siteReceivingLoading = true
                let _this = this

                getReconcilableMaterialList(params).then(res => {
                    this.paginationInfo2.total = res.totalCount
                    this.paginationInfo2.pageSize = res.pageSize
                    this.paginationInfo2.currentPage = res.currPage
                    this.siteReceivingTableDate = res.list.map(item => {
                        return {
                            ...item,
                            orderNo: item.orderSn,
                            quantity: item.maxQuantity
                        }
                    })
                    _this.$nextTick(() => {
                        _this.siteReceivingTableDate.forEach(item => {
                            _this.$refs.siteReceivingTableRefR.toggleRowSelection(item, true)
                        })
                    })
                }).finally(() => {
                    this.siteReceivingLoading = false
                })
            }
        },
        // 表格选中事件
        siteReceivingTableRowClickL (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRefL.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableSelectL (val) {
            let nowTime = new Date().getTime()
            if(nowTime - this.checkTimeNow < 200) {
                this.checkTimeNow = nowTime
                return false
            }
            let billNoArr = []
            for(let itemI of val) {
                if(billNoArr.indexOf(itemI.billNo) == -1) {
                    billNoArr.push(itemI.billNo)
                }
            }
            if(billNoArr.length > 1) {
                if(!this.dataListSelections.length) {
                    this.$refs.siteReceivingTableRefL.clearSelection()
                }else {
                    let billNo = this.dataListSelections[0].billNo
                    this.$nextTick(() => {
                        this.contractOrPlanTableDate.forEach(item => {
                            if(billNo != item.billNo ) {
                                this.$refs.siteReceivingTableRefL.toggleRowSelection(item, false)
                            }
                        })
                    })
                }
                this.checkTimeNow = new Date().getTime()
                return this.$message.warning('只能选择同一个计划编号的数据')
            }
            this.dataListSelections = val
            if(val.length > 0) {
                // 参考供应商端浮动价格对账页面，选择表格后调用后台接口
                this.getSiteReceivingTableDateM()
            } else {
                this.siteReceivingTableDate = []
                this.paginationInfo2.total = 0
                this.paginationInfo2.pageSize = 20
                this.paginationInfo2.currentPage = 1
            }
        },
        siteReceivingTableRowClickR (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRefR.toggleRowSelection(row, row.flag)
        },
        siteReceivingTableSelectR (value) {
            this.siteReceivingTableSelectRowData = value
        },
        selectContractOrPlanClickM (row) {
            this.productTypeDis = true
            this.tableData = []
            // 使用新接口返回的字段
            this.reconciliationForm.purchasingOrgId = row.orgId
            this.reconciliationForm.purchasingOrgName = row.orgName
            this.reconciliationForm.supplierName = row.supplierName
            this.reconciliationForm.sourceBillNo = row.billNo
            this.reconciliationForm.orderSn = row.orderSn

            // 根据业务类型设置相关字段
            if (this.reconciliationForm.reconciliationProductType == 0) {
                this.reconciliationForm.businessType = 2 // 零星采购
            } else if (this.reconciliationForm.reconciliationProductType == 1) {
                this.reconciliationForm.businessType = 6 // 大宗临购
            } else if (this.reconciliationForm.reconciliationProductType == 2) {
                this.reconciliationForm.businessType = 7 // 周转材料
            }

            this.reconciliationForm.sourceBillId = row.billId
            this.reconciliationForm.taxRate = row.taxRate || 13 // 默认税率13%

            this.showSelectContractOrPlan = false
            this.siteReceivingTableSelectRowData = []
            this.getSiteReceivingTableDateM()
        },
        currentChangeUser2 (currPage) {
            this.paginationInfo2.currentPage = currPage
            this.getSiteReceivingTableDateM()
        },
        sizeChangeUser2 (pageSize) {
            this.paginationInfo2.pageSize = pageSize
            this.getSiteReceivingTableDateM()
        },
        currentChangeUser (currPage) {
            this.paginationInfo.currentPage = currPage
            this.getContractOrPlanListM()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo.pageSize = pageSize
            this.getContractOrPlanListM()
        },
        // 获取合同或计划编号
        getContractOrPlanListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if(this.keywords != null) {
                params.keywords = this.keywords
            }
            if(this.reconciliationForm.reconciliationProductType != null) {
                params.productType = this.reconciliationForm.reconciliationProductType
            }
            if (this.reconciliationForm.supplierId != null) {
                params.supplierId = this.reconciliationForm.supplierId
            }
            // 添加对账时间参数
            if (this.reconciliationForm.startEndTme && this.reconciliationForm.startEndTme.length > 0) {
                params.startTime = this.reconciliationForm.startEndTme[0]
                params.endTime = this.reconciliationForm.startEndTme[1]
            }
            params.type = this.$route.query.type
            if(this.$refs.siteReceivingTableRefL) {this.$refs.siteReceivingTableRefL.clearSelection()}
            this.selectContractOrPlanLoading = true
            getReconciliablePlansByEnterprisePageList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.contractOrPlanTableDate = res.list.map(item => {
                    return {
                        ...item,
                        storageName: item.supplierName || item.orgName
                    }
                })
            }).finally(() => {
                this.selectContractOrPlanLoading = false
            })
        },
        // 获取明细
        getCanUseSiteReceivingDtlM () {
            if(this.reconciliationForm.startEndTme.length == 0) {
                return
            }
            this.getSiteReceivingTableDateM()
            this.showSiteReceivingDia = true
        },
        // 保存
        saveSheetM (num) {
            if(this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if(t.quantity <= 0) {
                    return this.$message.error('序号为：【' + (i + 1) + '】的数量需要大于0！')
                }
                if(t.price <= 0) {
                    return this.$message.error('序号为：【' + (i + 1) + '】的含税单价需要大于0！')
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要保存吗？', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        this.reconciliationForm.isSubmit = num

                        materialReconciliationCreate(this.reconciliationForm).then(res => {
                            if(res.code == null && res.message == null) {
                                this.$message.success('操作成功')
                                this.$router.push({
                                    path: '/performance/fixationDetailInfo',
                                    name: 'fixationDetailInfo',
                                    query: {
                                        sn: res
                                    }
                                })
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        // 选择计划
        selectPlanClick () {
            this.selectContractOrPlanTableTitle = '选择计划'
            this.getContractOrPlanListM()
            this.showSelectContractOrPlan = true
        },
        // 选择合同
        selectContractClick () {
            this.selectContractOrPlanTableTitle = '选择合同'
            this.getContractOrPlanListM()
            this.showSelectContractOrPlan = true
        },
        // 删除单
        deleteM (row) {
            this.tableData = this.tableData.filter(t => {
                if(t.uuid != row.uuid) {
                    return true
                }else{
                    return  false
                }
            })
        },
        taxRateChange () {
            if (this.reconciliationForm.taxRate == null || this.reconciliationForm.taxRate < 0) {
                this.reconciliationForm.taxRate = this.fixed2(0)
            } else if (this.reconciliationForm.taxRate >= 100 ) {
                this.reconciliationForm.taxRate = 100
            } else {
                this.reconciliationForm.taxRate = this.fixed2(this.reconciliationForm.taxRate)
            }

            // 税率变更时，重新计算所有明细的不含税单价、不含税金额和税额
            // 不保留原始不含税单价，全部重新计算
            let params = reconciliationFixCountAmount(this.tableData, this.reconciliationForm.taxRate, false)
            this.tableData = params.tableData
            this.reconciliationForm.taxAmount = params.taxAmount
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount
        },
        // 拆单
        dismantleM (row) {
            // 插入到当前点击的下一个节点
            let insertIndex = this.tableData.length
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if(t.uuid == row.uuid) {
                    insertIndex = i + 1
                }
            }
            let newRow = {
                ...row,
                acceptanceAmount: this.fixed2(0),
                quantity: 0,
                uuid: getUuid()
            }
            this.tableData.splice(insertIndex, 0, newRow)
        },
        // 表单变化
        getChangedRow (row) {
            // 处理数量
            this.disposeQuantityM(row)
            // 计算金额
            this.countAmountM()
        },
        countAmountM () {
            // 检查是否有数据项包含原始不含税单价
            let hasOriginalNoRatePrice = this.tableData.some(item => item.originalNoRatePrice && Number(item.originalNoRatePrice) > 0)
            let params = reconciliationFixCountAmount(this.tableData, this.reconciliationForm.taxRate, hasOriginalNoRatePrice)
            this.tableData = params.tableData
            this.reconciliationForm.taxAmount = params.taxAmount
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount
        },
        countAmountOneCount () {
            let params = reconciliationFixCountAmount(this.tableData, this.reconciliationForm.taxRate)
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount
            this.reconciliationForm.taxAmount = params.taxAmount
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        priceChange (row) {
            // 一旦变化则是1
            row.updateType = 1
            // 处理价格
            this.disposePriceM(row)
            // 计算金额
            this.countAmountM()
        },
        // 固定价格对账金额计算
        gCountAmountM () {
            // 检查是否有数据项包含原始不含税单价
            let hasOriginalNoRatePrice = this.tableData.some(item => item.originalNoRatePrice && Number(item.originalNoRatePrice) > 0)
            let params = reconciliationFixCountAmount(this.tableData, this.reconciliationForm.taxRate, hasOriginalNoRatePrice)
            this.tableData = params.tableData
            this.reconciliationForm.taxAmount = params.taxAmount
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount
        },
        // 处理价格
        disposePriceM (row) {
            if(row.price <= 0 || row.price >= this.maxNum) {
                row.price = this.fixed2(0)
            }else {
                row.price = this.fixed2(row.price)
            }
        },
        // 处理数量
        disposeQuantityM (row) {
            if(row.quantity <= 0) {
                return row.quantity = this.fixed4(0)
            }
            // 计算最大值
            let maxNum = this.fixed4(row.maxQuantity)
            let countNum = 0
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.materialName === row.materialName && t.orderSn === row.orderSn && t.spec === row.spec && t.unit == row.unit && t.uuid !== row.uuid) {
                    countNum++
                    if (maxNum <= 0) {
                        maxNum = 0
                        continue
                    }
                    maxNum = maxNum - t.quantity
                }
            }
            // 如果一次没添加，则表示操作的第一个
            if(countNum == 0) {
                maxNum = row.maxQuantity
            }
            if (row.quantity >= maxNum) {
                row.quantity = this.fixed4(maxNum)
            } else {
                row.quantity = this.fixed4(row.quantity)
            }
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try{
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            }catch (err) {
                // 滚动错误处理
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliationDtl']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
}
</script>

<style lang='scss' scoped>
.e-table {
    min-height: auto;
    background: #fff;
}

/* 表格水平滚动支持 */
.table-container {
    overflow-x: auto;
    width: 100%;
}

.table-container .el-table {
    min-width: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
    appearance: textfield !important;
}
/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}
/deep/ .el-dialog.dlg {
    height: 600px;

    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 474px;
        margin: 10px;
        display: flex;

        & > div {
            .e-pagination {
                background-color: unset;
            }

            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }

        .e-table {
            flex-grow: 1;

            .table {
                height: 100%;
            }
        }

        .box-left {
            width: 660px;
            display: flex;
            flex-direction: column;
            .top {
                box-shadow: unset;
            }
        }

        .box-right {
            flex-grow: 1;
            display: flex;
            flex-direction: column;

            & > div {
                display: flex;
                flex-direction: column;
            }

            .top {
                justify-content: left;
                border-radius: 0;
                box-shadow: unset;
            }

            .bottom {
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}

</style>