
/**
 * 流程常量
 */
export const ProcessID = Object.freeze({
    /**
     * 供应商平台注册
     */
    SUPPLIER_REGISTER: '1',

    /**
     * 店铺开店审核
     */
    SHOP_OPEN_AUDIT: '2',

    /**
     * 商品上架
     */
    GOODS_ON_SHELF: '3',

    /**
     * 上游供应商准入
     */
    UPSTREAM_SUPPLIER_ADMIT: '4',

    /**
     * 商品上/下架 常规加成率定价
     */
    GOODS_ON_SHELF_COMMON: '5',

    /**
     * 商品上/下架 市场调整加成率定价
     */
    GOODS_ON_SHELF_MARKET: '6',

    /**
     * 竞价管理-竞价结果   选择报价排名第一的供应商
     */
    BIDDING_PROCESS_ID_FIRST: '7',

    /**
     * 竞价管理-竞价结果   因特殊原因未选择报价排名第一的供应商及存在多个并列第一的供应商
     */
    BIDDING_PROCESS_ID_NO_FIRST: '8',

    /**
     * 大宗临购清单报价-系统推送
     */
    BULK_PURCHASE_LIST_QUOTE_SYSTEM: '9',

    /**
     * 大宗临购清单报价-手动填写/修改单价
     */
    BULK_PURCHASE_LIST_QUOTE_MANUAL: '10',

    /**
     * 入库结算单
     */
    INBOUND_SETTLEMENT: '11',

    /**
     * 出库结算单
     */
    OUTBOUND_SETTLEMENT: '12',

    /**
     * 零星采购对账单及二级供应商对账单
     */
    YEAR_APPROVED: '13',

    /**
     * 大宗临购对账单及二级供应商对账单
     */
    YEAR_PENDING_REVIEW: '14',

    /**
     * 大宗临购需求清单
     */
    BULK_PURCHASE_DEMAND_LIST: '15',

    /**
     * 零星采购对账单
     */
    SPORADIC_PURCHASE_STATEMENT: '16',

    /**
     * 大宗临购对账单
     */
    BULK_PURCHASE_STATEMENT: '17',

    /**
     * 发布竞价
     */
    PUBLISH_BIDDING_PROCESS_ID: '18'
})
