<template>
  <div>
    <!-- 单项搜索 -->
    <div class="search_box">
      <el-radio v-model="filterParams.orderBy" :label="1">按创建时间排序</el-radio>
      <el-radio v-model="filterParams.orderBy" :label="2">按发布时间</el-radio>
      <el-radio v-model="filterParams.orderBy" :label="3">按截止时间</el-radio>
      <el-input clearable style="width: 300px" type="text" @blur="handleInputSearch" placeholder="输入搜索关键字"
        v-model="filterParams.keywords">
        <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt="" />
      </el-input>
      <div class="adverse">
        <btn-normal @click="queryVisible = true" />
      </div>
    </div>

    <!-- 新增竞价 高级搜索  -->
      <el-dialog class="dialogCenterDiv" title="高级查询" :visible.sync="queryVisible" width="40%"  @close="resetSearchConditions">
        <el-form :model="filterParams" ref="form" label-width="120px" :inline="false">
          <el-row>
            <el-col :span="22">
              <el-form-item label="商品类型：">
                <el-select style="width: 100%;" v-model="filterParams.productType" placeholder="请选择商品类型">
                  <el-option v-for="item in productTypeSelect" :key="item.value" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="状态：">
                <el-select style="width: 100%;" v-model="filterParams.state" placeholder="请选择状态">
                  <el-option v-for="item in stateSelect" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="单据时间状态：">
                <el-select style="width: 100%;" v-model="filterParams.biddingState" placeholder="请选择订单类型">
                  <el-option v-for="item in biddingStateSelect" :key="item.value" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="竞价类型：">
                <el-select style="width: 100%;" v-model="filterParams.type" placeholder="请选择竞价类型">
                  <el-option v-for="item in typeSelect" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="竞价编号：">
                <el-input clearable maxlength="100" placeholder="请输入竞价编号" v-model="filterParams.biddingSn"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="22">
              <el-form-item label="标题：">
                <el-input clearable maxlength="100" placeholder="请输入标题" v-model="filterParams.title"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="创建时间：">
                <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss" v-model="filterParams.createDate"
                  type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="竞价截止时间：">
                <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss" v-model="filterParams.endDate" type="datetimerange"
                  range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col :span="12">
              <el-radio v-model="filterParams.orderBy" :label="1">按创建时间排序</el-radio>
              <el-radio v-model="filterParams.orderBy" :label="2">按发布时间</el-radio>
              <el-radio v-model="filterParams.orderBy" :label="3">按截止时间</el-radio>
            </el-col>
          </el-row> -->
        </el-form>
        <span slot="footer">
          <el-button type="primary" class="btn10" @click="confirmSearch">查询</el-button>
          <!-- <btn-normal buttonType="primary" buttonText="查询"  @click="confirmSearch" /> -->
          <el-button class="btn10" @click="resetSearchConditions">清空</el-button>
          <el-button class="btn10" @click="clearM">取消</el-button>
        </span>
      </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils/common'
import BtnNormal from '@/components/btn-normal.vue'
export default {
    name: 'SearchComponent',
    components: {
        BtnNormal,
    },
    props: {
        stateType: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            filterParams: {
                keywords: '',
                productType: null,
                state: this.stateType === 1 ? 1 : null,
                biddingState: this.stateType === 1 ? 1 : null,
                type: null,
                biddingSn: '',
                title: '',
                createDate: [],
                endDate: [],
                orderBy: 1
            },
            queryVisible: false,
            searchLoading: false,

            productTypeSelect: [
                { value: null, label: '全部' },
                { value: 0, label: '低值易耗品' },
                { value: 1, label: '大宗临购' },
                { value: 2, label: '大宗临购清单' },
            ],
            baseStateSelect: [
                { value: null, label: '全部' },
                { value: 0, label: '待提交' },
                { value: 1, label: '待审核' },
                { value: 2, label: '审核失败' },
                { value: 5, label: '审核通过' },
                { value: 9, label: '已流标' },
            ],
            baseBiddingStateSelect: [
                { value: null, label: '全部' },
                { value: 1, label: '未开始' },
                { value: 2, label: '进行中' },
                { value: 3, label: '已结束' },
            ],
            typeSelect: [
                { value: null, label: '全部' },
                { value: 1, label: '公开竞价' },
                { value: 2, label: '邀请竞价' },
            ],
        }
    },
    computed: {
        stateSelect () {
            if (this.stateType === 1) {
                return [
                    { value: 1, label: '待审核' }
                ]
            }
            return this.baseStateSelect
        },
        biddingStateSelect () {
            if (this.stateType === 1) {
                return [
                    { value: 1, label: '未开始' }
                ]
            }
            return this.baseBiddingStateSelect
        }
    },
    created () {
        // 使用指定的防抖函数，设置500ms延迟，非立即执行
        this.debouncedSearch = debounce(this.triggerSearch, 500, false)
        if (this.stateType === 1) {
            this.filterParams.biddingState = 1
            this.filterParams.state = 1
        }
    },
    watch: {
        'filterParams.orderBy': {
            handler () {
                this.triggerSearch()
            },
        },
        stateType: {
            handler (newVal) {
                if (newVal === 1) {
                    this.filterParams.biddingState = 1
                    this.filterParams.state = 1
                } else {
                    // 非1状态时重置为null
                    this.filterParams.biddingState = null
                    this.filterParams.state = null
                }
            },
            immediate: true // 初始化时执行一次
        }
    },
    methods: {

        clearM () {
            this.resetSearchConditions()
            this.queryVisible = false
        },
        /**
         * 重置搜索条件
         */
        resetSearchConditions () {
            this.filterParams = {
                keywords: '',
                productType: null,
                state: null,
                biddingState: null,
                type: null,
                biddingSn: '',
                title: '',
                createDate: [],
                endDate: [],
            }
            // 重置表单状态
            if (this.$refs.form) {
                this.$refs.form.resetFields()
            }
        },

        /**
         * 处理输入框搜索
         */
        handleInputSearch () {
            this.debouncedSearch()
        },

        /**
         * 高级搜索确认
         */
        confirmSearch () {
            this.searchLoading = true

            setTimeout(() => {
                this.triggerSearch()
                this.queryVisible = false
                this.searchLoading = false
            }, 300)
        },

        /**
         * 触发搜索
         */
        triggerSearch () {
            const params = { ...this.filterParams }

            if (params.createDate && params.createDate.length === 0) {
                params.createDate = null
            }
            if (params.endDate && params.endDate.length === 0) {
                params.endDate = null
            }

            this.$emit('search', params)
        }
    }
}
</script>

<style lang="scss" scoped>
.search_box {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  align-items: center;
  gap: 10px;
  background: #fff;
  height: 55px;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}
.btn10 {
  margin: 2px;
  padding: 4px 12px !important;
  height: auto !important;
  line-height: normal !important;
}
.dialogCenterDiv /deep/ .el-dialog {
  position: static;
}
</style>