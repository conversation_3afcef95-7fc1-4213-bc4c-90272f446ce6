<template>
  <div class="base-page" v-loading="showLoading">
    <!-- 列表 -->
    <div class="right">
      <el-tabs  style="margin-left: 20px" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="入库结算" name="first">
          <div class="e-table">
            <div class="top">
              <div class="left">
                <div class="left-btn dfa" style="flex-wrap: wrap;">
                  <el-button type="primary" class="btn-greenYellow" @click="add">新增入库结算单</el-button>
                  <el-button type="primary" class="btn-blue" :disabled="dataListSelections.length != 1 " @click="exportData">导出</el-button>
                </div>
              </div>
              <div class="search_box" style="width: 400px">
                <el-input
                    clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字"
                    v-model="init.keywords"
                >
                  <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                </el-input>
                <div class="adverse">
                  <el-button type="primary" size="small" @click="highSearch">高级搜索</el-button>
                </div>
              </div>
            </div>
          </div>
          <!--表格-->
          <div class="e-table">
            <el-table
                @row-click="handleCurrentInventoryClick2" ref="mainTable2" v-loading="tableLoading" class="table"
                :height="rightTableHeight" :data="tableData" border
                @selection-change="selectionChangeHandle"
            >
              <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
              <el-table-column label="序号" type="index" width="60"></el-table-column>
              <el-table-column label="合同编号" width="160">
                <template v-slot="scope">
                  <span class="action" @click="handleView(scope.row)">{{ scope.row.contractNo }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" prop="inboundType">
                <template v-slot="scope">
                  <span v-if="scope.row.inboundType==1">手动入库</span>
                  <span v-if="scope.row.inboundType==2">自动入库</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" prop="auditStatus">
                <template v-slot="scope">
                  <el-tag v-if="scope.row.auditStatus==0" type="info">待提交</el-tag>
                  <el-tag v-if="scope.row.auditStatus==1">待审核</el-tag>
                  <el-tag v-if="scope.row.auditStatus==2" type="success">已通过</el-tag>
                  <el-tag v-if="scope.row.auditStatus==3" type="danger">已驳回</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="供货公司" prop="supplierName"></el-table-column>
              <el-table-column label="商品类型" prop="supplierType">
                <template v-slot="scope">
                  <span v-if="scope.row.supplierType==0">低值易耗品</span>
                  <span v-if="scope.row.supplierType==1">大宗临购</span>
                  <span v-if="scope.row.supplierType==2">周转材料</span>
                </template>
              </el-table-column>
              <el-table-column label="采购进价(含税)" prop="rateAmount"></el-table-column>
              <el-table-column label="采购进价(不含税)" prop="noRateAmount"></el-table-column>
              <el-table-column label="数量" prop="num"></el-table-column>
              <el-table-column label="库房" prop="warehouseId">
                <template v-slot="scope">
                  <span v-if="scope.row.warehouseId==1">库房一</span>
                </template>
              </el-table-column>
              <el-table-column label="收件人" prop="receiveName"/>
              <el-table-column label="收件手机号" prop="receivePhone"/>
              <el-table-column label="创建时间" width="160" prop="gmtCreate"/>

            </el-table>
          </div>
          <!--            分页-->
          <Pagination
              v-show="tableData != null || tableData.length != 0"
              :total="paginationInfo.total"
              :pageSize.sync="paginationInfo.pageSize"
              :currentPage.sync="paginationInfo.currentPage"
              @currentChange="getTableData"
              @sizeChange="getTableData"
          />
        </el-tab-pane>
        <el-tab-pane label="待审核" name="second">
          <div class="e-table">
            <div class="top">
              <div class="left">
                <div class="left-btn dfa" style="flex-wrap: wrap;">
                  <el-button type="primary" class="btn-blue" :disabled="dataListSelections.length != 1 " @click="exportData">导出</el-button>
                </div>
              </div>
              <div class="search_box" style="width: 400px">
                <el-input
                    clearable type="text" @blur="handleInputSearch" placeholder="输入搜索关键字"
                    v-model="init.keywords"
                >
                  <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                </el-input>
                <div class="adverse">
                  <el-button type="primary" size="small" @click="highSearch">高级搜索</el-button>
                </div>
              </div>
            </div>
          </div>
          <!--表格-->
          <div class="e-table">
            <el-table
                @row-click="handleCurrentInventoryClick2" ref="mainTable2" v-loading="tableLoading" class="table"
                :height="rightTableHeight" :data="tableData" border
                @selection-change="selectionChangeHandle"
            >
              <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
              <el-table-column label="序号" type="index" width="60"></el-table-column>
              <el-table-column label="合同编号" width="160">
                <template v-slot="scope">
                  <span class="action" @click="handleView(scope.row)">{{ scope.row.contractNo }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" prop="inboundType">
                <template v-slot="scope">
                  <span v-if="scope.row.inboundType==2">自动入库</span>
                  <span v-if="scope.row.inboundType==1">手动入库</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" prop="auditStatus">
                <el-tag type="info">待审核</el-tag>
              </el-table-column>
              <el-table-column label="供货公司" prop="supplierName"></el-table-column>
              <el-table-column label="商品类型" prop="supplierType">
                <template v-slot="scope">
                  <span v-if="scope.row.supplierType==0">低值易耗品</span>
                  <span v-if="scope.row.supplierType==1">大宗临购</span>
                  <span v-if="scope.row.supplierType==2">大宗临购清单</span>
                </template>
              </el-table-column>
              <el-table-column label="总金额(含税)" prop="rateAmount"></el-table-column>
              <el-table-column label="数量" prop="num"></el-table-column>
              <!-- <el-table-column label="成本价" prop="costPrice"/> -->
              <el-table-column label="库房" prop="warehouseId"/>
              <el-table-column label="收件人" prop="receiveName"/>
              <el-table-column label="收件手机号" prop="receivePhone"/>
              <el-table-column label="创建时间" width="160" prop="gmtCreate"/>

            </el-table>
          </div>
          <!--            分页-->
          <Pagination
              v-show="tableData != null || tableData.length != 0"
              :total="paginationInfo.total"
              :pageSize.sync="paginationInfo.pageSize"
              :currentPage.sync="paginationInfo.currentPage"
              @currentChange="getTableData"
              @sizeChange="getTableData"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <!--高级查询-->
    <el-dialog class="dialogHeightAuto" v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="40%">
      <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
        <el-row>
          <el-col :span="22">
            <el-form-item label="合同编号：">
              <el-input
                  clearable maxlength="100" placeholder="请输入商品编码" v-model="filterData.contractNo"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="供货机构：">
              <el-input
                  clearable maxlength="100" placeholder="请输入供货机构"
                  v-model="filterData.supplierName"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="类型：">
              <el-radio v-model="filterData.inboundType" :label="1">手动入库</el-radio>
              <el-radio v-model="filterData.inboundType" :label="2">自动入库</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="activeName == 'first'">
          <el-col :span="22">
            <el-form-item label="审核状态：">
              <el-radio v-model="filterData.auditStatus" :label="0">待提交</el-radio>
              <el-radio v-model="filterData.auditStatus" :label="1">待审核</el-radio>
              <el-radio v-model="filterData.auditStatus" :label="2">已通过</el-radio>
              <el-radio v-model="filterData.auditStatus" :label="3">已驳回</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="创建时间：">
              <el-date-picker style="width: 100%;"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="filterData.createDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import ImportExcel from '@/components/importExcel.vue'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapMutations, mapState } from 'vuex'
import { getInBoundList, listInBoundExport } from '@/api/supplierSys/stock/inBoundManage'

export default {
    components: {
    // eslint-disable-next-line vue/no-unused-components
        SelectMaterialClass, Pagination, ImportExcel
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            showLoading: false,
            activeName: 'first',
            tableLoading: false,
            // 表格数据
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
                // state: [0, 2],
                productType: 0,
                orderBy: 2,
                classId: null,
                keywords: null,
                classPath: [],
            },
            // 商品库
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                supplierName: null,
                auditStatus: null,
                contractNo: null,
                createDate: [], // 创建时间
                inboundType: null,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
    // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    //this.getFixed()
    },
    methods: {
        handleClick () {
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        skipView (data) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/analysis/inBoundDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'inBoundDetail',
                params: {
                    row: data
                }
            })
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        exportData () {
            this.$confirm('确认导出入库结算单吗？', '提示', {}).then(() => {
                let params = {
                    id: this.dataListSelections[0].id
                }
                this.tableLoading = true
                listInBoundExport(params).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '入库结算单.xlsx'
                    a.click()
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            })
        },
        // 物资详情
        handleView (row) {
            this.skipView(row)
        },
        resetSearchConditions () {
            this.filterData.supplierName = ''
            this.filterData.productName = ''
            this.filterData.serialNum = ''
            this.filterData.createDate = []
            this.filterData.auditStatus = null
            this.filterData.inboundType = null
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        highSearch () {
            this.resetSearchConditions()
            this.queryVisible = true
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.mainTable2.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            this.tableData = []
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.contractNo != null) {
                params.contractNo = this.filterData.contractNo
            }
            if (this.filterData.auditStatus != null) {
                params.auditStatus = this.filterData.auditStatus
            }
            if(this.activeName == 'second') {
                params.auditStatus = 0
            }
            if (this.init.keywords != null) {
                params.keywords = this.init.keywords
            }
            if (this.filterData.createDate != null) {
                params.startCreateDate = this.filterData.createDate[0]
                params.endCreateDate = this.filterData.createDate[1]
            }
            this.tableLoading = true
            getInBoundList(params).then(res => {
                if (res && res.list && res.list.length) {
                    this.shopState = res.list[0].shopState
                }
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
}

.el-dialog__body {
  margin: 220px;
}

.base-page .left {
  min-width: 200px;
  height: 100%;
  padding: 0;
  overflow: auto;
}

.base-page {
  width: 100%;
  height: 100%;
}

/deep/ .el-dropdown {
  min-width: 75px;
  margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
  display: flex;
  align-items: center;
}

.e-table {
  min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
  .el-dialog__body {
    height: 400px;
    margin-top: 0px;
  }
}
.dialogHeightAuto /deep/ .el-dialog__body {
  height: auto;
}
</style>
