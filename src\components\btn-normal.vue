<template>
    <div>
        <el-button :type="buttonType" :size="buttonSize" class="btn10" @click="handleClick">
            {{ buttonText }}
        </el-button>
    </div>
</template>

<script>
export default {
    name: 'DynamicButton',
    props: {
        buttonText: {
            type: String,
            default: '高级搜索'
        },
        buttonType: {
            type: String,
            default: 'primary',
            validator: value => {
                return ['primary', 'success', 'warning', 'danger', 'info', 'text', ''].includes(value)
            }
        },
        buttonSize: {
            type: String,
            default: 'small',
            validator: value => {
                return ['medium', 'small', 'mini', ''].includes(value)
            }
        },
    },
    methods: {
        handleClick () {
            this.$emit('click')
        }
    }
}
</script>

<style lang="scss" scoped>

.btn10 {
    margin: 2px;
    padding: 4px 12px !important;
    height: auto !important;
    line-height: normal !important;
}
</style>
