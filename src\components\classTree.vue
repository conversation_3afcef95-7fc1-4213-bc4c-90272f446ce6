<template>
    <div class="tree" style="max-height: 10vh" v-loading="treeLoading">
        <div class="search_box">
            <el-input
                clearable
                class="ipt"
                type="text"
                placeholder="输入搜索关键字"
                v-model="searchKey"
                @select="onSearch"
                @change="onSearch"
            >
                <img src="@/assets/search.png" slot="suffix" @click="onSearch"  alt=""/>
            </el-input>
        </div>
        <el-tree
            :props="props"
            node-key="classId"
            @node-click="handleNodeClick"
            :data="treeData"
        >
        </el-tree>
    </div>
</template>

<script>
import { treeByName, } from '@/api/platform/product/productCategory'
export default {
    props: ['productType', 'isHaveProduct', 'isLc', 'clickRefresh'],
    data () {
        return {
            treeLoading: false,
            treeData: null, // 树data
            searchKey: null, // 关键字
            props: {
                label: 'className',
                children: 'children',
            },
            nodePath: [],
        }
    },
    created () {
        this.onSearch()
    },
    methods: {
        // 搜索框
        onSearch () {
            let params = {
                productType: this.productType,
                state: 1
            }
            if(this.searchKey != null) {
                params.className = this.searchKey
            }
            if(this.isHaveProduct != null) {
                params.isHaveProduct = this.isHaveProduct
            }
            // 临购分类处理
            if(this.isLc != null) {
                params.isLc = this.isLc
            }
            this.treeLoading = true
            treeByName(params).then(res => {
                this.treeData = res
                this.treeLoading = false
            })
        },
        // 树节点点击，调用父组件函数（递归）
        handleNodeClick (data, node) {
            this.nodePath = []
            this.getNodePath(node)
            this.nodePath = this.nodePath.reverse()
            if(this.$parent.classNodeClick == null) {
                if(this.clickRefresh && this.$parent.init && this.$parent.init.classId && this.$parent.init.classId == data.classId ) {
                    this.$emit('classNodeClick', { classId: null })
                }else {
                    this.$emit('classNodeClick', data)
                }
            }else {
                if(this.clickRefresh && this.$parent.init && this.$parent.init.classId && this.$parent.init.classId == data.classId ) {
                    this.$parent.classNodeClick({ classId: null }, this.nodePath)
                }else {
                    this.$parent.classNodeClick(data, this.nodePath)
                }
            }
        },
        getNodePath (node) {
            if(node.parent == null) {
                return
            }
            this.nodePath.push(node.data.classId)
            node = node.parent
            this.getNodePath(node)
        },
    }
}
</script>

<style lang="scss" scoped>
.search_box {
    flex-direction: column; margin: 5px 0;
}
/deep/ .el-input__inner {
    border-radius: 5px;
}
/deep/ .el-tree-node__content{
    width: 100%;
    .tree{display: flex; align-items: center;
        .txt{max-width: 134px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
        .btn{display: none; margin-left: 5px; width: 22px; height: 22px;}
    }
    &:hover{
        .tree{
            .btn{display: block;}
        }
    }
}
</style>