<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="待审核" name="first">
                        <SearchBar @search="searchM" :stateType="stateType" />
                    </el-tab-pane>
                    <el-tab-pane label="发布竞价列表" name="second">
                        <div class="top">
                            <div class="left">
                                <div class="left-btn">
                                    <el-button type="primary" class="btn10"
                                        @click="createBidingClickM">生成订单商品竞价</el-button>
                                    <el-button type="primary" class="btn10"
                                        @click="createInventoryBiding">生成清单商品竞价</el-button>
                                    <el-button type="primary" class="btn10" @click="submitBidingAudit">提交审核</el-button>
                                </div>
                            </div>
                            <SearchBar @search="searchM" :stateType="stateType" />
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <!--表格-->
            <div class="e-table">
                <el-table class="table" ref="tableRef" :height="rightTableHeight" @selection-change="tableSelectM"
                    @row-click="tableRowClickM" v-loading="tableLoading" :data="tableData" border>
                    <el-table-column type="selection" width="40"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="260">
                        <template slot-scope="scope">
                            <!-- <el-popconfirm v-if="scope.row.state == 5" confirm-button-text="确认" cancel-button-text="取消"
                icon="el-icon-info" icon-color="red" @confirm="bidOpening(scope.row)" style="margin-right: 5px"
                title="确定要开标吗？">
                <el-button size="mini" type="text" slot="reference">开标</el-button>
              </el-popconfirm> -->

                            <el-button v-if="scope.row.state == 5" size="mini" type="text" @click="openClick(scope.row)">开标</el-button>
                            <el-button size="mini" type="text" v-if="
                                scope.row.state == 0 ||
                                scope.row.state == 1 ||
                                scope.row.state == 2 ||
                                scope.row.state == 5
                            " @click="deadlineM(scope.row)" :disabled="scope.row.status === 0">延长截止时间</el-button>
                            <el-button size="mini" type="text" @click="handleView(scope.row)">详情</el-button>
                            <el-popconfirm v-if="
                                scope.row.state == 0 ||
                                scope.row.state == 1 ||
                                scope.row.state == 2 ||
                                scope.row.state == 5
                            " confirm-button-text="确认" cancel-button-text="取消" icon="el-icon-info" icon-color="red"
                                @confirm="handleDelete(scope.row)" style="margin-right: 10px" title="确定要删除吗？">
                                <el-button size="mini" type="text" :style="scope.row.state == 0 ||
                                    scope.row.state == 1 ||
                                    scope.row.state == 2 ||
                                    scope.row.state == 5
                                    ? 'color:#f56c6c'
                                    : 'color:#F9A6A6'
                                    " slot="reference">删除</el-button>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                    <el-table-column label="竞价编号" width="300" prop="biddingSn">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{
                                scope.row.biddingSn
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="标题" width="200" prop="title"></el-table-column>
                    <el-table-column label="竞价采购类型" width="90" prop="type">
                        <template v-slot="scope">
                            <span v-if="scope.row.type == 1"><el-tag type="success">公开竞价</el-tag></span>
                            <span v-if="scope.row.type == 2"> <el-tag>邀请竞价</el-tag></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="截止时间" width="160" prop="endTime">
                        <template v-slot="scope">
                            <span>{{ scope.row.endTime | dateStr }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品类型" prop="productType" width="140">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.productType == 0">低值易耗品</el-tag>
                            <el-tag v-if="scope.row.productType == 1">大宗临购订单</el-tag>
                            <el-tag v-if="scope.row.productType == 2">周转材料清单</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="生成来源" prop="biddingSourceType" width="100">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.biddingSourceType == 1">订单</el-tag>
                            <el-tag v-if="scope.row.biddingSourceType == 2">物资基础库</el-tag>
                            <el-tag v-if="scope.row.biddingSourceType == 3">清单</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="state">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">待提交</el-tag>
                            <el-tag v-if="scope.row.state == 1">待审核</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 2">审核失败</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 5">提交审核通过</el-tag>
                            <el-tag v-if="scope.row.state == 4">已开标</el-tag>
                            <el-tag type="" v-if="scope.row.state == 6">中标待审核</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 7">已中标</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 9">已流标</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="时间状态" width="100" prop="biddingState">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.biddingState == 1">未开始</el-tag>
                            <el-tag type="success" v-if="scope.row.biddingState == 2">进行中</el-tag>
                            <el-tag type="danger" v-if="scope.row.biddingState == 3">已结束</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="联系人名称" width="" prop="linkName" />
                    <el-table-column label="联系电话" width="150" prop="linkPhone" />
                    <el-table-column label="发布时间" width="160" prop="startTime" />
                    <el-table-column label="创建时间" width="160" prop="gmtCreate" />
                </el-table>
            </div>
            <!--分页-->
            <Pagination v-show="tableData && tableData.length > 0" :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize" :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData" @sizeChange="getTableData" />
        </div>

        <!-- 延期截至时间 -->
        <el-dialog title="修改截止时间" :visible.sync="deadlineTimeDialog" width="30%" :close-on-click-modal="false">
            <el-form :model="timeExpandForm" :rules="timeExpandRules" label-width="100px" ref="timeExpandFormRef"
                :disabled="false" class="demo-ruleForm">
                <el-form-item label="截止时间" prop="endTime">
                    <el-date-picker style="width: 100%; margin-right: 20px" v-model="timeExpandForm.endTime"
                        type="datetime" placeholder="选择日期时间" :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="延期理由" prop="result">
                    <el-input type="textarea" :rows="6" style="width: 100%; margin-right: 20px" clearable
                        v-model="timeExpandForm.result" placeholder="请输入延期理由" />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button style="padding: 0px 20px" @click="deadlineTimeDialog = false">取 消</el-button>
                <el-button style="padding: 0px 20px" type="primary" @click="deadlineTimeHandle">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 开标弹框 -->
        <el-dialog title="开标" :visible.sync="openBiddingDialog" width="30%">
            <!-- 添加一个容器用于居中 -->
             <div>
                您确定对:
             </div>
            <div style="font-size: 16px; display: flex; justify-content: center; align-items: center; height: 100%;">
                竞价编号 <span style="color: #000;">&nbsp; &nbsp; {{ openBiddingRow.biddingSn }} &nbsp; &nbsp;</span>
                ，标题 <span style="color: #000;">&nbsp; &nbsp;{{ openBiddingRow.title }} &nbsp; &nbsp;</span>开标
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button style="padding: 0px 20px" @click="openBiddingDialog = false">取 消</el-button>
                <el-button style="padding: 0px 20px" type="primary" @click="bidOpening(openBiddingRow)">确 定</el-button>
            </span>
        </el-dialog>

        <SupplierModel ref="supplierModelRef" />
        <orderBidingModel ref="orderBidingModelRef" @getList="getBidingList" />
        <inventory-biding-model ref="inventoryBidingModelRef" @getList="getBidingList" />
    </div>
</template>

<script>
//工具
import { mapState } from 'vuex'
import '@/utils/jquery.scrollTo.min'
import { debounce, } from '@/utils/common'

//接口
import {
    listMyCreateBiding,
    submitBidingByIds,
    bidOpening,
    deadlineTime,
    deleteBidingByBiddingId,
} from '@/api/shopManage/biding/biding'

//组件
import SupplierModel from '../components/supplier-model.vue'
import orderBidingModel from '../components/order-biding-model.vue'
import InventoryBidingModel from '../components/inventory-biding-model.vue'
import SearchBar from '../components/search-bar.vue'
import Pagination from '@/components/pagination/pagination'
export default {
    components: {
        Pagination,
        SearchBar,
        SupplierModel,
        orderBidingModel,
        InventoryBidingModel
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            },
        },
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return this.screenWidth - 300 + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        },
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) return
            let newDateSr = dateStr.split(' ')
            return newDateSr[0]
        },
    },
    data () {
        return {
            openBiddingRow: {},
            openBiddingDialog: false,
            rowData: {},
            stateType: 0,
            timeExpandForm: {
                endTime: null,
                result: null,
            },
            deadlineTimeDialog: false,
            // 第二个选择框的供应商
            activeName: 'first',
            selectedSupplierList: [],
            materialSelectRow: [],
            // 选中供应商
            selectedSupplierRow: [],
            // 选中供应商名称
            selectedSupplierRowName: [],
            checkAllShop: false, //全选控制
            tableSelectRow: [],

            timeExpandRules: {
                endTime: [{ required: true, message: '延期时间', trigger: 'blur' }],
                result: [{ required: true, message: '延期时间', trigger: 'blur' }],
            },
            orderProductType: 0,
            infoStr: '选择供应商',
            bidingOrderList: [],
            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() < Date.now()
                },
            },
            showBidingForm: false,
            tableLoading: false,

            // 表格数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },

            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                createDate: [],
                endDate: [],
                orderSn: null,
                title: null,
                biddingSn: null,
                state: null,
                productType: null,
                stateSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '待提交' },
                    { value: 1, label: '待审核' },
                    { value: 2, label: '审核失败' },
                    { value: 5, label: '审核通过' },
                    // { value: 7, label: '已确认' },
                    { value: 9, label: '已流标' },
                ],
                productTypeSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '低值易耗品' },
                    { value: 1, label: '大宗临购' },
                    { value: 2, label: '大宗临购清单' },
                ],
                biddingState: null,
                biddingStateSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '未开始' },
                    { value: 2, label: '进行中' },
                    { value: 3, label: '已结束' },
                ],
                publicityState: null,
                publicityStateSelect: [
                    { value: null, label: '全部' },
                    { value: 0, label: '未发布' },
                    { value: 1, label: '已发布' },
                ],
                orderBy: 1,
                type: null,
                typeSelect: [
                    { value: null, label: '全部' },
                    { value: 1, label: '公开竞价' },
                    { value: 2, label: '邀请竞价' },
                ],
            },
            orderSn: null,
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.filterData.state = null
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        if (this.$route.query.Tab == 'second') {
            this.activeName = 'second'
            this.filterData.state = null
            this.filterData.biddingState = null
            this.getTableData()
        } else {
            this.activeName = 'first'
            this.filterData.state = 1
            this.getTableData()
        }
    },
    created () { },
    methods: {

        openClick (row) {
            console.log('openClick', row)
            this.openBiddingRow = row
            this.openBiddingDialog = true
        },
        searchM (params) {
            params.state = this.stateType
            this.filterData = { ...params }
            this.getTableData()
        },
        handleClick (tab) {
            if (tab.label === '待审核') {
                this.filterData.state = 1
                this.stateType = 1
                this.getTableData()
            } else {
                this.filterData.state = null
                this.filterData.biddingState = null
                this.stateType = null
                this.getTableData()
            }
        },
        handleDelete (row) {
            deleteBidingByBiddingId({ biddingId: row.biddingId })
                .then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('删除成功')
                        // this.$router.go(-1)
                        this.getTableData()
                    }
                })
                .finally(() => {
                    // this.formLoading = false
                })
        },
        deadlineM (row) {
            this.rowData = row
            this.deadlineTimeDialog = true
        },
        // deadlineTimeHandle () {
        //     const params = {
        //         biddingId: this.rowData.biddingId,
        //         deadlineTime: this.timeExpandForm.endTime,
        //         deadlineTimeResult: this.timeExpandForm.result,
        //     }
        //     deadlineTime(params).then(() => {
        //         this.$message.success('延长截止时间成功！')
        //         this.getTableData()
        //         this.deadlineTimeDialog = false
        //     })
        // },
        deadlineTimeHandle () {
            this.$refs.timeExpandFormRef.validate(valid => {
                if (!valid) {
                    this.$message.warning('请填写完整信息')
                    return false
                }
                const params = {
                    biddingId: this.rowData.biddingId,
                    deadlineTime: this.timeExpandForm.endTime,
                    deadlineTimeResult: this.timeExpandForm.result,
                }
                deadlineTime(params).then(() => {
                    this.$message.success('延长截止时间成功！')
                    this.getTableData()
                    this.deadlineTimeDialog = false
                    this.$refs.timeExpandFormRef.resetFields()
                }).catch(() => {
                    this.$refs.timeExpandFormRef.resetFields()
                })
            })
        },
        bidOpening (row) {
            const params = {
                id: row.biddingId,
            }
            bidOpening(params).then(() => {
                this.$message.success('开标成功，请前往开标记录查看！')
                this.getTableData()
                this.openBiddingDialog = false
            })
        },

        createInventoryBiding () {
            // this.inventoryBidFormVisible = true
            this.$refs.inventoryBidingModelRef.openDialog()
        },
        tableSelectM (value) {
            this.tableSelectRow = value
        },

        tableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.tableRef.toggleRowSelection(row, row.flag)
        },

        createBidingClickM () {
            // 调用查询获取生成竞价
            // this.showBidingForm = true
            this.$refs.orderBidingModelRef.openDialog()
        },
        getBidingList () {
            this.getTableData()
        },
        submitBidingAudit () {
            if (this.tableSelectRow.length == 0) {
                return this.$message.error('未选择数据！')
            }
            let ids = this.tableSelectRow
                .filter(t => {
                    if (t.state == 0 || t.state == 2) {
                        return true
                    } else {
                        return false
                    }
                })
                .map(t => t.biddingId)
            if (ids.length == 0) {
                return this.$message.error('请选择待提交或审核失败数据！')
            }
            this.clientPop('info', '您确定要提交吗！', async () => {
                this.tableLoading = true
                submitBidingByIds(ids)
                    .then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('提交成功')
                            this.getTableData()
                            this.tableSelectRow = []
                        }
                    })
                    .finally(() => {
                        this.tableLoading = false
                    })
            })
            // 提交工作流
        },
        // 详情
        handleView (row) {
            const Tab = this.activeName
            console.log('tab', Tab)
            this.$router.push({
                path: '/supplierSys/bidManage/bidingListDetail',
                name: 'bidingListDetail',
                query: {
                    biddingSn: row.biddingSn,
                    Tab: Tab,
                },
            })
        },

        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            // 路由参数作为关键词
            if (this.$route.query.keywords != null) {
                this.keywords = this.$route.query.keywords
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.title != null) {
                params.title = this.filterData.title
            }
            if (this.filterData.biddingSn != null) {
                params.biddingSn = this.filterData.biddingSn
            }
            if (this.filterData.createDate != null) {
                params.createStartDate = this.filterData.createDate[0]
                params.createEndDate = this.filterData.createDate[1]
            }
            if (this.filterData.endDate != null) {
                params.startDate = this.filterData.endDate[0]
                params.endDate = this.filterData.endDate[1]
            }
            if (this.filterData.state != null) {
                params.state = this.filterData.state
            }
            if (this.filterData.biddingState != null) {
                params.biddingState = this.filterData.biddingState
            }
            if (this.filterData.productType != null) {
                params.productType = this.filterData.productType
            }
            if (this.filterData.publicityState != null) {
                params.publicityState = this.filterData.publicityState
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.type != null) {
                params.type = this.filterData.type
            }
            let { mallRoles, shopName } = this.userInfo
            if (shopName === '四川路桥自营店' && this.showDevFunc) {
                let lg = mallRoles.some(item => item.name === '大宗临购管理人员')
                if (lg) params.productType = 1
                let lx = mallRoles.some(item => item.name === '零星采购管理人员')
                if (lx) params.productType = 0
                if (lg && lx) params.productType = null
            }
            this.tableLoading = true
            listMyCreateBiding(params)
                .then(res => {
                    this.paginationInfo.total = res.totalCount
                    this.paginationInfo.pageSize = res.pageSize
                    this.paginationInfo.currentPage = res.currPage
                    this.tableData = res.list
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        getScreenInfo () {
            this.screenWidth =
                document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight =
                document.documentElement.clientHeight || document.body.clientHeight
        },
    },
}
</script>

<style lang="scss" scoped>
@import "./index.scss";

.btn10 {
    margin: 2px;
    padding: 4px 12px !important;
    height: auto !important;
    line-height: normal !important;
}
/deep/ .el-table .el-table__body .cell {
    align-items: center;
}
/deep/ .el-table .el-table__body .cell button.el-button {
    margin: 0 5px;
    vertical-align: middle;
}
/deep/ .el-table .el-table__body .cell .el-popconfirm {
    margin: 0 5px;
    vertical-align: middle;
    display: inline-flex;
    align-items: center;
}
</style>
