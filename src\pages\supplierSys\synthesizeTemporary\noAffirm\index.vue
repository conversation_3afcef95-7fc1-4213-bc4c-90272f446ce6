<template>
    <div class="right">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <el-tabs v-model="activeName" @tab-click="handleTabClick" class="toptab">
                        <el-tab-pane label="待审核" name="PendingApproval"
                                     v-if="userInfo.roles.includes('物资大宗临购清单审核权限')"></el-tab-pane>
                        <el-tab-pane label="大宗临购清单" name="BigList"></el-tab-pane>
                    </el-tabs>
                    <div class="left">
                        <div class="left-btn">
                            <!--                          <el-select @change="userOptionsClick" v-model="userOptionValue"-->
                            <!--                                     placeholder='全部'>-->
                            <!--                            <el-option v-for="item in userOptions" :key="item.value" :label="item.label"-->
                            <!--                                       :value="item.value">-->
                            <!--                            </el-option>-->
                            <!--                          </el-select>-->
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按确认时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="3">按提交时间排序</el-radio>
                        <el-input clearable style="width: 300px" type="text" @blur="handleInputSearch"
                                  placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table">
                <el-table
                    class="table" :height="rightTableHeight" v-loading="tableLoading" :data="tableData" border
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="编号" width="240" prop="synthesizeTemporarySn">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{
                                    scope.row.synthesizeTemporarySn
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="买方" width="400" prop="orgName"/>
                    <el-table-column label="卖方" width="400" prop="supplierOrgName"/>
                    <!--                    <el-table-column label="用户类型" width="100" prop="用户类型">-->
                    <!--                      <template v-slot="scope">-->
                    <!--                        <el-tag v-if="scope.row.userType == 1">PCWP</el-tag>-->
                    <!--                        <el-tag v-if="scope.row.userType == 2">非PCWP</el-tag>-->
                    <!--                      </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column label="项目收货地址" width="" prop="receiverAddress"/>
                    <el-table-column label="含税总金额（元）" width="130" prop="synthesizeSumAmount"/>
                    <el-table-column label="清单类型" width="100" prop="清单类型">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.billType == 1">浮动价格</el-tag>
                            <el-tag v-if="scope.row.billType == 2">固定价格</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="state" label="状态" width="">
                        <template v-slot="scope">
                            <el-tag type="info" v-if="scope.row.state == 0">草稿</el-tag>
                            <el-tag v-if="scope.row.state == 1">待确认</el-tag>
                            <el-tag v-if="scope.row.state == 2">待审核</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 3">审核通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 4">审核未通过</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 5">收货单位拒绝</el-tag>
                            <el-tag type="danger" v-if="scope.row.state == 11">审核不通过</el-tag>
                            <el-tag type="success" v-if="scope.row.state == 6">已推送大宗临购计划</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="确认时间" width="160" prop="auditTime"/>
                    <el-table-column label="提交时间" width="160" prop="submitTime"/>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                </el-table>
            </div>
            <!--分页-->
            <Pagination
                v-show="tableData || tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--        高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="40%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="清单类型：">
                            <el-select style="width: 100%;" v-model="filterData.selectBillTypeValue" placeholder="请选择清单类型">
                                <el-option
                                    v-for="item in filterData.billTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="状态：">
                            <el-select style="width: 100%;" v-model="filterData.selectStateValue" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="编号：">
                            <el-input clearable maxlength="100" placeholder="请输入订单号"
                                      v-model="filterData.synthesizeTemporarySn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="采购单位名称：">
                            <el-input clearable maxlength="100" placeholder="请输入商品名称"
                                      v-model="filterData.orgName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="创建时间：">
                            <el-date-picker style="width: 100%;"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.createTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="提交时间：">
                            <el-date-picker style="width: 100%;"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.submitTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="22">
                        <el-form-item label="确认时间：">
                            <el-date-picker style="width: 100%;"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.auditTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
// import { getUserShopEnterpriseInfoByUserSn, getUserList } from '@/api/platform/user/userInquire'
import { listByEntitySynthesizeTemporary } from '@/api/platform/order/orders'
import { mapState } from 'vuex'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            //标签页
            activeName: 'BigList',
            //用户状态
            userOptionValue: null,
            tableLoading: false,
            userOptions: [
                { value: null, label: '全部用户' },
                { value: 1, label: 'PCWP' },
                { value: 2, label: '非PCWP' },
            ],
            // 表格数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                orgName: null,
                selectBillTypeValue: null,  // 选中的值
                selectStateValue: null,  // 选中的值
                synthesizeTemporarySn: null,
                createTime: [],
                submitTime: [],
                auditTime: [],
                orderBy: 1,
                includedStates: [],
                billTypeOptions: [
                    {
                        value: null,
                        label: '全部'
                    }
                    , {
                        value: 1,
                        label: '浮动价格'
                    }
                    , {
                        value: 2,
                        label: '固定价格'
                    }],
                stateOptions: [
                    { value: null, label: '全部' },
                    { value: 1, label: '待确认' },
                    { value: 2, label: '待审核' },
                    { value: 3, label: '审核通过' },
                    { value: 4, label: '审核未通过' },
                    { value: 5, label: '收货单位拒绝' },
                    { value: 6, label: '已推送大宗临购计划' },
                ],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        console.log('userinfo', this.userInfo)
    },
    methods: {
        // 标签切换
        handleTabClick (tab) {
            this.activeName = tab.name
            this.paginationInfo.currentPage = 1
            this.resetSearchConditions()
            this.getTableData()
        },
        //选择用户状态进行查询
        userOptionsClick (value) {
            this.expertFIfter()
            this.userOptionValue = value
            this.getTableData()
        },
        expertFIfter () {
            //重置数据
            this.filterData.orgName = null
            this.filterData.selectBillTypeValue = null
            this.filterData.selectStateValue = null
            this.filterData.synthesizeTemporarySn = null
            this.filterData.createTime = []
            this.filterData.submitTime = []
            this.filterData.auditTime = []
            this.filterData.orderBy = 1
        },
        // 详情
        handleView (row) {
            this.$router.push({
                path: '/supplierSys/synthesizeTemporary/noAffirmDetail',
                name: 'noAffirmDetail',
                query: {
                    sn: row.synthesizeTemporarySn
                }
            })
        },
        resetSearchConditions () {
            this.filterData.synthesizeTemporarySn = null
            this.filterData.orgName = null
            this.filterData.selectBillTypeValue = null
            this.filterData.selectStateValue = null
            this.filterData.createTime = []
            this.filterData.submitTime = []
            this.filterData.auditTime = []
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = null
            this.getTableData()
            this.queryVisible = false
        },
        // 获取表格数据
        // getTableData () {
        //     let params = {
        //         page: this.paginationInfo.currentPage,
        //         limit: this.paginationInfo.pageSize,
        //     }
        //     if (this.userOptionValue != null) {
        //         params.type = this.userOptionValue
        //     }
        //     if (this.keywords != null && this.keywords != '') {
        //         params.keywords = this.keywords
        //     }
        //     if (this.filterData.selectBillTypeValue != null) {
        //         params.billType = this.filterData.selectBillTypeValue
        //     }
        //     if (this.filterData.selectStateValue != null) {
        //         params.state = this.filterData.selectStateValue
        //     }
        //     if (this.filterData.orderBy != null) {
        //         params.orderBy = this.filterData.orderBy
        //     }
        //     if (this.filterData.synthesizeTemporarySn != null) {
        //         params.synthesizeTemporarySn = this.filterData.synthesizeTemporarySn
        //     }
        //     if (this.filterData.orgName != null) {
        //         params.orgName = this.filterData.orgName
        //     }
        //     if (this.filterData.createTime != null) {
        //         params.startCreateTime = this.filterData.createTime[0]
        //         params.endCreateTime = this.filterData.createTime[1]
        //     }
        //     if (this.filterData.submitTime != null) {
        //         params.startSubmitTime = this.filterData.submitTime[0]
        //         params.endSubmitTime = this.filterData.submitTime[1]
        //     }
        //     if (this.filterData.auditTime != null) {
        //         params.startAuditTime = this.filterData.auditTime[0]
        //         params.endAuditTime = this.filterData.auditTime[1]
        //     }
        //     this.tableLoading = true
        //     listByEntitySynthesizeTemporary(params).then(res => {
        //         if (this.activeName === 'BigList') {
        //             this.paginationInfo.pageSize = res.pageSize
        //             this.paginationInfo.currentPage = res.currPage
        //             this.tableData = res.list.filter(item => item.state !== 2) || []
        //             this.paginationInfo.total = this.tableData.length
        //         }
        //         if (this.activeName === 'PendingApproval') {
        //             this.paginationInfo.pageSize = res.pageSize
        //             this.paginationInfo.currentPage = res.currPage
        //             this.tableData = res.list.filter(item => item.state !== 1 && item.state !== 3 && item.state !== 4 && item.state !== 5 && item.state !== 6 && item.state !== 11) || []
        //             this.paginationInfo.total = this.tableData.length
        //         }
        //     }).finally(() => {
        //         this.tableLoading = false
        //     })
        // },
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.userOptionValue != null) {
                params.type = this.userOptionValue
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.selectBillTypeValue != null) {
                params.billType = this.filterData.selectBillTypeValue
            }
            if (this.filterData.selectStateValue != null) {
                params.state = this.filterData.selectStateValue
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            if (this.filterData.synthesizeTemporarySn != null) {
                params.synthesizeTemporarySn = this.filterData.synthesizeTemporarySn
            }
            if (this.filterData.orgName != null) {
                params.orgName = this.filterData.orgName
            }
            if (this.filterData.createTime != null && this.filterData.createTime.length === 2) {
                params.startCreateTime = this.filterData.createTime[0]
                params.endCreateTime = this.filterData.createTime[1]
            }
            if (this.filterData.submitTime != null && this.filterData.submitTime.length === 2) {
                params.startSubmitTime = this.filterData.submitTime[0]
                params.endSubmitTime = this.filterData.submitTime[1]
            }
            if (this.filterData.auditTime != null && this.filterData.auditTime.length === 2) {
                params.startAuditTime = this.filterData.auditTime[0]
                params.endAuditTime = this.filterData.auditTime[1]
            }
            if (this.activeName === 'PendingApproval') {
                params.state = 2
                this.filterData.selectStateValue = null
            } else if (this.activeName === 'BigList') {
                params.states = [1, 3, 4, 5, 6, 11]
            }
            this.tableLoading = true
            listByEntitySynthesizeTemporary(params).then(res => {
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableData = res.list || []
                this.paginationInfo.total = res.totalCount
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}

.top {
    height: 80px;
}

.toptab {
    display: block;
    //border: 1px solid red;
    padding-left: 20px;
    padding-right: 20px;
    width: 100%;
    height: 50px;
    border-radius: 5px;
    background-color: white;
}

.search_box {
    padding-top: 25px;
    position: relative;
    bottom: 0;
}
</style>
