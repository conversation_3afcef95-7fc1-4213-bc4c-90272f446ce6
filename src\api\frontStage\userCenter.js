import service from '@/utils/request'
// import qs from 'qs'
// eslint-disable-next-line
const { httpPost, httpGet } = service

const createShopInside = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/createShopInside',
        params,
    })
}
const getAuditState = params => {
    return httpGet({
        url: '/materialMall/userCenter/user/getAuditState',
        params
    })
}

const getShoppingCartList = params => {
    return httpGet({
        url: '/materialMall/userCenter/shoppingCart/listCart',
        params,
    })
}
const isSynthesizeTemporary = params => {
    return httpPost({
        url: '/materialMall/userCenter/shoppingCart/check/isSynthesizeTemporary',
        params,
    })
}
const createSynthesizeTemporary = params => {
    return httpPost({
        url: '/materialMall/userCenter/synthesizeTemporary/createSynthesizeTemporary',
        params,
    })
}
const updateSynthesizeTemporary = params => {
    return httpPost({
        url: '/materialMall/userCenter/synthesizeTemporary/updateInfo',
        params,
    })
}
const deleteInfoItem = params => {
    return httpGet({
        url: '/materialMall/userCenter/synthesizeTemporary/deleteInfoItem',
        params,
    })
}
const deleteSynthesizeTemporary = params => {
    return httpGet({
        url: '/materialMall/userCenter/synthesizeTemporary/deleteInfo',
        params,
    })
}
const refuseBusiness = params => {
    return httpPost({
        url: '/materialMall/userCenter/synthesizeTemporary/refuseBusiness',
        params,
    })
}
const submitPlan = params => {
    return httpGet({
        url: '/materialMall/userCenter/synthesizeTemporary/submitSynthesizeTemporaryPlan',
        params,
    })
}

const submitTunOverMaterialPlan = params => {
    return httpPost({
        url: '/materialMall/userCenter/synthesizeTemporary/submitTunOverMaterialPlan',
        params,
    })
}

const changeCartNum = params => {
    return httpGet({
        url: '/materialMall/userCenter/shoppingCart/countCart',
        params
    })
}

const deleteSku = params => {
    return httpPost({
        url: '/materialMall/userCenter/shoppingCart/deleteBatch',
        params
    })
}
const batchUpdateItems = params => {
    return httpPost({
        url: '/materialMall/supplier/synthesizeTemporary/batchUpdateItems',
        params
    })
}

const emptyCart = params => {
    return httpGet({
        url: '/materialMall/userCenter/shoppingCart/empty',
        params
    })
}

const getCartNum = params => {
    return httpGet({
        url: '/materialMall/userCenter/shoppingCart/getCartNum',
        params
    })
}

const getMsgNum = params => {
    return httpGet({
        url: '/materialMall/userCenter/message/getMessageNum',
        params
    })
}
// 退出登陆
const loginOut = params => {
    return httpGet({
        url: '/materialMall/userCenter/user/loginOut',
        params
    })
}
// 切换企业
const cutOrg = params => {
    params.mallType = 0
    return httpPost({
        url: '/materialMall/userCenter/user/cutOrg',
        params
    })
}
const pushCardProductsArrive = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/pushCardProductsArrive',
        params
    })
}

const createRatail = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/create_retail_plan',
        params
    })
}
const createBulkRatail = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/create_synthesize_plan',
        params
    })
}
const createReval = params => {
    return httpPost({
        url: '/materialMall/userCenter/user/create_reval_plan',
        params
    })
}

const updateChecked = params => {
    return httpGet({
        url: '/materialMall/userCenter/shoppingCart/updateChecked',
        params
    })
}

const updateCartInfo = params => {
    return httpPost({
        url: '/materialMall/userCenter/shoppingCart/updateCartInfo',
        params
    })
}
const synthesizeTemporaryList = params => {
    return httpPost({
        url: '/materialMall/userCenter/synthesizeTemporary/getThisOrgList',
        params
    })
}
const synthesizeTemporaryGetBySn = params => {
    return httpGet({
        url: '/materialMall/userCenter/synthesizeTemporary/getBySn',
        params
    })
}
const suppliersSynthesizeTemporaryGetBySn = params => {
    return httpGet({
        url: '/materialMall/supplier/synthesizeTemporary/getBySn',
        params,
    })
}
const auditBusinessST = params => {
    return httpPost({
        url: '/materialMall/supplier/synthesizeTemporary/auditBusiness',
        params,
    })
}
const supplierDeleteInfo = params => {
    return httpGet({
        url: '/materialMall/supplier/synthesizeTemporary/supplierDeleteInfo',
        params
    })
}
const auditRefuseOrg = params => {
    return httpPost({
        url: '/materialMall/supplier/synthesizeTemporary/auditRefuseOrg',
        params
    })
}
const exportExcel = params => {
    return httpGet({
        url: '/materialMall/supplier/synthesizeTemporary/exportExcel',
        params,
        responseType: 'blob'
    })
}
const updateState = params => {
    return httpPost({
        url: '/materialMall/supplier/synthesizeTemporary/updateState',
        params
    })
}
export {
    loginOut,
    createShopInside,
    getShoppingCartList,
    exportExcel,
    deleteInfoItem,
    supplierDeleteInfo,
    createSynthesizeTemporary,
    suppliersSynthesizeTemporaryGetBySn,
    synthesizeTemporaryList,
    batchUpdateItems,
    synthesizeTemporaryGetBySn,
    deleteSynthesizeTemporary,
    changeCartNum,
    auditRefuseOrg,
    submitPlan,
    submitTunOverMaterialPlan,
    deleteSku,
    emptyCart,
    getCartNum,
    getMsgNum,
    auditBusinessST,
    cutOrg,
    refuseBusiness,
    updateSynthesizeTemporary,
    getAuditState,
    pushCardProductsArrive,
    isSynthesizeTemporary,
    updateChecked,
    updateCartInfo,
    createRatail,
    createBulkRatail,
    createReval,
    updateState
}
