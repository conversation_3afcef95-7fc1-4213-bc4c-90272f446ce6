<!-- 周转材料专区 -->
<template>
  <div class="main center front">
    <template v-if="ItemData.name == '平台自营'">
      <div class="titleBox dfb">
        <div class="dfa left">
          <!-- <div v-if="tabItem.className == '平台自营'">合作商家</div> -->
          <div>合作商家</div>
          <span class="ml10">精细挑选 为您选购</span>
        </div>
        <div
          class="right"
          @click="
            openWindowTab({
              path: '/mFront/shopList',
              query: { currentTab: currentTab },
            })
          "
        >
          更多<i class="el-icon-arrow-right"></i>
        </div>
      </div>
      <div class="qyList df">
        <div
          class="mb20 dfa pointer"
          v-for="item in companiesList"
          :key="item.shopId"
          @click="
            openWindowTab({
              path: '/mFront/shopIndex',
              query: { shopId: item.shopId, currentTab: currentTab },
            })
          "
        >
          <el-image
            style="width: 55px; height: 55px; margin-right: 15px"
            :src="
              item.shopImg
                ? imgUrlPrefixAdd + item.shopImg
                : require('@/assets/images/img/queshen5.png')
            "
            fit="cover"
            lazy
          />
          <span>{{ item.shopName }}</span>
        </div>
      </div>
    </template>
    <div v-for="(item, i) in floorData" :key="i">
      <div class="titleBox dfb">
        <div class="dfa left">
          <div>{{ item.floorName }}</div>
          <span class="ml10">{{ item.floorNameText }}</span>
        </div>
        <div
          class="right"
          @click="
            openWindowTab({
              path: '/mFront/productList',
              query: { classId: item.classId, classPath: item.className },
            })
          "
        >
          更多<i class="el-icon-arrow-right"></i>
        </div>
      </div>
      <div class="goodList df mb20">
        <div class="floorImg">
          <el-image
            style="width: 250px; height: 620px"
            :src="
              item.backgroundUrl
                ? imgUrlPrefixAdd + item.backgroundUrl
                : require('@/assets/images/default_bgc.png')
            "
            fit="cover"
            alt=""
          />
          <!--<div class="floorTitle">{{ item.floorName }}</div>
                    <div class="floorSubTitle">{{ item.floorNameText }}</div>-->
          <el-image
            style="
              width: 190px;
              height: 250px;
              margin-left: -95px;
              margin-top: -50%;
              z-index: 1;
              top: 50%;
              left: 50%;
            "
            :src="
              item.imgUrl
                ? imgUrlPrefixAdd + item.imgUrl
                : require('@/assets/images/img/queshen5.png')
            "
            alt=""
            fit="cover"
          />
        </div>
        <div class="df">
          <div
            class="goodItem mb20"
            v-show="item.goodsVOS"
            v-for="(item2, i) in item.goodsVOS"
            :key="i"
          >
            <!-- <img
              :src="
                item2.productMinImg
                  ? imgUrlPrefixAdd + item2.productMinImg
                  : require('@/assets/images/img/queshen5.png')
              "
              alt=""
              @click="
                openWindowTab({
                  path: '/mFront/productDetail',
                  query: { productId: item2.productId },
                })
              "
            />
            <div class="name textOverflow1">{{ item2.productName }}</div>
            <div class="type dfc textOverflow2">{{ item2.skuName }}</div>
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
              "
            >
              <div class="price" style="margin-left: 10px">
                ￥{{ item2.sellPrice }}
              </div>
              <div style="width: 100px">
                <QuantitySelector
                  v-model="item2.goodsNum"
                  @change="handleChange(item2.productId, $event)"
                  :min="1"
                  :max="100000"
                />
              </div>
              <img
                style="width: 30px; height: 30px; margin-right: 10px"
                src="@/assets/images/shopping_cart.png"
                alt=""
              />
            </div> -->
            <GoodsItem :itemData="item2"></GoodsItem>

          </div>
        </div>
      </div>
    </div>
    <div class="userCenter mb20" v-if="topList.length > 0">
      <div class="list-title dfa">
        周转材料各地区参考价信息栏
        <div
          class="right pointer"
          @click="openWindowTab('/mFront/massPriceList')"
        >
          更多<i class="el-icon-arrow-right"></i>
        </div>
      </div>
      <div class="p20">
        <div class="dfa mb20" v-for="item in topList" :key="item.id">
          <span class="date">{{ item.gmtRelease | trimData }}</span>
          <span class="tit pointer" @click="handleView(item)">{{
            item.title
          }}</span>
        </div>
      </div>
    </div>
    <div class="userCenter" v-if="isLoggedIn">
      <div class="list-title dfa">历史交易价格</div>
      <div class="p20">
        <el-table
          ref="scrollTable"
          :data="tableData"
          :header-cell-style="{
            height: '50px',
            fontSize: '16px',
            color: '#333',
            backgroundColor: '#fafafa',
          }"
          :cell-style="{ fontSize: '14px', height: '60px' }"
          style="border: 1px solid #ebeef5"
          height="550"
          @mouseenter.native="autoScroll(true)"
          @mouseleave.native="() => autoScroll()"
        >
          <el-table-column label="时间" align="center" width="130">
            <template v-slot="scope">
                {{ scope.row.buyTime ? scope.row.buyTime.substr(0, 10) : '' }}
            </template>
          </el-table-column>
          <el-table-column label="商品名称" prop="productName" align="center" />
          <el-table-column
            label="规格"
            prop="skuName"
            align="center"
            width="130"
          />
          <el-table-column
            label="材质"
            prop="texture"
            align="center"
            width="110"
          />
          <el-table-column
            label="送货区域"
            prop="receiverAddress"
            align="center"
          />
          <el-table-column
            label="供货价格 (元)"
            prop="supplyPrice"
            align="center"
            width="240"
          />
          <el-table-column
            label="综合单价（元）"
            prop="productPrice"
            align="center"
            width="200"
          />
          <el-table-column
            label="单位"
            prop="unit"
            align="center"
            width="100"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
// import QuantitySelector from '@/components/quantity-selector'
import GoodsItem from '@/components/goods-item'
import { getWebInfo, getOrderHistory } from '@/api/frontStage/webInfo'
import { getFixedFloor } from '@/api/frontStage/floor'
import { showLoading, hideLoading } from '@/utils/common'
import { getIndexShopList, getIndexSupplierList } from '@/api/w/indexShop'
import { mapState } from 'vuex'
export default {
    name: 'index',
    components: {
        // QuantitySelector,
        GoodsItem,
    },
    props: {
        ItemData: {},
        floorGoodsList: [],
    },
    data () {
        return {
            goodsNum: 1,
            topList: [],
            selfSupportArray: [],
            otherArray: [],
            floorData: {},
            list: [],
            tableData: [],
            scrollTimer: '',
            scrollPixel: 2,
            topListPage: {
                currPage: 1,
                orderBy: 4,
                state: 1,
                programaKey: 'LcPriceAnnouncement',
            },
            mallType: 0,
        }
    },
    filters: {
        trimData (str) {
            return str.substr(0, 10)
        },
    },
    watch: {
        ItemData (newVal) {
            if (newVal.name === '平台自营') {
                this.floorData = this.selfSupportArray
            } else {
                this.floorData = this.otherArray
            }
        },
    },
    computed: {
        ...mapState(['userInfo']),
        isLoggedIn () {
            return this.userInfo.token || localStorage.getItem('token')
        },
    },
    methods: {
        handleChange (productId, quantity) {
            // 根据productId更新对应商品的数量
            console.log(`Product ${productId} quantity changed to: ${quantity}`)
            // 这里添加你的业务逻辑
        },
        getIndexShopListM () {
            if (this.currentTab == 3) {
                let params = {
                    limit: 5,
                    page: 0,
                }
                getIndexSupplierList(params).then(res => {
                    this.companiesList = res.list
                })
            } else {
                getIndexShopList({ size: 12 }).then(res => {
                    this.companiesList = res
                })
            }
        },
        leaveTable () {
            this.autoScroll()
        },
        handleView ({ contentId }) {
            this.openWindowTab({
                path: '/mFront/newsDetail',
                query: { id: contentId },
            })
        },
        getList () {
            if (!this.isLoggedIn) return
            showLoading()
            let params = {
                limit: 3,
                mallType: this.mallType,
                ...this.topListPage,
            }
            getWebInfo(params)
                .then(res => {
                    this.topList = res.list
                })
                .finally(() => hideLoading())
        },
        getHistory () {
            if (!this.isLoggedIn) return
            getOrderHistory({ limit: 16, page: 1, productType: 2 }).then(res => {
                this.list = res.list
                this.tableData = res.list
            })
        },
        getFloor () {
            // if(!this.userInfo.token) return
            getFixedFloor({ isFixed: 1, size: 8 }).then(res => {
                // 处理周转材料数据（索引1）
                const turnoverMaterials = res[1].floorVOS

                // 按shopType分组
                const groupedFloorVOS = turnoverMaterials.reduce(
                    (acc, floor) => {
                        // 确保floor.goodsVOS存在
                        const goods = floor.goodsVOS || []

                        // 按shopType分组商品
                        const groupedGoods = goods.reduce(
                            (groups, item) => {
                                if (item.shopType === 1) {
                                    groups.shopType1.push(item)
                                } else {
                                    groups.other.push(item)
                                }
                                return groups
                            },
                            { shopType1: [], other: [] }
                        )

                        // 创建两个新的floor对象
                        const floorShopType1 = {
                            ...floor,
                            goodsVOS: groupedGoods.shopType1,
                        }

                        const floorOther = {
                            ...floor,
                            goodsVOS: groupedGoods.other,
                        }

                        // 添加到结果数组
                        if (groupedGoods.shopType1.length > 0) {
                            acc.shopType1.push(floorShopType1)
                        }

                        if (groupedGoods.other.length > 0) {
                            acc.other.push(floorOther)
                        }

                        return acc
                    },
                    { shopType1: [], other: [] }
                )
                this.selfSupportArray = groupedFloorVOS.shopType1
                this.otherArray = groupedFloorVOS.other
                this.floorData = this.selfSupportArray
            })
        },
        updateFloorData () {
            if (this.ItemData.name === '平台自营') {
                this.floorData = this.selfSupportArray
            } else {
                this.floorData = this.otherArray
            }
        },
        autoScroll (stop) {
            if (!this.isLoggedIn) return
            let table = this.$refs.scrollTable
            let wrapper = table.$refs.bodyWrapper

            if (stop) {
                clearInterval(this.scrollTimer)
            } else {
                this.scrollTimer = setInterval(() => {
                    wrapper.scrollTop += this.scrollPixel
                    if (
                        wrapper.clientHeight + wrapper.scrollTop === wrapper.scrollHeight ||
            wrapper.scrollTop === 0
                    ) {
                        this.scrollPixel = -this.scrollPixel
                    }
                }, 150)
            }
        },
    },
    created () {
        this.getList()
        this.getFloor()
        this.getHistory()
        this.getIndexShopListM()
    },
    mounted () {
        this.autoScroll()
    },
    beforeDestroy () {
        this.autoScroll(true)
    },
}
</script>

<style scoped lang="scss">
@import '../../../../assets/css/floor.scss';
.userCenter {
  background-color: #fff;
  .right {
    font-size: 14px;
    position: absolute;
    right: 20px;
  }
  .date {
    margin-right: 30px;
    color: #666666;
  }
  .tit {
    color: #1b7cea;
  }
}
.main {
  width: 1326px;
  padding: 20px 0;
  .title .left {
    font-size: 22px;
  }
  .box {
    background-color: #fff;
    .list-item {
      height: 60px;
      border-bottom: 1px solid rgba(230, 230, 230, 1);
      & > div {
        height: 100%;
      }
      .date {
        width: 126px;
        padding: 20px 0 0 30px;
        font-size: 14px;
        color: rgba(153, 153, 153, 1);
      }
      .content {
        padding-top: 20px;
        p {
          font-size: 18px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          ////能够显示的行数，超出部分用...表示
          //-webkit-box-orient: vertical;
        }
      }
      h3 {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
        cursor: pointer;
        &:hover {
          color: rgba(33, 110, 198, 1);
        }
      }
      p {
        width: 1030px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        color: rgba(153, 153, 153, 1);
      }
    }
  }
}
</style>
