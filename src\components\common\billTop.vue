!<template>
      <div class="billTop">
        <div class="billTop-title">
            <span class="title">{{ topTitle }}</span>
            <i class="el-icon-circle-close" title="关闭页面" @click="cancel"></i>
        </div>
    </div>
</template>

<script>
const topTitleList = [
    '零星采购商品', '大宗临购商品'
]
export default {
    props: ['title'],
    data () {
        return {
            topTitle: ''
        }
    },
    mounted () {
        let topTitle = this.$route.params.topTitle
        if(topTitleList.includes(topTitle) && this.$route.params.row.viewType == 'add') {
            this.topTitle = '新增'
            window.document.title = '新增'
        }else if(this.title) {
            this.topTitle = this.title
        }else{
            this.topTitle = this.$route.meta.title
        }
    },
    methods: {
        cancel () {
            this.$emit('cancel')
        },
    },
}
</script>

<style>

</style>