<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn">
                            <el-select @change="stateTopOptionsClick" v-model="stateOptionTitle" placeholder="请选择状态">
                                <el-option
                                    v-for="item in stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="search_box">
                        <el-radio v-model="filterData.orderBy" :label="1">按创建时间排序</el-radio>
                        <el-radio v-model="filterData.orderBy" :label="2">按修改时间排序</el-radio>
                        <el-input type="text" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table">
                <el-table
                    class="table" :height="rightTableHeight" :data="tableData" border
                    @selection-change="selectionChangeHandle"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="订单号" width="240" prop="orderSn"></el-table-column>
                    <el-table-column label="退货编号" width="200" prop="shopName">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row.orderReturnId)">{{ scope.row.orderReturnNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" width="240" prop="untitled"></el-table-column>
                    <el-table-column label="状态" width="" prop="state">
                        <template v-slot="scope">
                            <el-tag v-if="showTableState(scope.row)">{{ tableStateTitle }}</el-tag>
                            <el-tag v-else type="danger">已关闭</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="退货来源" width="200" prop="returnMethod">
                        <template v-slot="scope">
                            <el-tag v-show="scope.row.isOut==1">PCWP退货</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="申请退货时间" width="160" prop="gmtCreate"/>
                    <el-table-column label="退货成功时间" width="160" prop="flishTime">
                        <template v-slot="scope">
                            <span
                                v-if="scope.row.state==3"
                            >{{ scope.row.flishTime }}</span>
                            <span
                                v-if="scope.row.state!=3"
                            >-</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="退款原因" width="300" prop="orderRemark"></el-table-column>-->
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                v-show="tableData || tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="状态：">
                            <el-select @change="stateOptionsClick" v-model="filterData.selectStateTitle" placeholder="请选择状态">
                                <el-option
                                    v-for="item in filterData.stateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateValue"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="实际金额以上：">
                            <el-input type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="实际金额以下：">
                            <el-input type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce, hideLoading, showLoading } from '@/utils/common'
import { mapActions, mapMutations, mapState } from 'vuex'
// import { orderReturnList } from '@/api/platform/order/refund'
import { shopMangeOrderReturnList } from '@/api/shopManage/refund/refundapply'

export default {
    components: {
        Pagination
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        ...mapState('equip', ['equipData']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            // 状态选择查询
            selectOptionValue: null, // 选中的值
            stateOptionTitle: '', // 选中的状态标题
            stateOptions: [
                {
                    value: null,
                    label: '全部'
                },
                {
                    value: 4,
                    label: '审核失败'
                }, {
                    value: 2,
                    label: '退货中'
                }, {
                    value: 3,
                    label: '退货成功'
                }],
            // 表格数据
            tableStateTitle: null, // 表格的状态
            dataListSelections: [], //选中的数据
            className: null,
            classId: null, // 分类id
            keywords: null, // 关键字
            alertName: '商品信息',
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 20,
                pageSize: 20,
                currentPage: 1,
                totalPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
                selectStateTitle: null, // 选中的标题
                selectSateValue: null,  // 选中的值
                stateOptions: [
                    {
                        value: null,
                        label: '全部'
                    },
                    // {
                    //       value: 1,
                    //       label: '已申请'
                    //   },
                    {
                        value: 2,
                        label: '退货中'
                    },
                    {
                        value: 4,
                        label: '审核失败'
                    },
                    {
                        value: 3,
                        label: '退货成功'
                    }
                ],
                orderBy: 1,
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.getTableData()
    },
    methods: {
        // 显示状态
        showTableState (row) {
            let stateValue = row.state
            if (stateValue === 6) {
                return false
            }
            for (let i = 0; i < this.stateOptions.length; i++) {
                if (stateValue === this.stateOptions[i].value) {
                    this.tableStateTitle = this.stateOptions[i].label
                    return true
                }
            }
        },
        // 选中状态进行查询
        stateTopOptionsClick (value) {
            this.selectOptionValue = value
            this.getTableData()
        },
        // 详情
        handleView (row) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/supplierSys/returnGoods/bulkRecordDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'bulkRecordDetail',
                params: {
                    row: row
                }
            })
        },
        resetSearchConditions () {
            this.filterData.belowPrice = null // 以下价格
            this.filterData.abovePrice = null // 以上价格
            this.filterData.dateValue = [] // 开始时间和结束时间
            this.filterData.selectStateTitle = null// 选中的标题
            this.filterData.selectSateValue = null // 选中的值
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.selectOptionValue = null
            this.stateOptionTitle = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 高级搜索状态选中
        stateOptionsClick (value) {
            this.filterData.selectSateValue = value
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        ...mapActions('equip', ['setUnitMeasur']),
        ...mapMutations(['setSelectedInfo']),
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
                sourceType: 1
            }
            if (this.selectOptionValue != null) {
                params.state = this.selectOptionValue
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            if (this.filterData.selectSateValue != null) {
                params.state = this.filterData.selectSateValue
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.orderBy != null) {
                params.orderBy = this.filterData.orderBy
            }
            showLoading()
            shopMangeOrderReturnList(params).then(res => {
                this.tableData = res.list || []
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.paginationInfo.totalPage = res.totalPage
                hideLoading()
            })
        },
        // 查询
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-table {
    min-height: auto;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}
</style>
