let mutations = {
    setUserInfo (state, data) {
        state.userInfo = data
    },
    setIsNoticeFlag (state, data) {
        state.isNoticeFlag = data
    },
    setUserImproveFlag (state, data) {
        state.userImproveFlag = data
    },
    setNoticeList (state, data) {
        state.noticeList = data
    },
    setRouterObj (state, data) {
        state.routerObj = data
    },
    setSearchData (state, data) {
        state.searchForm = data
    },
    setPopStatus (state, data) {
        state.popStatus = data
    },
    setPopConfirm (state, data) {
        state.popConfirm = data
    },
    setComPopConfirm (state, data) {
        state.comPopConfirm = data
    },
    setSelectedInfo (state, data) {
        state.selectedInfo = data
    },
    setAuditState (state, data) {
        state.auditState = data
    },
    setAuditParams (state, data) {
        state.auditParams = data
    },
    setProfile (state, data) {
        state.userInfo.profile = data
    },
    setBaseCyByOrgId (state, data) {
        state.userInfo.baseCyByOrgId = data
    },
    setHistoryTableData (state, data) {
        state.historyTableData = data
    },
    setEvaluation (state, data) {
        state.evaluationBasis = data
    },
    setAnnualEvaluation (state, data) {
        state.annualEvaluationData = data
    },
    setTableHeight (state, data) {
        state.tableHeight = data
    },
    //更改菜单id
    setMenuid: (state, data) => {
        state.menuid = data
    },
    setDialogVisible: (state, data) => {
        state.dialogVisible = data
    },
    setDialogParams: (state, data) => {
        state.setDialogParams = data
    },
    setDialogData: (state, data) => {
        state.getDialogData = data
    },
    setSteps: (state, data) => {
        state.steps = data
    },
    setNumUnitOptions: (state, data) => {
        state.selectOptions.numUnitOptions = data
    },
    // 物资城市
    setMaterialCity: (state, data) => {
        state.materialCity = data
    },
    // 计量单位
    setMaterialUnit: (state, data) => {
        state.materialUnit = data
    },
    setAuthorities: (state, data) => {
        state.authorities = data
    },
    setCToken: (state, data) => {
        state.cToken = data
    }
}
export default mutations
