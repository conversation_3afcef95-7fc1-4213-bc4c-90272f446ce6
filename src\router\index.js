// import Vue from 'vue'
// import Router from 'vue-router'
import store from '@/store'
// import { hasPermiFun } from '@/utils/instruct/permission'
// import shop from './shop' // 店铺管理后台路由
import frontStage from './frontStage' // 前台路由
import platform from './platform' // 平台后台路由
import inspection from './inspection' // 纪检后台路由
import performance from './performance' // 履约后台路由
import cloudCenter from './cloudCenter'
import supplier from './supplier'
import { hideLoading } from '@/utils/common'
import { tokenLogin } from '@/api/frontStage/login'

Vue.use(VueRouter)
// TODO: 发布生产版时设为false
Vue.config.devtools = true

// 以下代码解决路由地址重复的报错问题(一劳永逸)
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
    return originalPush.call(this, location).catch(err => err)
}

const routerInstance = new VueRouter({
    mode: 'history',
    routes: [
        {
            path: '/',
            component: () => import(/* webpackChunkName: "index" */ '@/pages/frontStage/index'),
            redirect: '/index',
            meta: {
                title: '物资采购平台-首页'
            }
        },
        /*{
            path: '/login/:user?',
            // 开启props 会把URL中的参数传递给组件
            // 在组件中通过props来接收
            props: true,
            name: 'login',
            component: () => import(/!* webpackChunkName: "index" *!/ '@/pages/login/login'),
            meta: {
                title: '登录'
            }
        },*/
        // 页面
        // ...shop,
        ...platform,
        ...inspection,
        ...frontStage,
        ...supplier,
        ...performance,
        ...cloudCenter
    ]
})
/*const getUser = async token => {
    let dispatchRes
    try {
        // 客户端
        if (CefSharp) {
            dispatchRes = await store.dispatch('volideToken', token)
        }
    } catch (e) {
        // web端
        // dispatchRes = await store.dispatch('getToken')
        // 登录过期后再跳转登录界面进行登录
        dispatchRes = await store.dispatch('volideToken', token)
    }
    return dispatchRes
}*/
let  whiteList = ['TTLoginIndex', '/login', '/index', '/mallIndex', 'mFront', '/mFront/register', 'cLogin', '/resetAccount', '/resetPass']

function validatePermissions (key) {
    let info = store.state.userInfo
    let validInfo = Object.keys(info).length === 0 || !info
    let token = localStorage.getItem('token')
    if (!validInfo && !token) return '/login'
    if (key === 'isShopManage') {
        return info.shopId ? 'next' : '/'
    }
    let hasPermission = info[key] !== null && info[key] === 1
    if (!hasPermission) return '/'
    return 'next'
}

routerInstance.beforeEach(async (to, from, next) => {
    if(to.path.includes('cloudCenter')) {
        let cToken = store.state.cToken
        if(cToken) return next()
        return next('/cLogin')
    }
    if (to.meta && to.meta.title) {
        // 判断 meta.title 是字符串还是函数
        if (typeof to.meta.title === 'function') {
            // 如果是函数，则调用函数并传入to路由对象，获取动态标题
            window.document.title = to.meta.title(to)
        } else {
            // 如果是字符串，则直接使用
            window.document.title = to.meta.title
        }
    } else {
        // 如果没有定义title，则使用默认标题
        window.document.title = '物资采购平台' // 您的默认平台标题
    }
    let { token, orgid, searchOrgId, searchOrgName } = to.query
    if (whiteList.includes(to.fullPath) || whiteList.includes(to.path.split('/')[1])) {
        hideLoading()
        return next()
    }
    // 以下判断用户平台权限 TODO 有时间可以进行优化
    let target
    let hasPermiType = process.env.VUE_APP_PERMISSION_TYPE
    // eslint-disable-next-line no-debugger
    // 判断用户是否有改功能权限，没有则跳转到index首页
    if(hasPermiType && hasPermiType == 'NEW') {
        // if (to.path.includes('platform')) {
        //     target = hasPermiFun({ role: 'isPlatformAdmin' }) ? 'next' : '/'
        // } else if (to.path.includes('isShopManage')) {
        //     target = hasPermiFun({ role: 'isShopManage' }) ? 'next' : '/'
        //     // target = store.state.userInfo && store.state.userInfo.shopId ? 'next' : '/'
        // } else if (to.path.includes('supplier')) {
        //     target = hasPermiFun({ role: 'isSupplier' }) ? 'next' : '/'
        // } else if (to.path.includes('inspection')) {
        //     target = hasPermiFun({ role: 'isCheck' }) ? 'next' : '/'
        // }
        target = 'next'
    } else {
        if (to.path.includes('platform')) {
            target = validatePermissions('isPlatformAdmin')
        } else if (to.path.includes('isShopManage')) {
            target = validatePermissions('isShopManage')
        } else if (to.path.includes('supplier')) {
            target = validatePermissions('isSupplier')
        } else if (to.path.includes('inspection')) {
            target = validatePermissions('isCheck')
        }
    }
    if (target !== 'next') next(target)
    if (token && !localStorage.getItem('token') && to.path !== '/index') {
        let res = tokenLogin({ token, mallType: 0 })
        if (!res.token) return next('/login')
        store.dispatch('setToken', res)
    }
    if (!token) token = localStorage.getItem('token')
    if (!token) {
        next('/login')
        hideLoading()
    }
    next()
    if (orgid) localStorage.setItem('orgId', orgid)
    const keys = Object.keys(to.query)
    if (keys.includes('searchOrgId')) {
        localStorage.setItem('searchOrgId', searchOrgId || '')
    }
    if (keys.includes('searchOrgName')) {
        localStorage.setItem('searchOrgName', searchOrgId ? searchOrgName : '')
    }
    // 获取token、userName、userNumber存入localStorage
    // await getUser(token)
})

// eslint-disable-next-line no-unused-vars
routerInstance.afterEach((to, from, next) => {
    window.scrollTo(0, 0) // 切换路由滚动条恢复到顶部
})

export default routerInstance
