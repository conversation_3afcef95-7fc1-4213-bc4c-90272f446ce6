<template>
    <div class="e-form">
        <BillTop @cancel="handleClose"/>
        <div class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="楼层信息" name="floorInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="商品信息" name="goodsInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <!-- 楼层信息 -->
                    <div id="floorInfo" class="con" v-loading="floorLoading">
                        <div class="tabs-title" id="columnInfo">楼层信息</div>
                        <div style="width: 100%" class="form">
                            <el-form :model="floorData" label-width="195px" :rules="floorRules" ref="rulesBase" class="demo-ruleForm">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item required class="uploader" label="楼层主图（推荐：208x170）：" prop="imgUrl">
                                            <el-upload
                                                :class="mainImgLen === 1 ? 'hide_box_admin' : ''"
                                                action="fakeaction"
                                                :show-file-list="false"
                                                :before-upload="checkFileSize"
                                                :on-remove="removeMainImg"
                                                :on-change="(file) => { setCropperImg(file, 0, [208, 170]) }"
                                            >
                                                <img v-if="floorImg" :src="floorImg" alt="">
                                                <i v-else class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item required class="uploader" label="楼层背景（推荐：250x620）：" prop="backgroundUrl">
<!--                                            :class="backImgLen === 1 ? 'hide_box_admin' : ''"-->
                                            <el-upload
                                                action="fakeaction"
                                                :show-file-list="false"
                                                :before-upload="checkFileSize"
                                                :on-remove="removeBackImg"
                                                :on-change="(file) => setCropperImg(file,1, [250, 620])"
                                            >
                                                <img v-if="backgroundImg" :src="backgroundImg" alt="">
                                                <i v-else class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="20">
                                        <el-form-item label="楼层分类：" prop="floorName">
                                            <!--<el-input clearable v-model="floorData.floorName"></el-input>-->
                                            <category-cascader
                                                :classPath.sync="floorData.classPaths"
                                                :classId.sync="floorData.classId"
                                                :catelogPath="floorData.classPaths"
                                                @change="handleCategoryChange"
                                            ></category-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <!--<el-col :span="12">
                                        <el-form-item label="小标题：" prop="floorNameText">
                                            <el-input clearable v-model="floorData.floorNameText"></el-input>
                                        </el-form-item>
                                    </el-col>-->
                                </el-row>
                                <!--<el-row>-->
                                <!--    <el-col :span="24">-->
                                <!--        <el-form-item label="主图链接地址：" prop="state">-->
                                <!--            <a :href="floorData.mainImgUrl" style="color: blue">{{floorData.mainImgUrl}}</a>-->
                                <!--        </el-form-item>-->
                                <!--    </el-col>-->
                                <!--</el-row>-->
                                <el-row>
<!--                                    <el-col :span="12">-->
<!--                                        <el-form-item label="创建商城：" prop="mallType">-->
<!--                                            <span v-if="floorData.mallType == '1'">装备商城</span>-->
<!--                                            <span v-else-if="floorData.mallType == '0'">慧采商城</span>-->
<!--                                        </el-form-item>-->
<!--                                    </el-col>-->
                                    <el-col :span="12">
                                        <el-form-item label="楼层状态：" prop="state">
                                            <el-tag v-if="floorData.state == 1" type="success">显示</el-tag>
                                            <el-tag v-if="floorData.state == 0" type="danger">不显示</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-form-item label="备注信息：" prop="remarks">
                                        <el-input clearable type="textarea" v-model="floorData.remarks"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-button style="margin-left: 90%" type="primary" @click="floorUpdate" class="btn-blue">保存</el-button>
                            </el-form>
                        </div>
                    </div>
                    <!--楼层信息-->
                    <div id="goodsInfo" class="con">
                        <div class="tabs-title" id="goodInfo">商品信息</div>
                        <div class="e-table" style="background-color: #ffffff">
                            <div class="top">
                                <div class="left" style="height: 50px;margin-left: 20px">
                                    <div>
                                        <el-button size="small" type="primary" class="btn-greenYellow" plain @click="addData">导入
                                        </el-button>
                                        <el-button type="primary" @click="updateStateBatch(1)" class="btn-greenYellow">批量发布</el-button>
                                        <el-button type="primary" @click="updateStateBatch(0)" class="btn-delete">批量不发布</el-button>
                                        <el-button type="primary" class="btn-greenYellow" @click="changeSortValue">批量修改排序值</el-button>
                                        <el-button size="small" type="danger" class="btn-delete" plain @click="deleteData">批量删除
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="errorMsg" v-if="false">
                                    <span></span>
                                </div>
                                <el-table @row-click="handleCurrentInventoryClick" ref="eltableCurrentRow" v-loading="isLoading" border height="800px" style="width: 100%" :data="goodsData" class="table"
                                    @selection-change="handleSelectionChange">
                                    <el-table-column type="selection" width="40"></el-table-column>
                                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                                    <el-table-column label="商品编码" width="220">
                                        <template v-slot="scope">
                                            {{ scope.row.vo.serialNum }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="商品名称" width="200">
                                        <template v-slot="scope">
                                            {{ scope.row.vo.productName }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="商品小图" width="190">
                                        <template v-slot="scope">
                                            <el-image style="width: 160px; height: 90px" :src="imgUrlPrefixAdd + scope.row.vo.productMinImg"></el-image>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="规格" width="">
                                        <template v-slot="scope">
                                            {{ scope.row.vo.skuName }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="销售价格" width="">
                                        <template v-slot="scope">
                                            {{ scope.row.vo.sellPrice }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="排序值" width="">
                                        <template v-slot="scope">
                                            <el-input type="number" clearable v-model="scope.row.sort" @change="getChangedRow(scope.row)"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="状态" width="">
                                        <template v-slot="scope">
                                            <el-tag v-if="scope.row.state == 1" type="success">发布</el-tag>
                                            <el-tag v-if="scope.row.state == 0" type="danger">未发布</el-tag>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <!-- 分页器 -->
                                <ComPagination :total="pages.totalCount" :limit="50" :pageSize.sync="pages.pageSize" :currentPage.sync="pages.currPage"
                                               @currentChange="currentChange" @sizeChange="sizeChange" />
                            </div>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons" v-if="!isView">
            <el-button @click="handleClose">返回</el-button>
        </div>
        <select-product ref="selectProductRef" @batchCreate="createGoodBatch" :showDialog="showSelectProduct"></select-product>
        <Cropper ref="cropperImage" @handleUploadSuccess="handleUploadSuccess"></Cropper>
    </div>
</template>
<script>
import ComPagination from '@/components/pagination/pagination'
import Cropper from '@/components/cropper.vue'
import SelectProduct from '@/components/product/selectProduct'
import CategoryCascader from '@/components/category-cascader'
import '@/utils/jquery.scrollTo.min'
import $ from 'jquery'
import { hideLoading, showLoading, throttle } from '@/utils/common'
import { batchDeleteGoods, getGoodsList, batchUpdate, updateNotPublish, updateByPublish } from '@/api/platform/floor/goods'
import { editFloor, floorGoodsCreate } from '@/api/platform/floor/floor'
import { uploadFile } from '@/api/platform/common/file'

export default {
    components: {
        SelectProduct, ComPagination, CategoryCascader, Cropper
    },
    data () {
        return {
            floorLoading: false,
            floorRules: {
                imgUrl: [
                    { required: true, message: '请上传主图', trigger: 'blur' },
                ],
                backgroundUrl: { required: true, message: '请上传楼层背景图', trigger: 'blur' },
                floorName: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                ],
                floorNameText: [
                    { required: true, message: '请输入小标题', trigger: 'blur' },
                ],
            },
            backImg: [],
            backgroundImg: '',
            floorImg: '',
            mainImgLen: 0,
            backImgLen: 0,
            changedRow: [],
            showSelectProduct: false,
            isLoading: false,
            pages: {
                totalCount: null,
                pageSize: 20,
                currPage: 1,
            },
            uploadImgSize: 10, // 上传文件大小
            dialogVisible: false,
            topHeight: 120,
            //基本信息表单数据
            floorData: {},
            goodsData: [],
            // 表格数据
            // 高级查询数据对象
            filterData: {
                floorId: null,
                orderBy: 1,
            },
            //选中数据
            selectedRows: [],
            tabsName: 'columnInfo',
            screenWidth: 0,
            screenHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            disabled: false, //是否禁止
            auditParams: {
                'billId': this.$route.query.billid,
                'billType': 7024
            },
            isView: false, //是否隐藏底部按钮
            uploadType: null,
        }
    },
    async mounted () {

        // this.goodsData.forEach(item => {
        //     if(item.gmtRelease !== null && item.gmtRelease.length === 19) {
        //         item.gmtRelease = item.gmtRelease.slice(0, 10)
        //     }
        //     if(item.gmtModified !== null && item.gmtModified.length === 19) {
        //         item.gmtModified = item.gmtModified.slice(0, 10)
        //     }
        // })
        // 保存所有tabName
        let arr = ['floorInfo', 'goodsInfo']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    created () {
        if(!this.$route.params.row) this.handleClose()
        //接收上一个页面传递的参数
        this.floorData = this.$route.params.row
        this.filterData.floorId = this.floorData.floorId
        this.getGoodPageList()
        this.setImg()
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        }
    },
    computed: {
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        }
    },
    methods: {
        setCropperImg (file, type, [w, h]) {
            this.uploadType = type
            this.$refs.cropperImage.handleOpen(file, { autoCropWidth: w, autoCropHeight: h }, { type: 'fixed', sizeWidth: w, sizeHeight: h })
        },
        handleUploadSuccess (file) {
            let form = this.formatFile(file)
            this.floorLoading = true
            uploadFile(form).then(res => {
                if(this.uploadType === 0) {
                    this.floorData.imgUrl = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                    this.floorImg = this.imgUrlPrefixAdd + this.floorData.imgUrl
                }else if(this.uploadType === 1) {
                    this.floorData.backgroundUrl = res[0].objectPath.replace(this.imgUrlPrefixDelete, '')
                    this.backgroundImg = this.imgUrlPrefixAdd + this.floorData.backgroundUrl
                }
                this.$message({ message: '上传成功', type: 'success' })
            }).finally(() => {
                this.floorLoading = false
            })
        },
        handleCategoryChange (value) {
            let name = value.map(item => item.className)
            this.floorData.className = name.join(' > ')
            this.floorData.floorName = name[0]
            this.floorData.floorNameText = name.reverse()[0]
            this.floorData.classId = value.reverse()[0].classId
        },
        setImg () {
            // "http://192.168.100.100:9000/mall/material/SRBC/20221212/b96a0422dd4d6794f8bf915cdcc0261.png"
            if(this.floorData.backgroundUrl) {
                // let backImgName = this.floorData.backgroundUrl.split('/')
                this.backgroundImg = this.imgUrlPrefixAdd + this.floorData.backgroundUrl
                this.backImgLen = 1
            }
            if(this.floorData.imgUrl) {
                // let mainImgName = this.floorData.imgUrl.split('/')
                this.floorImg = this.imgUrlPrefixAdd + this.floorData.imgUrl
                this.mainImgLen = 1
            }
        },
        floorUpdate () {
            this.$refs.rulesBase.validate(async valid => {
                if (!valid) return
                this.floorLoading = true
                let res = await editFloor(this.floorData)
                this.message(res)
                this.floorLoading = false
            })
        },
        removeMainImg () {
            this.floorData.imgUrl = null
            this.mainImgLen = 0
        },
        removeBackImg () {
            this.floorData.backgroundUrl = null
            this.backImgLen = 0
        },
        formatFile (params) {
            const form = new FormData()
            form.append('files', params)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 判断上传的图片大小
        checkFileSize (file) {
            if(file.size / 1024 / 1024 > this.uploadImgSize) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
                return false
            }
            return true
        },
        // 批量发布
        updateStateBatch (num) {
            if (this.selectedRows.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let ids = this.selectedRows.map(item => item.floorGoodsId)
            if (num === 0) {
                this.clientPop('info', '您确定要取消发布这些数据吗！', async () => {
                    let res = await updateNotPublish(ids)
                    this.getGoodPageList()
                    this.selectedRows = []
                    this.message(res)
                })
            } else {
                this.clientPop('info', '您确定要发布这些数据吗！', async () => {
                    let res = await updateByPublish(ids)
                    this.getGoodPageList()
                    this.selectedRows = []
                    this.message(res)
                })
            }
        },
        // 批量修改排序值
        changeSortValue () {
            if (this.changedRow.length === 0) {
                return this.$message('未修改列表当中的排序值！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                batchUpdate(this.changedRow).then(res => {
                    this.message(res)
                    this.getGoodPageList()
                    this.changedRow = []
                })
            })
        },
        // 获取排序值
        getChangedRow (row) {
            let sort = row.sort ? parseInt(row.sort) : 0
            if (this.changedRow.length === 0) {
                this.changedRow.push({ floorGoodsId: row.floorGoodsId, sort, })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.floorGoodsId === row.floorGoodsId) {
                    t.sort = sort
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({ floorGoodsId: row.floorGoodsId, sort, })
            }
            flag = true
        },
        // 获取商品列表
        getGoodPageList () {
            let params = {
                page: this.pages.currentPage,
                limit: this.pages.pageSize,
                floorId: this.filterData.floorId
            }
            getGoodsList(params).then(res => {
                res.list.forEach(item => {
                    item.goodsPictureUrl = this.imgUrlPrefixAdd + item.goodsPictureUrl
                })
                this.goodsData = res.list
                this.pages = res
            })
        },
        // 批量创建
        createGoodBatch (products) {
            let params = []
            products.forEach(t => {
                let formData = {}
                formData.floorId = this.floorData.floorId
                formData.goodsId = t.productId
                formData.goodsPictureUrl = t.productMinImg
                params.push(formData)
            })
            floorGoodsCreate(params).then(res => {
                this.message(res)
                this.showSelectProduct = false
                this.getGoodPageList()
            })
        },
        message (res) {
            this.$message({
                message: res.message,
                type: res.code === 200 ? 'success' : 'error'
            })
        },
        // 关闭商品导入弹窗
        closeDialog () {
            this.showSelectProduct = false
        },
        addData () {
            // this.showSelectProduct = true
            this.$refs.selectProductRef.init(this.floorData.classId)
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag)
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 获取列表数据
        getTableData () {
            this.isLoading = true
            getGoodsList({ ...this.filterData }).then(res => {
                if (res.list) {
                    this.goodsData = res.list
                    // for(var i = 0 ; i < this.tableData.length ; i++) {
                    //     if (this.tableData[i].gmtRelease != null) {
                    //         this.tableData[i].gmtRelease = this.tableData[i].gmtRelease.slice(0, 10)
                    //     }
                    //     if(this.tableData[i].gmtCreate != null) {
                    //         this.tableData[i].gmtCreate = this.tableData[i].gmtCreate.slice(0, 10)
                    //     }
                    //     if(this.tableData[i].gmtModified != null) {
                    //         this.tableData[i].gmtModified = this.tableData[i].gmtModified.slice(0, 10)
                    //     }
                    // }
                } else {
                    this.clientPop('warn', res.message, () => { })
                }
                this.isLoading = false
                this.pages = res
            }).catch(() => {
                this.formLoading = false
            })
            this.isLoading = false
            this.viewList = true
        },
        cellClsNm ({ column }) {
            if (column.label === '') {
                return 'fixed-left'
            }
        },
        deleteData () {
            if (!this.selectedRows[0]) {
                return this.$message('请选择要删除的信息！')
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => item.floorGoodsId)
                batchDeleteGoods(arr).then(res => {
                    if (res.message === '操作成功') {
                        this.clientPop('suc', '删除成功', () => {
                            this.getTableData()
                        })
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        currentChange (page) {
            this.pages.currentPage = page
            this.getGoodPageList()
        },
        sizeChange (size) {
            this.pages.pageSize = size
            this.getGoodPageList()
        },
        //取消
        handleClose () {
            this.$router.replace('/platform/floor/floorManager')
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            // console.log($('.tabs-content'))
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        showBusinessLicenseHidden (info) {
            if (info == null) {
                this.businessLicenseHidden = false
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

/deep/.el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
        border-color: #409EFF;
    }
    img, i {
        width: 178px;
        height: 178px;
    }
    i {
        font-size: 28px;
        color: #8c939d;
        line-height: 178px;
        text-align: center;
    }
    img {
        object-fit: cover;
        display: block;
    }
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .hide_box_admin .el-upload--picture-card {
    display: none;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
/deep/ input[type="number"] {
    -moz-appearance: textfield !important;
}
</style>
