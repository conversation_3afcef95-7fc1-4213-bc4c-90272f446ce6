<template>
  <div id="auditRecords" class="con">
    <div class="tabs-title" id="auditRecords">审核历史</div>
    <div class="e-table"  style="background-color: #fff">
      <el-table
          border
          style="width: 100%"
          :data="auditRecords"
          :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
          :row-style="{ fontSize: '14px', height: '48px' }"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column prop="auditType" label="审核类型" width="160">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.auditType == 0">提交审核</el-tag>
            <el-tag v-if="scope.row.auditType == 1">审核通过</el-tag>
            <el-tag v-if="scope.row.auditType == 2">审核失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditor" label="审核人" width="200">
        </el-table-column>
        <el-table-column prop="auditTime" label="审核时间" width="160">
        </el-table-column>
        <el-table-column prop="auditRemark" label="审核意见" width="">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
    name: 'auditFrom',
    props: {
        auditRecords: []
    },
    data () {
        return {

        }
    }
}
</script>
